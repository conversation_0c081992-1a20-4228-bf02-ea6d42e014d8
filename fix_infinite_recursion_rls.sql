-- Fix infinite recursion in RLS policies
-- Run this in Supabase SQL Editor

-- First, create a function to check if a user is an admin
-- This function will use SECURITY DEFINER to bypass RLS
CREATE OR REPLACE FUNCTION is_admin(user_uuid UUID)
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT EXISTS (
    SELECT 1 FROM admin_users 
    WHERE user_id = user_uuid
  );
$$;

-- Drop all existing problematic policies
DROP POLICY IF EXISTS "Ad<PERSON> can view all admin_users" ON admin_users;
DROP POLICY IF EXISTS "Ad<PERSON> can insert admin_users" ON admin_users;
DROP POLICY IF EXISTS "Ad<PERSON> can view all counselor profiles" ON counselor_profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all assignments" ON counselor_couple_assignments;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all sessions" ON counseling_sessions;
DROP POLICY IF EXISTS "Allow admin write access" ON assessment_questions;

-- Create new policies for admin_users that don't cause recursion
-- Allow users to view admin_users only if they are already confirmed as admin
-- This breaks the circular dependency by using the function
CREATE POLICY "Admin users can view admin table"
  ON admin_users FOR SELECT
  USING (is_admin(auth.uid()));

CREATE POLICY "Admin users can insert into admin table"
  ON admin_users FOR INSERT
  WITH CHECK (is_admin(auth.uid()));

-- Create new policies for counselor_profiles
-- Counselors can view their own profile (no admin check needed)
CREATE POLICY "Counselors can view own profile"
  ON counselor_profiles FOR SELECT
  USING (auth.uid() = user_id);

-- Admins can view all counselor profiles (using the function)
CREATE POLICY "Admins can view all counselor profiles"
  ON counselor_profiles FOR SELECT
  USING (is_admin(auth.uid()));

-- Counselors can update their own profile
CREATE POLICY "Counselors can update own profile"
  ON counselor_profiles FOR UPDATE
  USING (auth.uid() = user_id);

-- Admins can update any counselor profile
CREATE POLICY "Admins can update counselor profiles"
  ON counselor_profiles FOR UPDATE
  USING (is_admin(auth.uid()));

-- Fix counselor_couple_assignments policies
CREATE POLICY "Counselors can view own assignments"
  ON counselor_couple_assignments FOR SELECT
  USING (auth.uid() = counselor_id);

CREATE POLICY "Admins can manage all assignments"
  ON counselor_couple_assignments FOR ALL
  USING (is_admin(auth.uid()));

-- Fix counseling_sessions policies
CREATE POLICY "Counselors can view own sessions"
  ON counseling_sessions FOR SELECT
  USING (auth.uid() = counselor_id);

CREATE POLICY "Counselors can update own sessions"
  ON counseling_sessions FOR UPDATE
  USING (auth.uid() = counselor_id);

CREATE POLICY "Admins can manage all sessions"
  ON counseling_sessions FOR ALL
  USING (is_admin(auth.uid()));

-- Fix assessment_questions policies
CREATE POLICY "Allow public read access to assessment questions"
  ON assessment_questions FOR SELECT
  USING (true);

CREATE POLICY "Allow admin write access to assessment questions"
  ON assessment_questions FOR ALL
  USING (is_admin(auth.uid()));

-- Grant execute permission on the function to authenticated users
GRANT EXECUTE ON FUNCTION is_admin(UUID) TO authenticated;
