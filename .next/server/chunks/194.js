"use strict";exports.id=194,exports.ids=[194],exports.modules={82561:(e,t,r)=>{r.d(t,{M:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},70545:(e,t,r)=>{r.d(t,{B:()=>a});var n=r(17577),o=r(93095),u=r(48051),i=r(34214),l=r(10326);function a(e){let t=e+"CollectionProvider",[r,a]=(0,o.b)(t),[s,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:r}=e,o=n.useRef(null),u=n.useRef(new Map).current;return(0,l.jsx)(s,{scope:t,itemMap:u,collectionRef:o,children:r})};f.displayName=t;let d=e+"CollectionSlot",m=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=c(d,r),a=(0,u.e)(t,o.collectionRef);return(0,l.jsx)(i.g7,{ref:a,children:n})});m.displayName=d;let p=e+"CollectionItemSlot",v="data-radix-collection-item",w=n.forwardRef((e,t)=>{let{scope:r,children:o,...a}=e,s=n.useRef(null),f=(0,u.e)(t,s),d=c(p,r);return n.useEffect(()=>(d.itemMap.set(s,{ref:s,...a}),()=>void d.itemMap.delete(s))),(0,l.jsx)(i.g7,{[v]:"",ref:f,children:o})});return w.displayName=p,[{Provider:f,Slot:m,ItemSlot:w},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},a]}},93095:(e,t,r)=>{r.d(t,{b:()=>u});var n=r(17577),o=r(10326);function u(e,t=[]){let r=[],u=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return u.scopeName=e,[function(t,u){let i=n.createContext(u),l=r.length;r=[...r,u];let a=t=>{let{scope:r,children:u,...a}=t,s=r?.[e]?.[l]||i,c=n.useMemo(()=>a,Object.values(a));return(0,o.jsx)(s.Provider,{value:c,children:u})};return a.displayName=t+"Provider",[a,function(r,o){let a=o?.[e]?.[l]||i,s=n.useContext(a);if(s)return s;if(void 0!==u)return u;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(u,...t)]}},17124:(e,t,r)=>{r.d(t,{gm:()=>u});var n=r(17577);r(10326);var o=n.createContext(void 0);function u(e){let t=n.useContext(o);return e||t||"ltr"}},88957:(e,t,r)=>{r.d(t,{M:()=>a});var n,o=r(17577),u=r(65819),i=(n||(n=r.t(o,2)))["useId".toString()]||(()=>void 0),l=0;function a(e){let[t,r]=o.useState(i());return(0,u.b)(()=>{e||r(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},9815:(e,t,r)=>{r.d(t,{z:()=>i});var n=r(17577),o=r(48051),u=r(65819),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[o,i]=n.useState(),a=n.useRef({}),s=n.useRef(e),c=n.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=l(a.current);c.current="mounted"===f?e:"none"},[f]),(0,u.b)(()=>{let t=a.current,r=s.current;if(r!==e){let n=c.current,o=l(t);e?d("MOUNT"):"none"===o||t?.display==="none"?d("UNMOUNT"):r&&n!==o?d("ANIMATION_OUT"):d("UNMOUNT"),s.current=e}},[e,d]),(0,u.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,r=r=>{let n=l(a.current).includes(r.animationName);if(r.target===o&&n&&(d("ANIMATION_END"),!s.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(c.current=l(a.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:n.useCallback(e=>{e&&(a.current=getComputedStyle(e)),i(e)},[])}}(t),a="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),s=(0,o.e)(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||i.isPresent?n.cloneElement(a,{ref:s}):null};function l(e){return e?.animationName||"none"}i.displayName="Presence"},45226:(e,t,r)=>{r.d(t,{WV:()=>l,jH:()=>a});var n=r(17577),o=r(60962),u=r(34214),i=r(10326),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...o}=e,l=n?u.g7:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function a(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},15594:(e,t,r)=>{r.d(t,{Pc:()=>b,ck:()=>O,fC:()=>C});var n=r(17577),o=r(82561),u=r(70545),i=r(48051),l=r(93095),a=r(88957),s=r(45226),c=r(55049),f=r(52067),d=r(17124),m=r(10326),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[g,y,M]=(0,u.B)(w),[N,b]=(0,l.b)(w,[M]),[R,x]=N(w),T=n.forwardRef((e,t)=>(0,m.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(E,{...e,ref:t})})}));T.displayName=w;var E=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:u,loop:l=!1,dir:a,currentTabStopId:w,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:M,onEntryFocus:N,preventScrollOnEntryFocus:b=!1,...x}=e,T=n.useRef(null),E=(0,i.e)(t,T),h=(0,d.gm)(a),[A=null,I]=(0,f.T)({prop:w,defaultProp:g,onChange:M}),[C,O]=n.useState(!1),F=(0,c.W)(N),P=y(r),D=n.useRef(!1),[j,_]=n.useState(0);return n.useEffect(()=>{let e=T.current;if(e)return e.addEventListener(p,F),()=>e.removeEventListener(p,F)},[F]),(0,m.jsx)(R,{scope:r,orientation:u,dir:h,loop:l,currentTabStopId:A,onItemFocus:n.useCallback(e=>I(e),[I]),onItemShiftTab:n.useCallback(()=>O(!0),[]),onFocusableItemAdd:n.useCallback(()=>_(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>_(e=>e-1),[]),children:(0,m.jsx)(s.WV.div,{tabIndex:C||0===j?-1:0,"data-orientation":u,...x,ref:E,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{D.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!D.current;if(e.target===e.currentTarget&&t&&!C){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=P().filter(e=>e.focusable);S([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),b)}}D.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>O(!1))})})}),h="RovingFocusGroupItem",A=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:u=!0,active:i=!1,tabStopId:l,...c}=e,f=(0,a.M)(),d=l||f,p=x(h,r),v=p.currentTabStopId===d,w=y(r),{onFocusableItemAdd:M,onFocusableItemRemove:N}=p;return n.useEffect(()=>{if(u)return M(),()=>N()},[u,M,N]),(0,m.jsx)(g.ItemSlot,{scope:r,id:d,focusable:u,active:i,children:(0,m.jsx)(s.WV.span,{tabIndex:v?0:-1,"data-orientation":p.orientation,...c,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{u?p.onItemFocus(d):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>p.onItemFocus(d)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return I[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>S(r))}})})})});A.displayName=h;var I={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function S(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var C=T,O=A},55049:(e,t,r)=>{r.d(t,{W:()=>o});var n=r(17577);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},52067:(e,t,r)=>{r.d(t,{T:()=>u});var n=r(17577),o=r(55049);function u({prop:e,defaultProp:t,onChange:r=()=>{}}){let[u,i]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[u]=r,i=n.useRef(u),l=(0,o.W)(t);return n.useEffect(()=>{i.current!==u&&(l(u),i.current=u)},[u,i,l]),r}({defaultProp:t,onChange:r}),l=void 0!==e,a=l?e:u,s=(0,o.W)(r);return[a,n.useCallback(t=>{if(l){let r="function"==typeof t?t(e):t;r!==e&&s(r)}else i(t)},[l,e,i,s])]}},65819:(e,t,r)=>{r.d(t,{b:()=>o});var n=r(17577),o=globalThis?.document?n.useLayoutEffect:()=>{}}};