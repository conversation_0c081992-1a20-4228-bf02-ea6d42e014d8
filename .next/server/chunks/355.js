"use strict";exports.id=355,exports.ids=[355],exports.modules={67721:(e,t,r)=>{r.d(t,{createServerClient:()=>w});var o=r(43183);function n(){return"undefined"!=typeof window&&void 0!==window.document}let i={path:"/",sameSite:"lax",httpOnly:!1,maxAge:3456e4},a=/^(.*)[.](0|[1-9][0-9]*)$/;function s(e,t){if(e===t)return!0;let r=e.match(a);return!!r&&r[1]===t}function l(e,t,r){let o=r??3180,n=encodeURIComponent(t);if(n.length<=o)return[{name:e,value:t}];let i=[];for(;n.length>0;){let e=n.slice(0,o),t=e.lastIndexOf("%");t>o-3&&(e=e.slice(0,t));let r="";for(;e.length>0;)try{r=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}i.push(r),n=n.slice(e.length)}return i.map((t,r)=>({name:`${e}.${r}`,value:t}))}async function c(e,t){let r=await t(e);if(r)return r;let o=[];for(let r=0;;r++){let n=`${e}.${r}`,i=await t(n);if(!i)break;o.push(i)}return o.length>0?o.join(""):null}let u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),d=" 	\n\r=".split(""),f=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<d.length;t+=1)e[d[t].charCodeAt(0)]=-2;for(let t=0;t<u.length;t+=1)e[u[t].charCodeAt(0)]=t;return e})();function p(e){let t=[],r=0,o=0;if(function(e,t){for(let r=0;r<e.length;r+=1){let o=e.charCodeAt(r);if(o>55295&&o<=56319){let t=(o-55296)*1024&65535;o=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127){t(e);return}if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(o,t)}}(e,e=>{for(r=r<<8|e,o+=8;o>=6;){let e=r>>o-6&63;t.push(u[e]),o-=6}}),o>0)for(r<<=6-o,o=6;o>=6;){let e=r>>o-6&63;t.push(u[e]),o-=6}return t.join("")}function h(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},o={utf8seq:0,codepoint:0},n=0,i=0;for(let t=0;t<e.length;t+=1){let a=f[e.charCodeAt(t)];if(a>-1)for(n=n<<6|a,i+=6;i>=8;)(function(e,t,r){if(0===t.utf8seq){if(e<=127){r(e);return}for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}})(n>>i-8&255,o,r),i-=8;else if(-2===a)continue;else throw Error(`Invalid Base64-URL character "${e.at(t)}" at position ${t}`)}return t.join("")}let g="base64-";async function y({getAll:e,setAll:t,setItems:r,removedItems:o},n){let a=n.cookieEncoding,c=n.cookieOptions??null,u=await e([...r?Object.keys(r):[],...o?Object.keys(o):[]]),d=u?.map(({name:e})=>e)||[],f=Object.keys(o).flatMap(e=>d.filter(t=>s(t,e))),h=Object.keys(r).flatMap(e=>{let t=new Set(d.filter(t=>s(t,e))),o=r[e];"base64url"===a&&(o=g+p(o));let n=l(e,o);return n.forEach(e=>{t.delete(e.name)}),f.push(...t),n}),y={...i,...c,maxAge:0},m={...i,...c,maxAge:i.maxAge};delete y.name,delete m.name,await t([...f.map(e=>({name:e,value:"",options:y})),...h.map(({name:e,value:t})=>({name:e,value:t,options:m}))])}var m=r(12814);function w(e,t,r){if(!e||!t)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{storage:a,getAll:u,setAll:d,setItems:f,removedItems:w}=function(e,t){let r,a;let u=e.cookies??null,d=e.cookieEncoding,f={},m={};if(u){if("get"in u){let e=async e=>{let t=e.flatMap(e=>[e,...Array.from({length:5}).map((t,r)=>`${e}.${r}`)]),r=[];for(let e=0;e<t.length;e+=1){let o=await u.get(t[e]);(o||"string"==typeof o)&&r.push({name:t[e],value:o})}return r};if(r=async t=>await e(t),"set"in u&&"remove"in u)a=async e=>{for(let t=0;t<e.length;t+=1){let{name:r,value:o,options:n}=e[t];o?await u.set(r,o,n):await u.remove(r,n)}};else if(t)a=async()=>{console.warn("@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in u){if(r=async()=>await u.getAll(),"setAll"in u)a=u.setAll;else if(t)a=async()=>{console.warn("@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.")};else throw Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)")}else throw Error(`@supabase/ssr: ${t?"createServerClient":"createBrowserClient"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${n()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`)}else if(!t&&n()){let e=()=>{let e=(0,o.Q)(document.cookie);return Object.keys(e).map(t=>({name:t,value:e[t]}))};r=()=>e(),a=e=>{e.forEach(({name:e,value:t,options:r})=>{document.cookie=(0,o.q)(e,t,r)})}}else if(t)throw Error("@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)");else r=()=>[],a=()=>{throw Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return t?{getAll:r,setAll:a,setItems:f,removedItems:m,storage:{isServer:!0,getItem:async e=>{if("string"==typeof f[e])return f[e];if(m[e])return null;let t=await r([e]),o=await c(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!o)return null;let n=o;return"string"==typeof o&&o.startsWith(g)&&(n=h(o.substring(g.length))),n},setItem:async(t,o)=>{t.endsWith("-code-verifier")&&await y({getAll:r,setAll:a,setItems:{[t]:o},removedItems:{}},{cookieOptions:e?.cookieOptions??null,cookieEncoding:d}),f[t]=o,delete m[t]},removeItem:async e=>{delete f[e],m[e]=!0}}}:{getAll:r,setAll:a,setItems:f,removedItems:m,storage:{isServer:!1,getItem:async e=>{let t=await r([e]),o=await c(e,async e=>{let r=t?.find(({name:t})=>t===e)||null;return r?r.value:null});if(!o)return null;let n=o;return o.startsWith(g)&&(n=h(o.substring(g.length))),n},setItem:async(t,o)=>{let n=await r([t]),c=new Set((n?.map(({name:e})=>e)||[]).filter(e=>s(e,t))),u=o;"base64url"===d&&(u=g+p(o));let f=l(t,u);f.forEach(({name:e})=>{c.delete(e)});let h={...i,...e?.cookieOptions,maxAge:0},y={...i,...e?.cookieOptions,maxAge:i.maxAge};delete h.name,delete y.name;let m=[...[...c].map(e=>({name:e,value:"",options:h})),...f.map(({name:e,value:t})=>({name:e,value:t,options:y}))];m.length>0&&await a(m)},removeItem:async t=>{let o=await r([t]),n=(o?.map(({name:e})=>e)||[]).filter(e=>s(e,t)),l={...i,...e?.cookieOptions,maxAge:0};delete l.name,n.length>0&&await a(n.map(e=>({name:e,value:"",options:l})))}}}}({...r,cookieEncoding:r?.cookieEncoding??"base64url"},!0),b=(0,m.eI)(e,t,{...r,global:{...r?.global,headers:{...r?.global?.headers,"X-Client-Info":"supabase-ssr/0.5.2"}},auth:{...r?.cookieOptions?.name?{storageKey:r.cookieOptions.name}:null,...r?.auth,flowType:"pkce",autoRefreshToken:!1,detectSessionInUrl:!1,persistSession:!0,storage:a}});return b.auth.onAuthStateChange(async e=>{(Object.keys(f).length>0||Object.keys(w).length>0)&&("SIGNED_IN"===e||"TOKEN_REFRESHED"===e||"USER_UPDATED"===e||"PASSWORD_RECOVERY"===e||"SIGNED_OUT"===e||"MFA_CHALLENGE_VERIFIED"===e)&&await y({getAll:u,setAll:d,setItems:f,removedItems:w},{cookieOptions:r?.cookieOptions??null,cookieEncoding:r?.cookieEncoding??"base64url"})}),b}},43183:(e,t)=>{t.Q=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");var r={},n=e.length;if(n<2)return r;var i=t&&t.decode||u,a=0,s=0,d=0;do{if(-1===(s=e.indexOf("=",a)))break;if(-1===(d=e.indexOf(";",a)))d=n;else if(s>d){a=e.lastIndexOf(";",s-1)+1;continue}var f=l(e,a,s),p=c(e,s,f),h=e.slice(f,p);if(!o.call(r,h)){var g=l(e,s+1,d),y=c(e,d,g);34===e.charCodeAt(g)&&34===e.charCodeAt(y-1)&&(g++,y--);var m=e.slice(g,y);r[h]=function(e,t){try{return t(e)}catch(t){return e}}(m,i)}a=d+1}while(a<n);return r},t.q=function(e,t,o){var l=o&&o.encode||encodeURIComponent;if("function"!=typeof l)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var c=l(t);if(!i.test(c))throw TypeError("argument val is invalid");var u=e+"="+c;if(!o)return u;if(null!=o.maxAge){var d=Math.floor(o.maxAge);if(!isFinite(d))throw TypeError("option maxAge is invalid");u+="; Max-Age="+d}if(o.domain){if(!a.test(o.domain))throw TypeError("option domain is invalid");u+="; Domain="+o.domain}if(o.path){if(!s.test(o.path))throw TypeError("option path is invalid");u+="; Path="+o.path}if(o.expires){var f=o.expires;if("[object Date]"!==r.call(f)||isNaN(f.valueOf()))throw TypeError("option expires is invalid");u+="; Expires="+f.toUTCString()}if(o.httpOnly&&(u+="; HttpOnly"),o.secure&&(u+="; Secure"),o.partitioned&&(u+="; Partitioned"),o.priority)switch("string"==typeof o.priority?o.priority.toLowerCase():o.priority){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return u};var r=Object.prototype.toString,o=Object.prototype.hasOwnProperty,n=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,i=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,a=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/;function l(e,t,r){do{var o=e.charCodeAt(t);if(32!==o&&9!==o)return t}while(++t<r);return r}function c(e,t,r){for(;t>r;){var o=e.charCodeAt(--t);if(32!==o&&9!==o)return t+1}return r}function u(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}},71615:(e,t,r)=>{var o=r(88757);r.o(o,"cookies")&&r.d(t,{cookies:function(){return o.cookies}})},33085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return i}});let o=r(45869),n=r(6278);class i{get isEnabled(){return this._provider.isEnabled}enable(){let e=o.staticGenerationAsyncStorage.getStore();return e&&(0,n.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=o.staticGenerationAsyncStorage.getStore();return e&&(0,n.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return f},draftMode:function(){return p},headers:function(){return d}});let o=r(68996),n=r(53047),i=r(92044),a=r(72934),s=r(33085),l=r(6278),c=r(45869),u=r(54580);function d(){let e="headers",t=c.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return n.HeadersAdapter.seal(new Headers({}));(0,l.trackDynamicDataAccessed)(t,e)}return(0,u.getExpectedRequestStore)(e).headers}function f(){let e="cookies",t=c.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return o.RequestCookiesAdapter.seal(new i.RequestCookies(new Headers({})));(0,l.trackDynamicDataAccessed)(t,e)}let r=(0,u.getExpectedRequestStore)(e),n=a.actionAsyncStorage.getStore();return(null==n?void 0:n.isAction)||(null==n?void 0:n.isAppRoute)?r.mutableCookies:r.cookies}function p(){let e=(0,u.getExpectedRequestStore)("draftMode");return new s.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53047:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return n}});let o=r(38238);class n extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new n}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return o.ReflectAdapter.get(t,r,n);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==a)return o.ReflectAdapter.get(t,a,n)},set(t,r,n,i){if("symbol"==typeof r)return o.ReflectAdapter.set(t,r,n,i);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);return o.ReflectAdapter.set(t,s??r,n,i)},has(t,r){if("symbol"==typeof r)return o.ReflectAdapter.has(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==i&&o.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return o.ReflectAdapter.deleteProperty(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===i||o.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return n.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,o]of this.entries())e.call(t,o,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},68996:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return d},ReadonlyRequestCookiesError:function(){return a},RequestCookiesAdapter:function(){return s},appendMutableCookies:function(){return u},getModifiedCookieValues:function(){return c}});let o=r(92044),n=r(38238),i=r(45869);class a extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new a}}class s{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return a.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function c(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function u(e,t){let r=c(t);if(0===r.length)return!1;let n=new o.ResponseCookies(e),i=n.getAll();for(let e of r)n.set(e);for(let e of i)n.set(e);return!0}class d{static wrap(e,t){let r=new o.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let a=[],s=new Set,c=()=>{let e=i.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of a){let r=new o.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case l:return a;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{c()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{c()}};default:return n.ReflectAdapter.get(e,t,r)}}})}}}};