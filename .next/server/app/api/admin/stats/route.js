"use strict";(()=>{var e={};e.id=553,e.ids=[553],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},71568:e=>{e.exports=require("zlib")},63965:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>x,requestAsyncStorage:()=>l,routeModule:()=>c,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(49303),a=t(88716),n=t(60670),i=t(48088),u=t(87070);async function p(e){try{let r=(0,i.i3)(),t=e.headers.get("Authorization"),s=t?.replace("Bearer ","");if(!s)return console.warn("/api/admin/stats: No token provided"),u.NextResponse.json({error:"Authentication required"},{status:401});let{data:{user:o},error:a}=await r.auth.getUser(s);if(a||!o)return console.warn("/api/admin/stats: Invalid token or user not found.",a?.message),u.NextResponse.json({error:a?.message||"Invalid token"},{status:401});console.log(`/api/admin/stats: Request from authenticated user ${o.id}. Fetching stats.`);let{count:n,error:p}=await r.from("profiles").select("*",{count:"exact",head:!0}),{count:c,error:l}=await r.from("couples").select("*",{count:"exact",head:!0}),{count:d,error:m}=await r.from("counselor_profiles").select("*",{count:"exact",head:!0}),{count:h,error:x}=await r.from("individual_results").select("*",{count:"exact",head:!0});if(p||l||m||x)return console.error("/api/admin/stats: Error fetching one or more stats:",{usersError:p?.message,couplesError:l?.message,counselorsError:m?.message,assessmentsError:x?.message}),u.NextResponse.json({error:"Failed to fetch some statistics"},{status:500});let f={totalUsers:n||0,totalCouples:c||0,totalCounselors:d||0,completedAssessments:h||0};return console.log("/api/admin/stats: Successfully fetched stats:",f),u.NextResponse.json(f)}catch(e){return console.error("/api/admin/stats: Unhandled exception:",e.message,e.stack),u.NextResponse.json({error:"Internal server error fetching stats"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/admin/stats/route",pathname:"/api/admin/stats",filename:"route",bundlePath:"app/api/admin/stats/route"},resolvedPagePath:"/Users/<USER>/Nextjs/marriage-map/src/app/api/admin/stats/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:d,serverHooks:m}=c,h="/api/admin/stats/route";function x(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}},48088:(e,r,t)=>{t.d(r,{i3:()=>i});var s=t(12814);let o=null,a="https://eqghwtejdnzgopmcjlho.supabase.co",n=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!a)throw Error("Missing Supabase environment variables");let i=()=>{if(!o){if(!n)throw Error("SUPABASE_SERVICE_ROLE_KEY is required for admin operations");o=(0,s.eI)(a,n,{auth:{autoRefreshToken:!1,persistSession:!1}})}return o}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[948,505],()=>t(63965));module.exports=s})();