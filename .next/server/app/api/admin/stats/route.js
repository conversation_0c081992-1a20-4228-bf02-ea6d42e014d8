"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/stats/route";
exports.ids = ["app/api/admin/stats/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_yoshuavictor_Nextjs_marriage_map_src_app_api_admin_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/stats/route.ts */ \"(rsc)/./src/app/api/admin/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/stats/route\",\n        pathname: \"/api/admin/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/stats/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Nextjs/marriage-map/src/app/api/admin/stats/route.ts\",\n    nextConfigOutput,\n    userland: _Users_yoshuavictor_Nextjs_marriage_map_src_app_api_admin_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZhZG1pbiUyRnN0YXRzJTJGcm91dGUmcGFnZT0lMkZhcGklMkZhZG1pbiUyRnN0YXRzJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGYWRtaW4lMkZzdGF0cyUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRnlvc2h1YXZpY3RvciUyRk5leHRqcyUyRm1hcnJpYWdlLW1hcCUyRnNyYyUyRmFwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9JTJGVXNlcnMlMkZ5b3NodWF2aWN0b3IlMkZOZXh0anMlMkZtYXJyaWFnZS1tYXAmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQ3dCO0FBQ3JHO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnSEFBbUI7QUFDM0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsaUVBQWlFO0FBQ3pFO0FBQ0E7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDdUg7O0FBRXZIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLz85ZWMyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi9Vc2Vycy95b3NodWF2aWN0b3IvTmV4dGpzL21hcnJpYWdlLW1hcC9zcmMvYXBwL2FwaS9hZG1pbi9zdGF0cy9yb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvYWRtaW4vc3RhdHMvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9hZG1pbi9zdGF0c1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvYWRtaW4vc3RhdHMvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCIvVXNlcnMveW9zaHVhdmljdG9yL05leHRqcy9tYXJyaWFnZS1tYXAvc3JjL2FwcC9hcGkvYWRtaW4vc3RhdHMvcm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5jb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXBpL2FkbWluL3N0YXRzL3JvdXRlXCI7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHNlcnZlckhvb2tzLFxuICAgICAgICBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIG9yaWdpbmFsUGF0aG5hbWUsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/stats/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/admin/stats/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(rsc)/./src/lib/supabase/client.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.createAdminClient)(); // Uses SERVICE_ROLE_KEY\n        // 1. Verify the user token from the Authorization header\n        const authHeader = request.headers.get(\"Authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            console.warn(\"/api/admin/stats: No token provided\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        const { data: { user }, error: userError } = await supabase.auth.getUser(token);\n        if (userError || !user) {\n            console.warn(\"/api/admin/stats: Invalid token or user not found.\", userError?.message);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: userError?.message || \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        // Optional: Re-verify admin role here if desired for extra security, \n        // though the frontend /api/auth should have already done this.\n        // For simplicity, we'll assume if they have a valid token and hit this endpoint,\n        // the frontend has done its due diligence. A more robust solution might re-check admin_users table.\n        console.log(`/api/admin/stats: Request from authenticated user ${user.id}. Fetching stats.`);\n        // 2. Fetch statistics\n        const { count: usersCount, error: usersError } = await supabase.from(\"profiles\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        const { count: couplesCount, error: couplesError } = await supabase.from(\"couples\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        const { count: counselorsCount, error: counselorsError } = await supabase.from(\"counselor_profiles\").select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        const { count: completedAssessmentsCount, error: assessmentsError } = await supabase.from(\"individual_results\") // Assuming this table stores completed assessments\n        .select(\"*\", {\n            count: \"exact\",\n            head: true\n        });\n        if (usersError || couplesError || counselorsError || assessmentsError) {\n            console.error(\"/api/admin/stats: Error fetching one or more stats:\", {\n                usersError: usersError?.message,\n                couplesError: couplesError?.message,\n                counselorsError: counselorsError?.message,\n                assessmentsError: assessmentsError?.message\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                error: \"Failed to fetch some statistics\"\n            }, {\n                status: 500\n            });\n        }\n        const stats = {\n            totalUsers: usersCount || 0,\n            totalCouples: couplesCount || 0,\n            totalCounselors: counselorsCount || 0,\n            completedAssessments: completedAssessmentsCount || 0\n        };\n        console.log(\"/api/admin/stats: Successfully fetched stats:\", stats);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json(stats);\n    } catch (error) {\n        console.error(\"/api/admin/stats: Unhandled exception:\", error.message, error.stack);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            error: \"Internal server error fetching stats\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminClient: () => (/* binding */ createAdminClient),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   getClient: () => (/* binding */ getClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Check if the code is running in a browser environment\nconst isBrowser = \"undefined\" !== \"undefined\";\n// These will store the singleton instances\nlet supabaseClient = null;\nlet supabaseAdminClient = null;\n// Get the Supabase URL and anon key from environment variables\nconst supabaseUrl = \"https://eqghwtejdnzgopmcjlho.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // For admin operations\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\n/**\n * Get or create the Supabase client for client-side usage\n * This should be used in browser environments only\n */ const createClient = ()=>{\n    if (!isBrowser) {\n        throw new Error(\"createClient should only be called on the client side\");\n    }\n    if (!supabaseClient) {\n        supabaseClient = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n            auth: {\n                persistSession: true,\n                autoRefreshToken: true,\n                detectSessionInUrl: true\n            }\n        });\n    }\n    return supabaseClient;\n};\n/**\n * Get or create the admin Supabase client with service role key\n * This should be used in server-side or API routes only\n */ const createAdminClient = ()=>{\n    if (!supabaseAdminClient) {\n        if (!supabaseServiceKey) {\n            throw new Error(\"SUPABASE_SERVICE_ROLE_KEY is required for admin operations\");\n        }\n        supabaseAdminClient = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n            auth: {\n                autoRefreshToken: false,\n                persistSession: false\n            }\n        });\n    }\n    return supabaseAdminClient;\n};\n/**\n * Get the current Supabase client instance\n * This is the preferred way to access the client in components\n */ const getClient = ()=>{\n    if (isBrowser) {\n        return createClient();\n    }\n    return createAdminClient();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/client.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();