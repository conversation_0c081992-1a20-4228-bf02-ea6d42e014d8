"use strict";(()=>{var e={};e.id=932,e.ids=[932],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},71568:e=>{e.exports=require("zlib")},39066:(e,r,s)=>{s.r(r),s.d(r,{originalPathname:()=>m,patchFetch:()=>f,requestAsyncStorage:()=>d,routeModule:()=>c,serverHooks:()=>g,staticGenerationAsyncStorage:()=>h});var o={};s.r(o),s.d(o,{GET:()=>p,POST:()=>l});var t=s(49303),a=s(88716),n=s(60670),i=s(48088),u=s(87070);async function l(e){try{let{email:r,password:s,role:o,fullName:t}=await e.json(),a=(0,i.i3)();console.log(`/api/auth POST: Attempting to create user ${r} with role ${o}`);let{data:n,error:l}=await a.auth.admin.createUser({email:r,password:s,email_confirm:!0});if(l)return console.error("/api/auth POST: Error creating user via supabase.auth.admin.createUser:",l.message),u.NextResponse.json({error:l.message},{status:400});if(!n.user)return console.error("/api/auth POST: Failed to create user, no user data returned from createUser."),u.NextResponse.json({error:"Failed to create user"},{status:400});if(console.log(`/api/auth POST: User created successfully via admin client: ${n.user.id}`),"admin"===o){console.log(`/api/auth POST: Creating admin_users entry for ${n.user.id}`);let{error:e}=await a.from("admin_users").insert([{user_id:n.user.id}]);if(e)return console.error("/api/auth POST: Error creating admin_users entry:",e.message),u.NextResponse.json({error:e.message},{status:400})}else if("counselor"===o){let e=t||r.split("@")[0];console.log(`/api/auth POST: Creating counselor_profiles entry for ${n.user.id}`);let{error:s}=await a.from("counselor_profiles").insert([{user_id:n.user.id,full_name:e}]);if(s)return console.error("/api/auth POST: Error creating counselor_profiles entry:",s.message),u.NextResponse.json({error:s.message},{status:400})}let p=t||r.split("@")[0];console.log(`/api/auth POST: Upserting profiles entry for ${n.user.id}`);let{error:c}=await a.from("profiles").upsert({id:n.user.id,full_name:p,email:r},{onConflict:"id"});if(c)return console.error("/api/auth POST: Error upserting profiles entry:",c.message),u.NextResponse.json({error:c.message},{status:400});return console.log(`/api/auth POST: Successfully processed registration for ${n.user.id}`),u.NextResponse.json({message:"User created successfully",user:n.user,role:o})}catch(e){return console.error("/api/auth POST: Unhandled exception:",e.message?e.message:e,e.stack),u.NextResponse.json({error:"Internal server error during registration"},{status:500})}}async function p(e){try{let r=(0,i.i3)(),s=e.headers.get("Authorization"),o=s?.replace("Bearer ","");if(!o)return console.log("/api/auth GET: No token provided"),u.NextResponse.json({error:"No token provided"},{status:401});console.log("/api/auth GET: Received token:",o?"Exists":"Empty/Null");let{data:{user:t},error:a}=await r.auth.getUser(o);if(console.log("/api/auth GET: getUser result - user:",t?t.id:null,"error:",a?a.message:null),a)return console.error("/api/auth GET: Error from supabase.auth.getUser:",a.message),u.NextResponse.json({error:a.message},{status:401});if(!t)return console.log("/api/auth GET: Not authenticated, no user found for token."),u.NextResponse.json({error:"Not authenticated"},{status:401});console.log(`/api/auth GET: Authenticated user ID: ${t.id}`),console.log(`/api/auth GET: Checking admin_users for user_id: ${t.id}`);let{data:n,error:l}=await r.from("admin_users").select("user_id").eq("user_id",t.id).limit(1);if(l)return console.error("/api/auth GET: Database error querying admin_users:",l.message),u.NextResponse.json({error:"Server error checking admin role"},{status:500});if(console.log("/api/auth GET: admin_users query result - records count:",n?n.length:"null"),n&&n.length>0)return console.log(`/api/auth GET: User ${t.id} is admin.`),u.NextResponse.json({user:t,role:"admin"});console.log(`/api/auth GET: Checking counselor_profiles for user_id: ${t.id}`);let{data:p,error:c}=await r.from("counselor_profiles").select("user_id").eq("user_id",t.id).limit(1);if(c)return console.error("/api/auth GET: Database error querying counselor_profiles:",c.message),u.NextResponse.json({error:"Server error checking counselor role"},{status:500});if(console.log("/api/auth GET: counselor_profiles query result - records count:",p?p.length:"null"),p&&p.length>0)return console.log(`/api/auth GET: User ${t.id} is counselor.`),u.NextResponse.json({user:t,role:"counselor"});return console.log(`/api/auth GET: User ${t.id} is default user.`),u.NextResponse.json({user:t,role:"user"})}catch(e){return console.error("/api/auth GET: Unhandled exception in GET handler:",e.message?e.message:e,e.stack),u.NextResponse.json({error:"Internal server error from GET handler"},{status:500})}}let c=new t.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/route",pathname:"/api/auth",filename:"route",bundlePath:"app/api/auth/route"},resolvedPagePath:"/Users/<USER>/Nextjs/marriage-map/src/app/api/auth/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:d,staticGenerationAsyncStorage:h,serverHooks:g}=c,m="/api/auth/route";function f(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:h})}},48088:(e,r,s)=>{s.d(r,{i3:()=>i});var o=s(12814);let t=null,a="https://eqghwtejdnzgopmcjlho.supabase.co",n=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!a)throw Error("Missing Supabase environment variables");let i=()=>{if(!t){if(!n)throw Error("SUPABASE_SERVICE_ROLE_KEY is required for admin operations");t=(0,o.eI)(a,n,{auth:{autoRefreshToken:!1,persistSession:!1}})}return t}}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),o=r.X(0,[948,505],()=>s(39066));module.exports=o})();