"use strict";(()=>{var e={};e.id=770,e.ids=[770],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},71568:e=>{e.exports=require("zlib")},87409:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>_,patchFetch:()=>m,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>x,staticGenerationAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>c});var o=t(49303),n=t(88716),i=t(60670),a=t(87070),u=t(65655);async function c(e){try{let{code:r}=await e.json();if(!r||"string"!=typeof r||6!==r.length)return a.NextResponse.json({error:"Invalid code format. Code must be 6 characters."},{status:400});let t=(0,u.e)(),{data:{user:s},error:o}=await t.auth.getUser();if(o||!s)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{data:n}=await t.from("couples").select("couple_id").or(`user_id_1.eq.${s.id},user_id_2.eq.${s.id}`).single();if(n)return a.NextResponse.json({error:"You are already connected with a partner"},{status:400});let{data:i,error:c}=await t.from("couple_invitation_codes").select("*").eq("code",r.toUpperCase()).eq("is_active",!0).single();if(c||!i)return a.NextResponse.json({error:"Invalid or expired invitation code"},{status:400});if(new Date(i.expires_at)<new Date)return a.NextResponse.json({error:"Invitation code has expired"},{status:400});if(i.used_by_user_id)return a.NextResponse.json({error:"Invitation code has already been used"},{status:400});if(i.creator_user_id===s.id)return a.NextResponse.json({error:"You cannot use your own invitation code"},{status:400});let{data:p}=await t.from("couples").select("couple_id").or(`user_id_1.eq.${i.creator_user_id},user_id_2.eq.${i.creator_user_id}`).single();if(p)return a.NextResponse.json({error:"The code creator is already connected with someone else"},{status:400});let{data:d,error:l}=await t.from("couples").insert({user_id_1:i.creator_user_id,user_id_2:s.id}).select().single();if(l)return console.error("Error creating couple:",l),a.NextResponse.json({error:"Failed to create couple connection"},{status:500});let{error:x}=await t.from("couple_invitation_codes").update({used_by_user_id:s.id,used_at:new Date().toISOString(),is_active:!1}).eq("id",i.id);x&&console.error("Error updating invitation code:",x);let{data:_}=await t.from("profiles").select("full_name, email").eq("id",i.creator_user_id).single();return a.NextResponse.json({success:!0,couple_id:d.couple_id,partner:{id:i.creator_user_id,name:_?.full_name||"Partner",email:_?.email},message:"Successfully connected with your partner!"})}catch(e){return console.error("Error in connect API:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/couples/connect/route",pathname:"/api/couples/connect",filename:"route",bundlePath:"app/api/couples/connect/route"},resolvedPagePath:"/Users/<USER>/Nextjs/marriage-map/src/app/api/couples/connect/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:x}=p,_="/api/couples/connect/route";function m(){return(0,i.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:l})}},65655:(e,r,t)=>{t.d(r,{e:()=>n});var s=t(67721),o=t(71615);function n(){let e=(0,o.cookies)();return(0,s.createServerClient)("https://eqghwtejdnzgopmcjlho.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY",{cookies:{get:r=>e.get(r)?.value,set(r,t,s){try{e.set({name:r,value:t,...s})}catch(e){}},remove(r,t){try{e.set({name:r,value:"",...t})}catch(e){}}}})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[948,505,355],()=>t(87409));module.exports=s})();