"use strict";(()=>{var e={};e.id=725,e.ids=[725],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},71568:e=>{e.exports=require("zlib")},47146:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>m,patchFetch:()=>x,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>_,staticGenerationAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(49303),a=t(88716),i=t(60670),n=t(87070),u=t(48088);async function p(e){try{let r=(0,u.i3)(),t=e.headers.get("Authorization"),s=t?.replace("Bearer ","");if(!s)return n.NextResponse.json({error:"No token provided"},{status:401});let{data:{user:o},error:a}=await r.auth.getUser(s);if(a||!o)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{data:i,error:p}=await r.from("couples").select(`
        couple_id,
        user_id_1,
        user_id_2,
        created_at
      `).or(`user_id_1.eq.${o.id},user_id_2.eq.${o.id}`).single();if(p&&"PGRST116"!==p.code)return console.error("Error fetching couple:",p),n.NextResponse.json({error:"Failed to fetch couple status"},{status:500});if(!i){let{data:e}=await r.from("couple_invitation_codes").select("code, expires_at").eq("creator_user_id",o.id).eq("is_active",!0).order("created_at",{ascending:!1}).limit(1);return n.NextResponse.json({isConnected:!1,couple:null,partner:null,activeInvitationCode:e?.[0]||null})}let l=i.user_id_1===o.id?i.user_id_2:i.user_id_1,{data:d,error:c}=await r.from("profiles").select("full_name, email").eq("id",l).single(),_=i.couple_id.substring(0,8).toUpperCase(),m="Partner";return d?.email?m=d.email:d?.full_name&&(m=d.full_name),n.NextResponse.json({isConnected:!0,couple:{id:i.couple_id,friendlyCode:_,created_at:i.created_at},partner:{id:l,name:d?.full_name||"Partner",email:d?.email||"Unknown",displayName:m},activeInvitationCode:null})}catch(e){return console.error("Error in couples status API:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/couples/status/route",pathname:"/api/couples/status",filename:"route",bundlePath:"app/api/couples/status/route"},resolvedPagePath:"/Users/<USER>/Nextjs/marriage-map/src/app/api/couples/status/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:c,serverHooks:_}=l,m="/api/couples/status/route";function x(){return(0,i.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:c})}},48088:(e,r,t)=>{t.d(r,{i3:()=>n});var s=t(12814);let o=null,a="https://eqghwtejdnzgopmcjlho.supabase.co",i=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!a)throw Error("Missing Supabase environment variables");let n=()=>{if(!o){if(!i)throw Error("SUPABASE_SERVICE_ROLE_KEY is required for admin operations");o=(0,s.eI)(a,i,{auth:{autoRefreshToken:!1,persistSession:!1}})}return o}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[948,505],()=>t(47146));module.exports=s})();