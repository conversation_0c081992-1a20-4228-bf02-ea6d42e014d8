"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/couples/status/route";
exports.ids = ["app/api/couples/status/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcouples%2Fstatus%2Froute&page=%2Fapi%2Fcouples%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcouples%2Fstatus%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcouples%2Fstatus%2Froute&page=%2Fapi%2Fcouples%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcouples%2Fstatus%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_yoshuavictor_Nextjs_marriage_map_src_app_api_couples_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/couples/status/route.ts */ \"(rsc)/./src/app/api/couples/status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/couples/status/route\",\n        pathname: \"/api/couples/status\",\n        filename: \"route\",\n        bundlePath: \"app/api/couples/status/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Nextjs/marriage-map/src/app/api/couples/status/route.ts\",\n    nextConfigOutput,\n    userland: _Users_yoshuavictor_Nextjs_marriage_map_src_app_api_couples_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/couples/status/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcouples%2Fstatus%2Froute&page=%2Fapi%2Fcouples%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcouples%2Fstatus%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/couples/status/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/couples/status/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/client */ \"(rsc)/./src/lib/supabase/client.ts\");\n\n\nconst dynamic = \"force-dynamic\";\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_1__.createAdminClient)();\n        // Get token from Authorization header\n        const authHeader = request.headers.get(\"Authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"No token provided\"\n            }, {\n                status: 401\n            });\n        }\n        // Get current user using token\n        const { data: { user }, error: userError } = await supabase.auth.getUser(token);\n        if (userError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is in a couple\n        const { data: couple, error: coupleError } = await supabase.from(\"couples\").select(`\n        couple_id,\n        user_id_1,\n        user_id_2,\n        created_at\n      `).or(`user_id_1.eq.${user.id},user_id_2.eq.${user.id}`).single();\n        if (coupleError && coupleError.code !== \"PGRST116\") {\n            console.error(\"Error fetching couple:\", coupleError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch couple status\"\n            }, {\n                status: 500\n            });\n        }\n        if (!couple) {\n            // User is not connected, check for active invitation codes\n            const { data: activeCodes } = await supabase.from(\"couple_invitation_codes\").select(\"code, expires_at\").eq(\"creator_user_id\", user.id).eq(\"is_active\", true).order(\"created_at\", {\n                ascending: false\n            }).limit(1);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                isConnected: false,\n                couple: null,\n                partner: null,\n                activeInvitationCode: activeCodes?.[0] || null\n            });\n        }\n        // Get partner ID\n        const partnerId = couple.user_id_1 === user.id ? couple.user_id_2 : couple.user_id_1;\n        // Get partner profile (without avatar_url for now due to column not existing)\n        const { data: partnerProfile, error: partnerError } = await supabase.from(\"profiles\").select(\"full_name, email\").eq(\"id\", partnerId).single();\n        // Generate a user-friendly couple code (first 8 characters of UUID)\n        const friendlyCode = couple.couple_id.substring(0, 8).toUpperCase();\n        // Determine display name with priority: email > full_name > \"Partner\"\n        let displayName = \"Partner\";\n        if (partnerProfile?.email) {\n            displayName = partnerProfile.email;\n        } else if (partnerProfile?.full_name) {\n            displayName = partnerProfile.full_name;\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            isConnected: true,\n            couple: {\n                id: couple.couple_id,\n                friendlyCode: friendlyCode,\n                created_at: couple.created_at\n            },\n            partner: {\n                id: partnerId,\n                name: partnerProfile?.full_name || \"Partner\",\n                email: partnerProfile?.email || \"Unknown\",\n                displayName: displayName\n            },\n            activeInvitationCode: null\n        });\n    } catch (error) {\n        console.error(\"Error in couples status API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/couples/status/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminClient: () => (/* binding */ createAdminClient),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   getClient: () => (/* binding */ getClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Check if the code is running in a browser environment\nconst isBrowser = \"undefined\" !== \"undefined\";\n// These will store the singleton instances\nlet supabaseClient = null;\nlet supabaseAdminClient = null;\n// Get the Supabase URL and anon key from environment variables\nconst supabaseUrl = \"https://eqghwtejdnzgopmcjlho.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // For admin operations\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\n/**\n * Get or create the Supabase client for client-side usage\n * This should be used in browser environments only\n */ const createClient = ()=>{\n    if (!isBrowser) {\n        throw new Error(\"createClient should only be called on the client side\");\n    }\n    if (!supabaseClient) {\n        supabaseClient = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n            auth: {\n                persistSession: true,\n                autoRefreshToken: true,\n                detectSessionInUrl: true\n            }\n        });\n    }\n    return supabaseClient;\n};\n/**\n * Get or create the admin Supabase client with service role key\n * This should be used in server-side or API routes only\n */ const createAdminClient = ()=>{\n    if (!supabaseAdminClient) {\n        if (!supabaseServiceKey) {\n            throw new Error(\"SUPABASE_SERVICE_ROLE_KEY is required for admin operations\");\n        }\n        supabaseAdminClient = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n            auth: {\n                autoRefreshToken: false,\n                persistSession: false\n            }\n        });\n    }\n    return supabaseAdminClient;\n};\n/**\n * Get the current Supabase client instance\n * This is the preferred way to access the client in components\n */ const getClient = ()=>{\n    if (isBrowser) {\n        return createClient();\n    }\n    return createAdminClient();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/client.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcouples%2Fstatus%2Froute&page=%2Fapi%2Fcouples%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcouples%2Fstatus%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fmarriage-map&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();