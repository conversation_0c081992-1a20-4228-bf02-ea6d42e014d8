"use strict";(()=>{var e={};e.id=851,e.ids=[851],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{e.exports=require("buffer")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},68621:e=>{e.exports=require("punycode")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},17360:e=>{e.exports=require("url")},71568:e=>{e.exports=require("zlib")},67632:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>q,patchFetch:()=>m,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>g,staticGenerationAsyncStorage:()=>x});var o={};r.r(o),r.d(o,{POST:()=>p});var s=r(49303),i=r(88716),a=r(60670),n=r(87070),c=r(65655),u=r(71615);async function p(e){try{let e;(0,u.cookies)();let t=(0,c.e)(),{data:{user:r},error:o}=await t.auth.getUser();if(o||!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await t.from("couples").select("couple_id").or(`user_id_1.eq.${r.id},user_id_2.eq.${r.id}`).single();if(s)return n.NextResponse.json({error:"You are already connected with a partner"},{status:400});await t.from("couple_invitation_codes").update({is_active:!1}).eq("creator_user_id",r.id).eq("is_active",!0);let i=!1,a=0;do{e=Math.random().toString(36).substring(2,8).toUpperCase();let{data:r}=await t.from("couple_invitation_codes").select("id").eq("code",e).eq("is_active",!0).single();i=!r,a++}while(!i&&a<10);if(!i)return n.NextResponse.json({error:"Failed to generate unique code. Please try again."},{status:500});let{data:p,error:d}=await t.from("couple_invitation_codes").insert({code:e,creator_user_id:r.id,expires_at:new Date(Date.now()+6048e5).toISOString()}).select().single();if(d)return console.error("Error creating invitation code:",d),n.NextResponse.json({error:"Failed to create invitation code"},{status:500});return n.NextResponse.json({code:p.code,expires_at:p.expires_at,message:"Invitation code generated successfully"})}catch(e){return console.error("Error in generate-code API:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/couples/generate-code/route",pathname:"/api/couples/generate-code",filename:"route",bundlePath:"app/api/couples/generate-code/route"},resolvedPagePath:"/Users/<USER>/Nextjs/marriage-map/src/app/api/couples/generate-code/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:l,staticGenerationAsyncStorage:x,serverHooks:g}=d,q="/api/couples/generate-code/route";function m(){return(0,a.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:x})}},65655:(e,t,r)=>{r.d(t,{e:()=>i});var o=r(67721),s=r(71615);function i(){let e=(0,s.cookies)();return(0,o.createServerClient)("https://eqghwtejdnzgopmcjlho.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY",{cookies:{get:t=>e.get(t)?.value,set(t,r,o){try{e.set({name:t,value:r,...o})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[948,505,355],()=>r(67632));module.exports=o})();