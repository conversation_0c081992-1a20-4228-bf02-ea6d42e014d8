(()=>{var e={};e.id=743,e.ids=[743],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},55315:e=>{"use strict";e.exports=require("path")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},41696:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>a.a,__next_app__:()=>f,originalPathname:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c}),n(21791),n(61158),n(35866);var r=n(23191),o=n(88716),i=n(37922),a=n.n(i),l=n(95231),s={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>l[e]);n.d(t,s);let c=["",{children:["admin",{children:["questions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,21791)),"/Users/<USER>/Nextjs/marriage-map/src/app/admin/questions/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,61158)),"/Users/<USER>/Nextjs/marriage-map/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["/Users/<USER>/Nextjs/marriage-map/src/app/admin/questions/page.tsx"],d="/admin/questions/page",f={require:n,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/admin/questions/page",pathname:"/admin/questions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},77833:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,12994,23)),Promise.resolve().then(n.t.bind(n,96114,23)),Promise.resolve().then(n.t.bind(n,9727,23)),Promise.resolve().then(n.t.bind(n,79671,23)),Promise.resolve().then(n.t.bind(n,41868,23)),Promise.resolve().then(n.t.bind(n,84759,23))},42896:(e,t,n)=>{Promise.resolve().then(n.bind(n,94750))},40447:(e,t,n)=>{Promise.resolve().then(n.bind(n,80917))},80917:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>rf});var r,o,i,a=n(10326),l=n(17577),s=n(35047),c=n(91664),u=n(41190),d=n(82015),f=n(76812),p=n(60962);function m(e,[t,n]){return Math.min(n,Math.max(t,e))}var h=n(82561),v=n(70545),g=n(48051),y=n(93095),x=n(17124),w=n(45226),b=n(55049),j="dismissableLayer.update",E=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),S=l.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:i,onFocusOutside:s,onInteractOutside:c,onDismiss:u,...d}=e,f=l.useContext(E),[p,m]=l.useState(null),v=p?.ownerDocument??globalThis?.document,[,y]=l.useState({}),x=(0,g.e)(t,e=>m(e)),S=Array.from(f.layers),[R]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),k=S.indexOf(R),P=p?S.indexOf(p):-1,T=f.layersWithOutsidePointerEventsDisabled.size>0,A=P>=k,L=function(e,t=globalThis?.document){let n=(0,b.W)(e),r=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){N("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...f.branches].some(e=>e.contains(t));!A||n||(i?.(e),c?.(e),e.defaultPrevented||u?.())},v),M=function(e,t=globalThis?.document){let n=(0,b.W)(e),r=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!r.current&&N("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...f.branches].some(e=>e.contains(t))||(s?.(e),c?.(e),e.defaultPrevented||u?.())},v);return function(e,t=globalThis?.document){let n=(0,b.W)(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P!==f.layers.size-1||(r?.(e),!e.defaultPrevented&&u&&(e.preventDefault(),u()))},v),l.useEffect(()=>{if(p)return n&&(0===f.layersWithOutsidePointerEventsDisabled.size&&(o=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(p)),f.layers.add(p),C(),()=>{n&&1===f.layersWithOutsidePointerEventsDisabled.size&&(v.body.style.pointerEvents=o)}},[p,v,n,f]),l.useEffect(()=>()=>{p&&(f.layers.delete(p),f.layersWithOutsidePointerEventsDisabled.delete(p),C())},[p,f]),l.useEffect(()=>{let e=()=>y({});return document.addEventListener(j,e),()=>document.removeEventListener(j,e)},[]),(0,a.jsx)(w.WV.div,{...d,ref:x,style:{pointerEvents:T?A?"auto":"none":void 0,...e.style},onFocusCapture:(0,h.M)(e.onFocusCapture,M.onFocusCapture),onBlurCapture:(0,h.M)(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:(0,h.M)(e.onPointerDownCapture,L.onPointerDownCapture)})});function C(){let e=new CustomEvent(j);document.dispatchEvent(e)}function N(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,w.jH)(o,i):o.dispatchEvent(i)}S.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(E),r=l.useRef(null),o=(0,g.e)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,a.jsx)(w.WV.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var R=0;function k(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var P="focusScope.autoFocusOnMount",T="focusScope.autoFocusOnUnmount",A={bubbles:!1,cancelable:!0},L=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[c,u]=l.useState(null),d=(0,b.W)(o),f=(0,b.W)(i),p=l.useRef(null),m=(0,g.e)(t,e=>u(e)),h=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let e=function(e){if(h.paused||!c)return;let t=e.target;c.contains(t)?p.current=t:q(p.current,{select:!0})},t=function(e){if(h.paused||!c)return;let t=e.relatedTarget;null===t||c.contains(t)||q(p.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&q(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,c,h.paused]),l.useEffect(()=>{if(c){O.add(h);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(P,A);c.addEventListener(P,d),c.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(q(r,{select:t}),document.activeElement!==n)return}(M(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&q(c))}return()=>{c.removeEventListener(P,d),setTimeout(()=>{let t=new CustomEvent(T,A);c.addEventListener(T,f),c.dispatchEvent(t),t.defaultPrevented||q(e??document.body,{select:!0}),c.removeEventListener(T,f),O.remove(h)},0)}}},[c,d,f,h]);let v=l.useCallback(e=>{if(!n&&!r||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=M(e);return[D(t,e),D(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&q(i,{select:!0})):(e.preventDefault(),n&&q(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,h.paused]);return(0,a.jsx)(w.WV.div,{tabIndex:-1,...s,ref:m,onKeyDown:v})});function M(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function D(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function q(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}L.displayName="FocusScope";var O=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=I(e,t)).unshift(t)},remove(t){e=I(e,t),e[0]?.resume()}}}();function I(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var W=n(88957);let F=["top","right","bottom","left"],V=Math.min,_=Math.max,H=Math.round,z=Math.floor,B=e=>({x:e,y:e}),K={left:"right",right:"left",bottom:"top",top:"bottom"},U={start:"end",end:"start"};function $(e,t){return"function"==typeof e?e(t):e}function Y(e){return e.split("-")[0]}function Q(e){return e.split("-")[1]}function X(e){return"x"===e?"y":"x"}function Z(e){return"y"===e?"height":"width"}function G(e){return["top","bottom"].includes(Y(e))?"y":"x"}function J(e){return e.replace(/start|end/g,e=>U[e])}function ee(e){return e.replace(/left|right|bottom|top/g,e=>K[e])}function et(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function en(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function er(e,t,n){let r,{reference:o,floating:i}=e,a=G(t),l=X(G(t)),s=Z(l),c=Y(t),u="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[s]/2-i[s]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(Q(t)){case"start":r[l]-=p*(n&&u?-1:1);break;case"end":r[l]+=p*(n&&u?-1:1)}return r}let eo=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),s=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=er(c,r,s),f=r,p={},m=0;for(let n=0;n<l.length;n++){let{name:i,fn:h}=l[n],{x:v,y:g,data:y,reset:x}=await h({x:u,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});u=null!=v?v:u,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},x&&m<=50&&(m++,"object"==typeof x&&(x.placement&&(f=x.placement),x.rects&&(c=!0===x.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):x.rects),{x:u,y:d}=er(c,f,s)),n=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}};async function ei(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:s}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=$(t,e),m=et(p),h=l[f?"floating"===d?"reference":"floating":d],v=en(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:u,strategy:s})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),x=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},w=en(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:y,strategy:s}):g);return{top:(v.top-w.top+m.top)/x.y,bottom:(w.bottom-v.bottom+m.bottom)/x.y,left:(v.left-w.left+m.left)/x.x,right:(w.right-v.right+m.right)/x.x}}function ea(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function el(e){return F.some(t=>e[t]>=0)}async function es(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=Y(n),l=Q(n),s="y"===G(n),c=["left","top"].includes(a)?-1:1,u=i&&s?-1:1,d=$(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof m&&(p="end"===l?-1*m:m),s?{x:p*u,y:f*c}:{x:f*c,y:p*u}}function ec(){return"undefined"!=typeof window}function eu(e){return ep(e)?(e.nodeName||"").toLowerCase():"#document"}function ed(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ef(e){var t;return null==(t=(ep(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ep(e){return!!ec()&&(e instanceof Node||e instanceof ed(e).Node)}function em(e){return!!ec()&&(e instanceof Element||e instanceof ed(e).Element)}function eh(e){return!!ec()&&(e instanceof HTMLElement||e instanceof ed(e).HTMLElement)}function ev(e){return!!ec()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ed(e).ShadowRoot)}function eg(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ej(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function ey(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function ex(e){let t=ew(),n=em(e)?ej(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function ew(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function eb(e){return["html","body","#document"].includes(eu(e))}function ej(e){return ed(e).getComputedStyle(e)}function eE(e){return em(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eS(e){if("html"===eu(e))return e;let t=e.assignedSlot||e.parentNode||ev(e)&&e.host||ef(e);return ev(t)?t.host:t}function eC(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eS(t);return eb(n)?t.ownerDocument?t.ownerDocument.body:t.body:eh(n)&&eg(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=ed(o);if(i){let e=eN(a);return t.concat(a,a.visualViewport||[],eg(o)?o:[],e&&n?eC(e):[])}return t.concat(o,eC(o,[],n))}function eN(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eR(e){let t=ej(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eh(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,l=H(n)!==i||H(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function ek(e){return em(e)?e:e.contextElement}function eP(e){let t=ek(e);if(!eh(t))return B(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eR(t),a=(i?H(n.width):n.width)/r,l=(i?H(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let eT=B(0);function eA(e){let t=ed(e);return ew()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eT}function eL(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=ek(e),l=B(1);t&&(r?em(r)&&(l=eP(r)):l=eP(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===ed(a))&&o)?eA(a):B(0),c=(i.left+s.x)/l.x,u=(i.top+s.y)/l.y,d=i.width/l.x,f=i.height/l.y;if(a){let e=ed(a),t=r&&em(r)?ed(r):r,n=e,o=eN(n);for(;o&&r&&t!==n;){let e=eP(o),t=o.getBoundingClientRect(),r=ej(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,u*=e.y,d*=e.x,f*=e.y,c+=i,u+=a,o=eN(n=ed(o))}}return en({width:d,height:f,x:c,y:u})}function eM(e,t){let n=eE(e).scrollLeft;return t?t.left+n:eL(ef(e)).left+n}function eD(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eM(e,r)),y:r.top+t.scrollTop}}function eq(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ed(e),r=ef(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,s=0;if(o){i=o.width,a=o.height;let e=ew();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,s=o.offsetTop)}return{width:i,height:a,x:l,y:s}}(e,n);else if("document"===t)r=function(e){let t=ef(e),n=eE(e),r=e.ownerDocument.body,o=_(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=_(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+eM(e),l=-n.scrollTop;return"rtl"===ej(r).direction&&(a+=_(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:l}}(ef(e));else if(em(t))r=function(e,t){let n=eL(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=eh(e)?eP(e):B(1),a=e.clientWidth*i.x;return{width:a,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=eA(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return en(r)}function eO(e){return"static"===ej(e).position}function eI(e,t){if(!eh(e)||"fixed"===ej(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ef(e)===n&&(n=n.ownerDocument.body),n}function eW(e,t){let n=ed(e);if(ey(e))return n;if(!eh(e)){let t=eS(e);for(;t&&!eb(t);){if(em(t)&&!eO(t))return t;t=eS(t)}return n}let r=eI(e,t);for(;r&&["table","td","th"].includes(eu(r))&&eO(r);)r=eI(r,t);return r&&eb(r)&&eO(r)&&!ex(r)?n:r||function(e){let t=eS(e);for(;eh(t)&&!eb(t);){if(ex(t))return t;if(ey(t))break;t=eS(t)}return null}(e)||n}let eF=async function(e){let t=this.getOffsetParent||eW,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eh(t),o=ef(t),i="fixed"===n,a=eL(e,!0,i,t),l={scrollLeft:0,scrollTop:0},s=B(0);if(r||!r&&!i){if(("body"!==eu(t)||eg(o))&&(l=eE(t)),r){let e=eL(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eM(o))}let c=!o||r||i?B(0):eD(o,l);return{x:a.left+l.scrollLeft-s.x-c.x,y:a.top+l.scrollTop-s.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eV={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=ef(r),l=!!t&&ey(t.floating);if(r===a||l&&i)return n;let s={scrollLeft:0,scrollTop:0},c=B(1),u=B(0),d=eh(r);if((d||!d&&!i)&&(("body"!==eu(r)||eg(a))&&(s=eE(r)),eh(r))){let e=eL(r);c=eP(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}let f=!a||d||i?B(0):eD(a,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+u.x+f.x,y:n.y*c.y-s.scrollTop*c.y+u.y+f.y}},getDocumentElement:ef,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?ey(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eC(e,[],!1).filter(e=>em(e)&&"body"!==eu(e)),o=null,i="fixed"===ej(e).position,a=i?eS(e):e;for(;em(a)&&!eb(a);){let t=ej(a),n=ex(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||eg(a)&&!n&&function e(t,n){let r=eS(t);return!(r===n||!em(r)||eb(r))&&("fixed"===ej(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=eS(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((e,n)=>{let r=eq(t,n,o);return e.top=_(r.top,e.top),e.right=V(r.right,e.right),e.bottom=V(r.bottom,e.bottom),e.left=_(r.left,e.left),e},eq(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eW,getElementRects:eF,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eR(e);return{width:t,height:n}},getScale:eP,isElement:em,isRTL:function(e){return"rtl"===ej(e).direction}};function e_(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eH=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:a,elements:l,middlewareData:s}=t,{element:c,padding:u=0}=$(e,t)||{};if(null==c)return{};let d=et(u),f={x:n,y:r},p=X(G(o)),m=Z(p),h=await a.getDimensions(c),v="y"===p,g=v?"clientHeight":"clientWidth",y=i.reference[m]+i.reference[p]-f[p]-i.floating[m],x=f[p]-i.reference[p],w=await (null==a.getOffsetParent?void 0:a.getOffsetParent(c)),b=w?w[g]:0;b&&await (null==a.isElement?void 0:a.isElement(w))||(b=l.floating[g]||i.floating[m]);let j=b/2-h[m]/2-1,E=V(d[v?"top":"left"],j),S=V(d[v?"bottom":"right"],j),C=b-h[m]-S,N=b/2-h[m]/2+(y/2-x/2),R=_(E,V(N,C)),k=!s.arrow&&null!=Q(o)&&N!==R&&i.reference[m]/2-(N<E?E:S)-h[m]/2<0,P=k?N<E?N-E:N-C:0;return{[p]:f[p]+P,data:{[p]:R,centerOffset:N-R-P,...k&&{alignmentOffset:P}},reset:k}}}),ez=(e,t,n)=>{let r=new Map,o={platform:eV,...n},i={...o.platform,_c:r};return eo(e,t,{...o,platform:i})};var eB="undefined"!=typeof document?l.useLayoutEffect:l.useEffect;function eK(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eK(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eK(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eU(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e$(e,t){let n=eU(e);return Math.round(t*n)/n}function eY(e){let t=l.useRef(e);return eB(()=>{t.current=e}),t}let eQ=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eH({element:n.current,padding:r}).fn(t):{}:n?eH({element:n,padding:r}).fn(t):{}}}),eX=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,s=await es(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:a}}}}}(e),options:[e,t]}),eZ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=$(e,t),c={x:n,y:r},u=await ei(t,s),d=G(Y(o)),f=X(d),p=c[f],m=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+u[e],r=p-u[t];p=_(n,V(p,r))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=m+u[e],r=m-u[t];m=_(n,V(m,r))}let h=l.fn({...t,[f]:p,[d]:m});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[f]:i,[d]:a}}}}}}(e),options:[e,t]}),eG=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:s=!0,crossAxis:c=!0}=$(e,t),u={x:n,y:r},d=G(o),f=X(d),p=u[f],m=u[d],h=$(l,t),v="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(s){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=["top","left"].includes(Y(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);m<n?m=n:m>r&&(m=r)}return{[f]:p,[d]:m}}}}(e),options:[e,t]}),eJ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:s,rects:c,initialPlacement:u,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:h,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...x}=$(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let w=Y(l),b=G(u),j=Y(u)===u,E=await (null==d.isRTL?void 0:d.isRTL(f.floating)),S=h||(j||!y?[ee(u)]:function(e){let t=ee(e);return[J(e),t,J(t)]}(u)),C="none"!==g;!h&&C&&S.push(...function(e,t,n,r){let o=Q(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(Y(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(J)))),i}(u,y,g,E));let N=[u,...S],R=await ei(t,x),k=[],P=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&k.push(R[w]),m){let e=function(e,t,n){void 0===n&&(n=!1);let r=Q(e),o=X(G(e)),i=Z(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=ee(a)),[a,ee(a)]}(l,c,E);k.push(R[e[0]],R[e[1]])}if(P=[...P,{placement:l,overflows:k}],!k.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=N[e];if(t)return{data:{index:e,overflows:P},reset:{placement:t}};let n=null==(i=P.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=P.filter(e=>{if(C){let t=G(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=u}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),e0=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i;let{placement:a,rects:l,platform:s,elements:c}=t,{apply:u=()=>{},...d}=$(e,t),f=await ei(t,d),p=Y(a),m=Q(a),h="y"===G(a),{width:v,height:g}=l.floating;"top"===p||"bottom"===p?(o=p,i=m===(await (null==s.isRTL?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===m?"top":"bottom");let y=g-f.top-f.bottom,x=v-f.left-f.right,w=V(g-f[o],y),b=V(v-f[i],x),j=!t.middlewareData.shift,E=w,S=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(S=x),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(E=y),j&&!m){let e=_(f.left,0),t=_(f.right,0),n=_(f.top,0),r=_(f.bottom,0);h?S=v-2*(0!==e||0!==t?e+t:_(f.left,f.right)):E=g-2*(0!==n||0!==r?n+r:_(f.top,f.bottom))}await u({...t,availableWidth:S,availableHeight:E});let C=await s.getDimensions(c.floating);return v!==C.width||g!==C.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),e1=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=$(e,t);switch(r){case"referenceHidden":{let e=ea(await ei(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:el(e)}}}case"escaped":{let e=ea(await ei(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:el(e)}}}default:return{}}}}}(e),options:[e,t]}),e2=(e,t)=>({...eQ(e),options:[e,t]});var e5=l.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,a.jsx)(w.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,a.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e5.displayName="Arrow";var e7=n(65819),e3=n(2566),e4="Popper",[e9,e6]=(0,y.b)(e4),[e8,te]=e9(e4),tt=e=>{let{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return(0,a.jsx)(e8,{scope:t,anchor:r,onAnchorChange:o,children:n})};tt.displayName=e4;var tn="PopperAnchor",tr=l.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=te(tn,n),s=l.useRef(null),c=(0,g.e)(t,s);return l.useEffect(()=>{i.onAnchorChange(r?.current||s.current)}),r?null:(0,a.jsx)(w.WV.div,{...o,ref:c})});tr.displayName=tn;var to="PopperContent",[ti,ta]=e9(to),tl=l.forwardRef((e,t)=>{let{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:c=0,avoidCollisions:u=!0,collisionBoundary:d=[],collisionPadding:f=0,sticky:m="partial",hideWhenDetached:h=!1,updatePositionStrategy:v="optimized",onPlaced:y,...x}=e,j=te(to,n),[E,S]=l.useState(null),C=(0,g.e)(t,e=>S(e)),[N,R]=l.useState(null),k=(0,e3.t)(N),P=k?.width??0,T=k?.height??0,A="number"==typeof f?f:{top:0,right:0,bottom:0,left:0,...f},L=Array.isArray(d)?d:[d],M=L.length>0,D={padding:A,boundary:L.filter(td),altBoundary:M},{refs:q,floatingStyles:O,placement:I,isPositioned:W,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:a}={},transform:s=!0,whileElementsMounted:c,open:u}=e,[d,f]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,h]=l.useState(r);eK(m,r)||h(r);let[v,g]=l.useState(null),[y,x]=l.useState(null),w=l.useCallback(e=>{e!==S.current&&(S.current=e,g(e))},[]),b=l.useCallback(e=>{e!==C.current&&(C.current=e,x(e))},[]),j=i||v,E=a||y,S=l.useRef(null),C=l.useRef(null),N=l.useRef(d),R=null!=c,k=eY(c),P=eY(o),T=eY(u),A=l.useCallback(()=>{if(!S.current||!C.current)return;let e={placement:t,strategy:n,middleware:m};P.current&&(e.platform=P.current),ez(S.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};L.current&&!eK(N.current,t)&&(N.current=t,p.flushSync(()=>{f(t)}))})},[m,t,n,P,T]);eB(()=>{!1===u&&N.current.isPositioned&&(N.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[u]);let L=l.useRef(!1);eB(()=>(L.current=!0,()=>{L.current=!1}),[]),eB(()=>{if(j&&(S.current=j),E&&(C.current=E),j&&E){if(k.current)return k.current(j,E,A);A()}},[j,E,A,k,R]);let M=l.useMemo(()=>({reference:S,floating:C,setReference:w,setFloating:b}),[w,b]),D=l.useMemo(()=>({reference:j,floating:E}),[j,E]),q=l.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=e$(D.floating,d.x),r=e$(D.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...eU(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,D.floating,d.x,d.y]);return l.useMemo(()=>({...d,update:A,refs:M,elements:D,floatingStyles:q}),[d,A,M,D,q])}({strategy:"fixed",placement:r+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,u=ek(e),d=i||a?[...u?eC(u):[],...eC(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let f=u&&s?function(e,t){let n,r=null,o=ef(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function a(l,s){void 0===l&&(l=!1),void 0===s&&(s=1),i();let c=e.getBoundingClientRect(),{left:u,top:d,width:f,height:p}=c;if(l||t(),!f||!p)return;let m=z(d),h=z(o.clientWidth-(u+f)),v={rootMargin:-m+"px "+-h+"px "+-z(o.clientHeight-(d+p))+"px "+-z(u)+"px",threshold:_(0,V(1,s))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==s){if(!g)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||e_(c,e.getBoundingClientRect())||a(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(u,n):null,p=-1,m=null;l&&(m=new ResizeObserver(e=>{let[r]=e;r&&r.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=m)||e.observe(t)})),n()}),u&&!c&&m.observe(u),m.observe(t));let h=c?eL(e):null;return c&&function t(){let r=eL(e);h&&!e_(h,r)&&n(),h=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=m)||e.disconnect(),m=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===v}),elements:{reference:j.anchor},middleware:[eX({mainAxis:o+T,alignmentAxis:s}),u&&eZ({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?eG():void 0,...D}),u&&eJ({...D}),e0({...D,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),N&&e2({element:N,padding:c}),tf({arrowWidth:P,arrowHeight:T}),h&&e1({strategy:"referenceHidden",...D})]}),[H,B]=tp(I),K=(0,b.W)(y);(0,e7.b)(()=>{W&&K?.()},[W,K]);let U=F.arrow?.x,$=F.arrow?.y,Y=F.arrow?.centerOffset!==0,[Q,X]=l.useState();return(0,e7.b)(()=>{E&&X(window.getComputedStyle(E).zIndex)},[E]),(0,a.jsx)("div",{ref:q.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:W?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Q,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,a.jsx)(ti,{scope:n,placedSide:H,onArrowChange:R,arrowX:U,arrowY:$,shouldHideArrow:Y,children:(0,a.jsx)(w.WV.div,{"data-side":H,"data-align":B,...x,ref:C,style:{...x.style,animation:W?void 0:"none"}})})})});tl.displayName=to;var ts="PopperArrow",tc={top:"bottom",right:"left",bottom:"top",left:"right"},tu=l.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ta(ts,n),i=tc[o.placedSide];return(0,a.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,a.jsx)(e5,{...r,ref:t,style:{...r.style,display:"block"}})})});function td(e){return null!==e}tu.displayName=ts;var tf=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[s,c]=tp(n),u={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",m="";return"bottom"===s?(p=i?u:`${d}px`,m=`${-l}px`):"top"===s?(p=i?u:`${d}px`,m=`${r.floating.height+l}px`):"right"===s?(p=`${-l}px`,m=i?u:`${f}px`):"left"===s&&(p=`${r.floating.width+l}px`,m=i?u:`${f}px`),{data:{x:p,y:m}}}});function tp(e){let[t,n="center"]=e.split("-");return[t,n]}var tm=l.forwardRef((e,t)=>{let{container:n,...r}=e,[o,i]=l.useState(!1);(0,e7.b)(()=>i(!0),[]);let s=n||o&&globalThis?.document?.body;return s?p.createPortal((0,a.jsx)(w.WV.div,{...r,ref:t}),s):null});tm.displayName="Portal";var th=n(34214),tv=n(52067),tg=n(53405),ty=l.forwardRef((e,t)=>(0,a.jsx)(w.WV.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));ty.displayName="VisuallyHidden";var tx=new WeakMap,tw=new WeakMap,tb={},tj=0,tE=function(e){return e&&(e.host||tE(e.parentNode))},tS=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tE(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tb[n]||(tb[n]=new WeakMap);var i=tb[n],a=[],l=new Set,s=new Set(o),c=function(e){!e||l.has(e)||(l.add(e),c(e.parentNode))};o.forEach(c);var u=function(e){!e||s.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))u(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,s=(tx.get(e)||0)+1,c=(i.get(e)||0)+1;tx.set(e,s),i.set(e,c),a.push(e),1===s&&o&&tw.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return u(t),l.clear(),tj++,function(){a.forEach(function(e){var t=tx.get(e)-1,o=i.get(e)-1;tx.set(e,t),i.set(e,o),t||(tw.has(e)||e.removeAttribute(r),tw.delete(e)),o||e.removeAttribute(n)}),--tj||(tx=new WeakMap,tx=new WeakMap,tw=new WeakMap,tb={})}},tC=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r,o=Array.from(Array.isArray(e)?e:[e]),i=t||(r=e,"undefined"==typeof document?null:(Array.isArray(r)?r[0]:r).ownerDocument.body);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),tS(o,i,n,"aria-hidden")):function(){return null}},tN=function(){return(tN=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tR(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create,Object.create;var tk=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tP="width-before-scroll-bar";function tT(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tA="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,tL=new WeakMap;function tM(e){return e}var tD=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=tM),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}});return o.options=tN({async:!0,ssr:!1},e),o}(),tq=function(){},tO=l.forwardRef(function(e,t){var n,r,o,i,a=l.useRef(null),s=l.useState({onScrollCapture:tq,onWheelCapture:tq,onTouchMoveCapture:tq}),c=s[0],u=s[1],d=e.forwardProps,f=e.children,p=e.className,m=e.removeScrollBar,h=e.enabled,v=e.shards,g=e.sideCar,y=e.noIsolation,x=e.inert,w=e.allowPinchZoom,b=e.as,j=e.gapMode,E=tR(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(n=[a,t],r=function(e){return n.forEach(function(t){return tT(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tA(function(){var e=tL.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||tT(e,null)}),r.forEach(function(e){t.has(e)||tT(e,o)})}tL.set(i,n)},[n]),i),C=tN(tN({},E),c);return l.createElement(l.Fragment,null,h&&l.createElement(g,{sideCar:tD,removeScrollBar:m,shards:v,noIsolation:y,inert:x,setCallbacks:u,allowPinchZoom:!!w,lockRef:a,gapMode:j}),d?l.cloneElement(l.Children.only(f),tN(tN({},C),{ref:S})):l.createElement(void 0===b?"div":b,tN({},C,{className:p,ref:S}),f))});tO.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tO.classNames={fullWidth:tP,zeroRight:tk};var tI=function(e){var t=e.sideCar,n=tR(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,tN({},n))};tI.isSideCarExport=!0;var tW=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tF=function(){var e=tW();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},tV=function(){var e=tF();return function(t){return e(t.styles,t.dynamic),null}},t_={left:0,top:0,right:0,gap:0},tH=function(e){return parseInt(e||"",10)||0},tz=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[tH(n),tH(r),tH(o)]},tB=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return t_;var t=tz(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},tK=tV(),tU="data-scroll-locked",t$=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(tU,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tk," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(tP," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(tk," .").concat(tk," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tP," .").concat(tP," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(tU,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},tY=function(){var e=parseInt(document.body.getAttribute(tU)||"0",10);return isFinite(e)?e:0},tQ=function(){l.useEffect(function(){return document.body.setAttribute(tU,(tY()+1).toString()),function(){var e=tY()-1;e<=0?document.body.removeAttribute(tU):document.body.setAttribute(tU,e.toString())}},[])},tX=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;tQ();var i=l.useMemo(function(){return tB(o)},[o]);return l.createElement(tK,{styles:t$(i,!t,o,n?"":"!important")})},tZ=!1;if("undefined"!=typeof window)try{var tG=Object.defineProperty({},"passive",{get:function(){return tZ=!0,!0}});window.addEventListener("test",tG,tG),window.removeEventListener("test",tG,tG)}catch(e){tZ=!1}var tJ=!!tZ&&{passive:!1},t0=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},t1=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),t2(e,r)){var o=t5(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},t2=function(e,t){return"v"===e?t0(t,"overflowY"):t0(t,"overflowX")},t5=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},t7=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,s=n.target,c=t.contains(s),u=!1,d=l>0,f=0,p=0;do{var m=t5(e,s),h=m[0],v=m[1]-m[2]-a*h;(h||v)&&t2(e,s)&&(f+=v,p+=h),s instanceof ShadowRoot?s=s.host:s=s.parentNode}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&l>f)?u=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(u=!0),u},t3=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},t4=function(e){return[e.deltaX,e.deltaY]},t9=function(e){return e&&"current"in e?e.current:e},t6=0,t8=[];let ne=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(t6++)[0],i=l.useState(tV)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(t9),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=t3(e),l=n.current,s="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],u=e.target,d=Math.abs(s)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=t1(d,u);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=t1(d,u)),!f)return!1;if(!r.current&&"changedTouches"in e&&(s||c)&&(r.current=o),!o)return!0;var p=r.current||o;return t7(p,t,e,"h"===p?s:c,!0)},[]),c=l.useCallback(function(e){if(t8.length&&t8[t8.length-1]===i){var n="deltaY"in e?t4(e):t3(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(t9).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),u=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=t3(e),r.current=void 0},[]),f=l.useCallback(function(t){u(t.type,t4(t),t.target,s(t,e.lockRef.current))},[]),p=l.useCallback(function(t){u(t.type,t3(t),t.target,s(t,e.lockRef.current))},[]);l.useEffect(function(){return t8.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,tJ),document.addEventListener("touchmove",c,tJ),document.addEventListener("touchstart",d,tJ),function(){t8=t8.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,tJ),document.removeEventListener("touchmove",c,tJ),document.removeEventListener("touchstart",d,tJ)}},[]);var m=e.removeScrollBar,h=e.inert;return l.createElement(l.Fragment,null,h?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?l.createElement(tX,{gapMode:e.gapMode}):null)},tD.useMedium(r),tI);var nt=l.forwardRef(function(e,t){return l.createElement(tO,tN({},e,{ref:t,sideCar:ne}))});nt.classNames=tO.classNames;var nn=[" ","Enter","ArrowUp","ArrowDown"],nr=[" ","Enter"],no="Select",[ni,na,nl]=(0,v.B)(no),[ns,nc]=(0,y.b)(no,[nl,e6]),nu=e6(),[nd,nf]=ns(no),[np,nm]=ns(no),nh=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:s,defaultValue:c,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:m,required:h,form:v}=e,g=nu(t),[y,w]=l.useState(null),[b,j]=l.useState(null),[E,S]=l.useState(!1),C=(0,x.gm)(d),[N=!1,R]=(0,tv.T)({prop:r,defaultProp:o,onChange:i}),[k,P]=(0,tv.T)({prop:s,defaultProp:c,onChange:u}),T=l.useRef(null),A=!y||v||!!y.closest("form"),[L,M]=l.useState(new Set),D=Array.from(L).map(e=>e.props.value).join(";");return(0,a.jsx)(tt,{...g,children:(0,a.jsxs)(nd,{required:h,scope:t,trigger:y,onTriggerChange:w,valueNode:b,onValueNodeChange:j,valueNodeHasChildren:E,onValueNodeHasChildrenChange:S,contentId:(0,W.M)(),value:k,onValueChange:P,open:N,onOpenChange:R,dir:C,triggerPointerDownPosRef:T,disabled:m,children:[(0,a.jsx)(ni.Provider,{scope:t,children:(0,a.jsx)(np,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{M(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{M(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),A?(0,a.jsxs)(nG,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:p,value:k,onChange:e=>P(e.target.value),disabled:m,form:v,children:[void 0===k?(0,a.jsx)("option",{value:""}):null,Array.from(L)]},D):null]})})};nh.displayName=no;var nv="SelectTrigger",ng=l.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=nu(n),s=nf(nv,n),c=s.disabled||r,u=(0,g.e)(t,s.onTriggerChange),d=na(n),f=l.useRef("touch"),[p,m,v]=nJ(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=n0(t,e,n);void 0!==r&&s.onValueChange(r.value)}),y=e=>{c||(s.onOpenChange(!0),v()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,a.jsx)(tr,{asChild:!0,...i,children:(0,a.jsx)(w.WV.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":nZ(s.value)?"":void 0,...o,ref:u,onClick:(0,h.M)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:(0,h.M)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,h.M)(o.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&nn.includes(e.key)&&(y(),e.preventDefault())})})})});ng.displayName=nv;var ny="SelectValue",nx=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...s}=e,c=nf(ny,n),{onValueNodeHasChildrenChange:u}=c,d=void 0!==i,f=(0,g.e)(t,c.onValueNodeChange);return(0,e7.b)(()=>{u(d)},[u,d]),(0,a.jsx)(w.WV.span,{...s,ref:f,style:{pointerEvents:"none"},children:nZ(c.value)?(0,a.jsx)(a.Fragment,{children:l}):i})});nx.displayName=ny;var nw=l.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,a.jsx)(w.WV.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nw.displayName="SelectIcon";var nb=e=>(0,a.jsx)(tm,{asChild:!0,...e});nb.displayName="SelectPortal";var nj="SelectContent",nE=l.forwardRef((e,t)=>{let n=nf(nj,e.__scopeSelect),[r,o]=l.useState();return((0,e7.b)(()=>{o(new DocumentFragment)},[]),n.open)?(0,a.jsx)(nN,{...e,ref:t}):r?p.createPortal((0,a.jsx)(nS,{scope:e.__scopeSelect,children:(0,a.jsx)(ni.Slot,{scope:e.__scopeSelect,children:(0,a.jsx)("div",{children:e.children})})}),r):null});nE.displayName=nj;var[nS,nC]=ns(nj),nN=l.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:s,side:c,sideOffset:u,align:d,alignOffset:f,arrowPadding:p,collisionBoundary:m,collisionPadding:v,sticky:y,hideWhenDetached:x,avoidCollisions:w,...b}=e,j=nf(nj,n),[E,C]=l.useState(null),[N,P]=l.useState(null),T=(0,g.e)(t,e=>C(e)),[A,M]=l.useState(null),[D,q]=l.useState(null),O=na(n),[I,W]=l.useState(!1),F=l.useRef(!1);l.useEffect(()=>{if(E)return tC(E)},[E]),l.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??k()),document.body.insertAdjacentElement("beforeend",e[1]??k()),R++,()=>{1===R&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),R--}},[]);let V=l.useCallback(e=>{let[t,...n]=O().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&N&&(N.scrollTop=0),n===r&&N&&(N.scrollTop=N.scrollHeight),n?.focus(),document.activeElement!==o))return},[O,N]),_=l.useCallback(()=>V([A,E]),[V,A,E]);l.useEffect(()=>{I&&_()},[I,_]);let{onOpenChange:H,triggerPointerDownPosRef:z}=j;l.useEffect(()=>{if(E){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(z.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():E.contains(n.target)||H(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[E,H,z]),l.useEffect(()=>{let e=()=>H(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[H]);let[B,K]=nJ(e=>{let t=O().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=n0(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),U=l.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==j.value&&j.value===t||r)&&(M(e),r&&(F.current=!0))},[j.value]),$=l.useCallback(()=>E?.focus(),[E]),Y=l.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==j.value&&j.value===t||r)&&q(e)},[j.value]),Q="popper"===r?nk:nR,X=Q===nk?{side:c,sideOffset:u,align:d,alignOffset:f,arrowPadding:p,collisionBoundary:m,collisionPadding:v,sticky:y,hideWhenDetached:x,avoidCollisions:w}:{};return(0,a.jsx)(nS,{scope:n,content:E,viewport:N,onViewportChange:P,itemRefCallback:U,selectedItem:A,onItemLeave:$,itemTextRefCallback:Y,focusSelectedItem:_,selectedItemText:D,position:r,isPositioned:I,searchRef:B,children:(0,a.jsx)(nt,{as:th.g7,allowPinchZoom:!0,children:(0,a.jsx)(L,{asChild:!0,trapped:j.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,h.M)(o,e=>{j.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,a.jsx)(S,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>j.onOpenChange(!1),children:(0,a.jsx)(Q,{role:"listbox",id:j.contentId,"data-state":j.open?"open":"closed",dir:j.dir,onContextMenu:e=>e.preventDefault(),...b,...X,onPlaced:()=>W(!0),ref:T,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:(0,h.M)(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||K(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>V(t)),e.preventDefault()}})})})})})})});nN.displayName="SelectContentImpl";var nR=l.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=nf(nj,n),s=nC(nj,n),[c,u]=l.useState(null),[d,f]=l.useState(null),p=(0,g.e)(t,e=>f(e)),h=na(n),v=l.useRef(!1),y=l.useRef(!0),{viewport:x,selectedItem:b,selectedItemText:j,focusSelectedItem:E}=s,S=l.useCallback(()=>{if(i.trigger&&i.valueNode&&c&&d&&x&&b&&j){let e=i.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=j.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,i=n.left-r,a=e.left-i,l=e.width+a,s=Math.max(l,t.width),u=m(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.left=u+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,a=window.innerWidth-e.right-i,l=e.width+a,s=Math.max(l,t.width),u=m(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.right=u+"px"}let a=h(),l=window.innerHeight-20,s=x.scrollHeight,u=window.getComputedStyle(d),f=parseInt(u.borderTopWidth,10),p=parseInt(u.paddingTop,10),g=parseInt(u.borderBottomWidth,10),y=f+p+s+parseInt(u.paddingBottom,10)+g,w=Math.min(5*b.offsetHeight,y),E=window.getComputedStyle(x),S=parseInt(E.paddingTop,10),C=parseInt(E.paddingBottom,10),N=e.top+e.height/2-10,R=b.offsetHeight/2,k=f+p+(b.offsetTop+R);if(k<=N){let e=a.length>0&&b===a[a.length-1].ref.current;c.style.bottom="0px";let t=d.clientHeight-x.offsetTop-x.offsetHeight;c.style.height=k+Math.max(l-N,R+(e?C:0)+t+g)+"px"}else{let e=a.length>0&&b===a[0].ref.current;c.style.top="0px";let t=Math.max(N,f+x.offsetTop+(e?S:0)+R);c.style.height=t+(y-k)+"px",x.scrollTop=k-N+x.offsetTop}c.style.margin="10px 0",c.style.minHeight=w+"px",c.style.maxHeight=l+"px",r?.(),requestAnimationFrame(()=>v.current=!0)}},[h,i.trigger,i.valueNode,c,d,x,b,j,i.dir,r]);(0,e7.b)(()=>S(),[S]);let[C,N]=l.useState();(0,e7.b)(()=>{d&&N(window.getComputedStyle(d).zIndex)},[d]);let R=l.useCallback(e=>{e&&!0===y.current&&(S(),E?.(),y.current=!1)},[S,E]);return(0,a.jsx)(nP,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:R,children:(0,a.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:(0,a.jsx)(w.WV.div,{...o,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});nR.displayName="SelectItemAlignedPosition";var nk=l.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=nu(n);return(0,a.jsx)(tl,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nk.displayName="SelectPopperPosition";var[nP,nT]=ns(nj,{}),nA="SelectViewport",nL=l.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=nC(nA,n),s=nT(nA,n),c=(0,g.e)(t,i.onViewportChange),u=l.useRef(0);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,a.jsx)(ni.Slot,{scope:n,children:(0,a.jsx)(w.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,h.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if(r?.current&&n){let e=Math.abs(u.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,a=Math.min(r,i),l=i-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=l>0?l:0,n.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});nL.displayName=nA;var nM="SelectGroup",[nD,nq]=ns(nM);l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,W.M)();return(0,a.jsx)(nD,{scope:n,id:o,children:(0,a.jsx)(w.WV.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=nM;var nO="SelectLabel",nI=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nq(nO,n);return(0,a.jsx)(w.WV.div,{id:o.id,...r,ref:t})});nI.displayName=nO;var nW="SelectItem",[nF,nV]=ns(nW),n_=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...s}=e,c=nf(nW,n),u=nC(nW,n),d=c.value===r,[f,p]=l.useState(i??""),[m,v]=l.useState(!1),y=(0,g.e)(t,e=>u.itemRefCallback?.(e,r,o)),x=(0,W.M)(),b=l.useRef("touch"),j=()=>{o||(c.onValueChange(r),c.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,a.jsx)(nF,{scope:n,value:r,disabled:o,textId:x,isSelected:d,onItemTextChange:l.useCallback(e=>{p(t=>t||(e?.textContent??"").trim())},[]),children:(0,a.jsx)(ni.ItemSlot,{scope:n,value:r,disabled:o,textValue:f,children:(0,a.jsx)(w.WV.div,{role:"option","aria-labelledby":x,"data-highlighted":m?"":void 0,"aria-selected":d&&m,"data-state":d?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...s,ref:y,onFocus:(0,h.M)(s.onFocus,()=>v(!0)),onBlur:(0,h.M)(s.onBlur,()=>v(!1)),onClick:(0,h.M)(s.onClick,()=>{"mouse"!==b.current&&j()}),onPointerUp:(0,h.M)(s.onPointerUp,()=>{"mouse"===b.current&&j()}),onPointerDown:(0,h.M)(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,h.M)(s.onPointerMove,e=>{b.current=e.pointerType,o?u.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,h.M)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&u.onItemLeave?.()}),onKeyDown:(0,h.M)(s.onKeyDown,e=>{u.searchRef?.current!==""&&" "===e.key||(nr.includes(e.key)&&j()," "===e.key&&e.preventDefault())})})})})});n_.displayName=nW;var nH="SelectItemText",nz=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,s=nf(nH,n),c=nC(nH,n),u=nV(nH,n),d=nm(nH,n),[f,m]=l.useState(null),h=(0,g.e)(t,e=>m(e),u.onItemTextChange,e=>c.itemTextRefCallback?.(e,u.value,u.disabled)),v=f?.textContent,y=l.useMemo(()=>(0,a.jsx)("option",{value:u.value,disabled:u.disabled,children:v},u.value),[u.disabled,u.value,v]),{onNativeOptionAdd:x,onNativeOptionRemove:b}=d;return(0,e7.b)(()=>(x(y),()=>b(y)),[x,b,y]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(w.WV.span,{id:u.textId,...i,ref:h}),u.isSelected&&s.valueNode&&!s.valueNodeHasChildren?p.createPortal(i.children,s.valueNode):null]})});nz.displayName=nH;var nB="SelectItemIndicator",nK=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return nV(nB,n).isSelected?(0,a.jsx)(w.WV.span,{"aria-hidden":!0,...r,ref:t}):null});nK.displayName=nB;var nU="SelectScrollUpButton";l.forwardRef((e,t)=>{let n=nC(nU,e.__scopeSelect),r=nT(nU,e.__scopeSelect),[o,i]=l.useState(!1),s=(0,g.e)(t,r.onScrollButtonChange);return(0,e7.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,a.jsx)(nY,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}).displayName=nU;var n$="SelectScrollDownButton";l.forwardRef((e,t)=>{let n=nC(n$,e.__scopeSelect),r=nT(n$,e.__scopeSelect),[o,i]=l.useState(!1),s=(0,g.e)(t,r.onScrollButtonChange);return(0,e7.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,a.jsx)(nY,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}).displayName=n$;var nY=l.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=nC("SelectScrollButton",n),s=l.useRef(null),c=na(n),u=l.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return l.useEffect(()=>()=>u(),[u]),(0,e7.b)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,a.jsx)(w.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,h.M)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(r,50))}),onPointerMove:(0,h.M)(o.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(r,50))}),onPointerLeave:(0,h.M)(o.onPointerLeave,()=>{u()})})}),nQ=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,a.jsx)(w.WV.div,{"aria-hidden":!0,...r,ref:t})});nQ.displayName="SelectSeparator";var nX="SelectArrow";function nZ(e){return""===e||void 0===e}l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nu(n),i=nf(nX,n),l=nC(nX,n);return i.open&&"popper"===l.position?(0,a.jsx)(tu,{...o,...r,ref:t}):null}).displayName=nX;var nG=l.forwardRef((e,t)=>{let{value:n,...r}=e,o=l.useRef(null),i=(0,g.e)(t,o),s=(0,tg.D)(n);return l.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==n&&t){let r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[s,n]),(0,a.jsx)(ty,{asChild:!0,children:(0,a.jsx)("select",{...r,ref:i,defaultValue:n})})});function nJ(e){let t=(0,b.W)(e),n=l.useRef(""),r=l.useRef(0),o=l.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=l.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function n0(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let a=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}nG.displayName="BubbleSelect";var n1=n(51223);let n2=l.forwardRef(({className:e,children:t,...n},r)=>(0,a.jsxs)(ng,{ref:r,className:(0,n1.cn)("flex h-9 w-full items-center justify-between rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),...n,children:[t,a.jsx(nw,{asChild:!0,children:a.jsx(f.jnn,{className:"h-4 w-4 opacity-50"})})]}));n2.displayName=ng.displayName;let n5=l.forwardRef(({className:e,children:t,position:n="popper",...r},o)=>a.jsx(nb,{children:a.jsx(nE,{ref:o,className:(0,n1.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:a.jsx(nL,{className:(0,n1.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t})})}));n5.displayName=nE.displayName,l.forwardRef(({className:e,...t},n)=>a.jsx(nI,{ref:n,className:(0,n1.cn)("px-2 py-1.5 text-sm font-semibold",e),...t})).displayName=nI.displayName;let n7=l.forwardRef(({className:e,children:t,...n},r)=>(0,a.jsxs)(n_,{ref:r,className:(0,n1.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[a.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(nK,{children:a.jsx(f.nQG,{className:"h-4 w-4"})})}),a.jsx(nz,{children:t})]}));n7.displayName=n_.displayName,l.forwardRef(({className:e,...t},n)=>a.jsx(nQ,{ref:n,className:(0,n1.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=nQ.displayName;var n3=n(9815),n4="Checkbox",[n9,n6]=(0,y.b)(n4),[n8,re]=n9(n4),rt=l.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:o,defaultChecked:i,required:s,disabled:c,value:u="on",onCheckedChange:d,form:f,...p}=e,[m,v]=l.useState(null),y=(0,g.e)(t,e=>v(e)),x=l.useRef(!1),b=!m||f||!!m.closest("form"),[j=!1,E]=(0,tv.T)({prop:o,defaultProp:i,onChange:d}),S=l.useRef(j);return l.useEffect(()=>{let e=m?.form;if(e){let t=()=>E(S.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[m,E]),(0,a.jsxs)(n8,{scope:n,state:j,disabled:c,children:[(0,a.jsx)(w.WV.button,{type:"button",role:"checkbox","aria-checked":ri(j)?"mixed":j,"aria-required":s,"data-state":ra(j),"data-disabled":c?"":void 0,disabled:c,value:u,...p,ref:y,onKeyDown:(0,h.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,h.M)(e.onClick,e=>{E(e=>!!ri(e)||!e),b&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})}),b&&(0,a.jsx)(ro,{control:m,bubbles:!x.current,name:r,value:u,checked:j,required:s,disabled:c,form:f,style:{transform:"translateX(-100%)"},defaultChecked:!ri(i)&&i})]})});rt.displayName=n4;var rn="CheckboxIndicator",rr=l.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,i=re(rn,n);return(0,a.jsx)(n3.z,{present:r||ri(i.state)||!0===i.state,children:(0,a.jsx)(w.WV.span,{"data-state":ra(i.state),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});rr.displayName=rn;var ro=e=>{let{control:t,checked:n,bubbles:r=!0,defaultChecked:o,...i}=e,s=l.useRef(null),c=(0,tg.D)(n),u=(0,e3.t)(t);l.useEffect(()=>{let e=s.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==n&&t){let o=new Event("click",{bubbles:r});e.indeterminate=ri(n),t.call(e,!ri(n)&&n),e.dispatchEvent(o)}},[c,n,r]);let d=l.useRef(!ri(n)&&n);return(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o??d.current,...i,tabIndex:-1,ref:s,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function ri(e){return"indeterminate"===e}function ra(e){return ri(e)?"indeterminate":e?"checked":"unchecked"}let rl=l.forwardRef(({className:e,...t},n)=>a.jsx(rt,{ref:n,className:(0,n1.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:a.jsx(rr,{className:(0,n1.cn)("flex items-center justify-center text-current"),children:a.jsx(f.nQG,{className:"h-4 w-4"})})}));rl.displayName=rt.displayName;var rs=n(44794),rc=n(29752),ru=n(99744),rd=n(48880);function rf(){(0,s.useRouter)();let[e,t]=(0,l.useState)({}),[n,r]=(0,l.useState)(!0),[o,i]=(0,l.useState)(null),[f,p]=(0,l.useState)("finances"),[m,h]=(0,l.useState)(!1),[v,g]=(0,l.useState)(null),[y,x]=(0,l.useState)({id:"",domain:"finances",type:"multiple-choice",text:"",options:["","","",""],required:!0}),w=async()=>{try{r(!0),i(null);let e=await (0,rd.xz)();t(e)}catch(e){console.error("Error loading questions:",e),i("Failed to load questions")}finally{r(!1)}},b=(e,t)=>{x({...y,[e]:t})},j=(e,t)=>{let n=[...y.options];n[e]=t,x({...y,options:n})},E=e=>{let t=y.options.filter((t,n)=>n!==e);x({...y,options:t})},S=e=>{g(e),x({id:e.id,domain:e.domain,type:e.type,text:e.text,options:e.options||["","","",""],required:e.required||!1})},C=async()=>{try{if(!y.id||!y.text||!y.domain){i("Please fill in all required fields");return}if(["multiple-choice","scenario","ranking"].includes(y.type)&&(!y.options||y.options.filter(e=>e.trim()).length<2)){i("Please provide at least 2 options");return}let e={id:y.id,domain:y.domain,type:y.type,text:y.text,options:["multiple-choice","scenario","ranking"].includes(y.type)?y.options.filter(e=>e.trim()):void 0,required:y.required};v?await (0,rd.Ds)(e):await (0,rd.xL)(e),R(),await w()}catch(e){console.error("Error saving question:",e),i("Failed to save question")}},N=async e=>{if(confirm("Are you sure you want to delete this question?"))try{await (0,rd.Km)(e),await w()}catch(e){console.error("Error deleting question:",e),i("Failed to delete question")}},R=()=>{g(null),x({id:"",domain:f,type:"multiple-choice",text:"",options:["","","",""],required:!0}),i(null)};return m?(0,a.jsxs)("div",{className:"container py-8",children:[a.jsx("h1",{className:"text-3xl font-bold mb-6",children:"Assessment Questions Management"}),o&&a.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:o}),(0,a.jsxs)(ru.mQ,{defaultValue:"view",className:"w-full",children:[(0,a.jsxs)(ru.dr,{className:"mb-4",children:[a.jsx(ru.SP,{value:"view",children:"View Questions"}),a.jsx(ru.SP,{value:"add",children:"Add/Edit Question"})]}),a.jsx(ru.nU,{value:"view",children:(0,a.jsxs)(rc.Zb,{children:[(0,a.jsxs)(rc.Ol,{children:[a.jsx(rc.ll,{children:"Assessment Questions"}),a.jsx(rc.SZ,{children:"Select a domain to view its questions"}),(0,a.jsxs)(nh,{value:f,onValueChange:p,children:[a.jsx(n2,{className:"w-[180px]",children:a.jsx(nx,{placeholder:"Select domain"})}),a.jsx(n5,{children:Object.keys(e).map(e=>a.jsx(n7,{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))})]})]}),a.jsx(rc.aY,{children:n?a.jsx("p",{children:"Loading questions..."}):e[f]&&e[f].length>0?a.jsx("div",{className:"space-y-4",children:e[f].map(e=>(0,a.jsxs)(rc.Zb,{className:"border border-gray-200",children:[(0,a.jsxs)(rc.Ol,{className:"pb-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[a.jsx(rc.ll,{className:"text-lg",children:e.text}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx(c.z,{variant:"outline",size:"sm",onClick:()=>S(e),children:"Edit"}),a.jsx(c.z,{variant:"destructive",size:"sm",onClick:()=>N(e.id),children:"Delete"})]})]}),(0,a.jsxs)(rc.SZ,{children:["ID: ",e.id," | Type: ",e.type," | Required:"," ",e.required?"Yes":"No"]})]}),a.jsx(rc.aY,{children:e.options&&e.options.length>0&&(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium mb-1",children:"Options:"}),a.jsx("ul",{className:"list-disc pl-5",children:e.options.map((e,t)=>a.jsx("li",{children:e},t))})]})})]},e.id))}):a.jsx("p",{children:"No questions found for this domain."})})]})}),a.jsx(ru.nU,{value:"add",children:(0,a.jsxs)(rc.Zb,{children:[(0,a.jsxs)(rc.Ol,{children:[a.jsx(rc.ll,{children:v?"Edit Question":"Add New Question"}),a.jsx(rc.SZ,{children:v?`Editing question ID: ${v.id}`:"Create a new assessment question"})]}),a.jsx(rc.aY,{children:(0,a.jsxs)("form",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(rs._,{htmlFor:"id",children:"Question ID"}),a.jsx(u.I,{id:"id",placeholder:"e.g., finances_q5",value:y.id,onChange:e=>b("id",e.target.value),disabled:!!v})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(rs._,{htmlFor:"domain",children:"Domain"}),(0,a.jsxs)(nh,{value:y.domain,onValueChange:e=>b("domain",e),children:[a.jsx(n2,{id:"domain",children:a.jsx(nx,{placeholder:"Select domain"})}),(0,a.jsxs)(n5,{children:[a.jsx(n7,{value:"finances",children:"Finances"}),a.jsx(n7,{value:"vision",children:"Vision"}),a.jsx(n7,{value:"parenting",children:"Parenting"}),a.jsx(n7,{value:"communication",children:"Communication"}),a.jsx(n7,{value:"roles",children:"Roles"}),a.jsx(n7,{value:"sexuality",children:"Sexuality"}),a.jsx(n7,{value:"spirituality",children:"Spirituality"}),a.jsx(n7,{value:"darkside",children:"Dark Side"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(rs._,{htmlFor:"type",children:"Question Type"}),(0,a.jsxs)(nh,{value:y.type,onValueChange:e=>b("type",e),children:[a.jsx(n2,{id:"type",children:a.jsx(nx,{placeholder:"Select type"})}),(0,a.jsxs)(n5,{children:[a.jsx(n7,{value:"multiple-choice",children:"Multiple Choice"}),a.jsx(n7,{value:"scenario",children:"Scenario"}),a.jsx(n7,{value:"ranking",children:"Ranking"}),a.jsx(n7,{value:"open-ended",children:"Open Ended"}),a.jsx(n7,{value:"scale",children:"Scale"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(rs._,{htmlFor:"text",children:"Question Text"}),a.jsx(d.g,{id:"text",placeholder:"Enter the question text",value:y.text,onChange:e=>b("text",e.target.value),rows:3})]}),["multiple-choice","scenario","ranking"].includes(y.type)&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx(rs._,{children:"Options"}),a.jsx(c.z,{type:"button",variant:"outline",size:"sm",onClick:()=>{x({...y,options:[...y.options,""]})},children:"Add Option"})]}),y.options.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(u.I,{placeholder:`Option ${t+1}`,value:e,onChange:e=>j(t,e.target.value)}),a.jsx(c.z,{type:"button",variant:"ghost",size:"sm",onClick:()=>E(t),disabled:y.options.length<=2,children:"Remove"})]},t))]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(rl,{id:"required",checked:y.required,onCheckedChange:e=>b("required",!0===e)}),a.jsx(rs._,{htmlFor:"required",children:"Required question"})]})]})}),(0,a.jsxs)(rc.eW,{className:"flex justify-between",children:[a.jsx(c.z,{variant:"outline",onClick:R,children:v?"Cancel":"Clear"}),a.jsx(c.z,{onClick:C,children:v?"Update Question":"Add Question"})]})]})})]})]}):a.jsx("div",{className:"container py-8",children:"Checking permissions..."})}n(69701)},94750:(e,t,n)=>{"use strict";function r(){return null}n.d(t,{TempoInit:()=>r}),n(65411),n(17577)},91664:(e,t,n)=>{"use strict";n.d(t,{z:()=>c});var r=n(10326),o=n(17577),i=n(34214),a=n(79360),l=n(51223);let s=(0,a.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=o.forwardRef(({className:e,variant:t,size:n,asChild:o=!1,...a},c)=>{let u=o?i.g7:"button";return r.jsx(u,{className:(0,l.cn)(s({variant:t,size:n,className:e})),ref:c,...a})});c.displayName="Button"},29752:(e,t,n)=>{"use strict";n.d(t,{Ol:()=>l,SZ:()=>c,Zb:()=>a,aY:()=>u,eW:()=>d,ll:()=>s});var r=n(10326),o=n(17577),i=n(51223);let a=o.forwardRef(({className:e,...t},n)=>r.jsx("div",{ref:n,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));a.displayName="Card";let l=o.forwardRef(({className:e,...t},n)=>r.jsx("div",{ref:n,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let s=o.forwardRef(({className:e,...t},n)=>r.jsx("h3",{ref:n,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...t}));s.displayName="CardTitle";let c=o.forwardRef(({className:e,...t},n)=>r.jsx("p",{ref:n,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let u=o.forwardRef(({className:e,...t},n)=>r.jsx("div",{ref:n,className:(0,i.cn)("p-6 pt-0",e),...t}));u.displayName="CardContent";let d=o.forwardRef(({className:e,...t},n)=>r.jsx("div",{ref:n,className:(0,i.cn)(" flex items-center p-6 pt-0",e),...t}));d.displayName="CardFooter"},41190:(e,t,n)=>{"use strict";n.d(t,{I:()=>a});var r=n(10326),o=n(17577),i=n(51223);let a=o.forwardRef(({className:e,type:t,...n},o)=>r.jsx("input",{type:t,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:o,...n}));a.displayName="Input"},44794:(e,t,n)=>{"use strict";n.d(t,{_:()=>c});var r=n(10326),o=n(17577),i=n(34478),a=n(79360),l=n(51223);let s=(0,a.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=o.forwardRef(({className:e,...t},n)=>r.jsx(i.f,{ref:n,className:(0,l.cn)(s(),e),...t}));c.displayName=i.f.displayName},99744:(e,t,n)=>{"use strict";n.d(t,{mQ:()=>P,nU:()=>L,dr:()=>T,SP:()=>A});var r=n(10326),o=n(17577),i=n(82561),a=n(93095),l=n(15594),s=n(9815),c=n(45226),u=n(17124),d=n(52067),f=n(88957),p="Tabs",[m,h]=(0,a.b)(p,[l.Pc]),v=(0,l.Pc)(),[g,y]=m(p),x=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,onValueChange:i,defaultValue:a,orientation:l="horizontal",dir:s,activationMode:p="automatic",...m}=e,h=(0,u.gm)(s),[v,y]=(0,d.T)({prop:o,onChange:i,defaultProp:a});return(0,r.jsx)(g,{scope:n,baseId:(0,f.M)(),value:v,onValueChange:y,orientation:l,dir:h,activationMode:p,children:(0,r.jsx)(c.WV.div,{dir:h,"data-orientation":l,...m,ref:t})})});x.displayName=p;var w="TabsList",b=o.forwardRef((e,t)=>{let{__scopeTabs:n,loop:o=!0,...i}=e,a=y(w,n),s=v(n);return(0,r.jsx)(l.fC,{asChild:!0,...s,orientation:a.orientation,dir:a.dir,loop:o,children:(0,r.jsx)(c.WV.div,{role:"tablist","aria-orientation":a.orientation,...i,ref:t})})});b.displayName=w;var j="TabsTrigger",E=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,disabled:a=!1,...s}=e,u=y(j,n),d=v(n),f=N(u.baseId,o),p=R(u.baseId,o),m=o===u.value;return(0,r.jsx)(l.ck,{asChild:!0,...d,focusable:!a,active:m,children:(0,r.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":p,"data-state":m?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:f,...s,ref:t,onMouseDown:(0,i.M)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(o)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(o)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==u.activationMode;m||a||!e||u.onValueChange(o)})})})});E.displayName=j;var S="TabsContent",C=o.forwardRef((e,t)=>{let{__scopeTabs:n,value:i,forceMount:a,children:l,...u}=e,d=y(S,n),f=N(d.baseId,i),p=R(d.baseId,i),m=i===d.value,h=o.useRef(m);return o.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(s.z,{present:a||m,children:({present:n})=>(0,r.jsx)(c.WV.div,{"data-state":m?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":f,hidden:!n,id:p,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:n&&l})})});function N(e,t){return`${e}-trigger-${t}`}function R(e,t){return`${e}-content-${t}`}C.displayName=S;var k=n(51223);let P=x,T=o.forwardRef(({className:e,...t},n)=>r.jsx(b,{ref:n,className:(0,k.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",e),...t}));T.displayName=b.displayName;let A=o.forwardRef(({className:e,...t},n)=>r.jsx(E,{ref:n,className:(0,k.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",e),...t}));A.displayName=E.displayName;let L=o.forwardRef(({className:e,...t},n)=>r.jsx(C,{ref:n,className:(0,k.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));L.displayName=C.displayName},82015:(e,t,n)=>{"use strict";n.d(t,{g:()=>a});var r=n(10326),o=n(17577),i=n(51223);let a=o.forwardRef(({className:e,...t},n)=>r.jsx("textarea",{className:(0,i.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));a.displayName="Textarea"},48880:(e,t,n)=>{"use strict";n.d(t,{Ds:()=>l,Km:()=>s,Z7:()=>o,xL:()=>a,xz:()=>i});var r=n(69701);async function o(e){let t=(0,r.eI)(),{data:n,error:o}=await t.from("assessment_questions").select("*").eq("domain",e.toLowerCase());if(o)throw console.error("Error fetching questions:",o),Error(`Failed to fetch ${e} questions`);return n.map(e=>({id:e.id,type:e.type,text:e.text,options:e.options,required:e.required,domain:e.domain,weight:e.weight,category:e.category}))}async function i(){let e=(0,r.eI)(),{data:t,error:n}=await e.from("assessment_questions").select("*");if(n)throw console.error("Error fetching all questions:",n),Error("Failed to fetch questions");let o={};return t.forEach(e=>{let t={id:e.id,type:e.type,text:e.text,options:e.options,required:e.required,domain:e.domain,weight:e.weight,category:e.category};o[e.domain]||(o[e.domain]=[]),o[e.domain].push(t)}),o}async function a(e){let t=(0,r.eI)(),{error:n}=await t.from("assessment_questions").insert([{id:e.id,domain:e.domain,type:e.type,text:e.text,options:e.options,required:e.required,weight:e.weight,category:e.category}]);if(n)throw console.error("Error adding question:",n),Error("Failed to add question")}async function l(e){let t=(0,r.eI)(),{error:n}=await t.from("assessment_questions").update({domain:e.domain,type:e.type,text:e.text,options:e.options,required:e.required,weight:e.weight,category:e.category}).eq("id",e.id);if(n)throw console.error("Error updating question:",n),Error("Failed to update question")}async function s(e){let t=(0,r.eI)(),{error:n}=await t.from("assessment_questions").delete().eq("id",e);if(n)throw console.error("Error deleting question:",n),Error("Failed to delete question")}},69701:(e,t,n)=>{"use strict";n.d(t,{eI:()=>l,s3:()=>c});var r=n(65815);let o=null,i="https://eqghwtejdnzgopmcjlho.supabase.co",a=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!i)throw Error("Missing Supabase environment variables");let l=()=>{throw Error("createClient should only be called on the client side")},s=()=>{if(!o){if(!a)throw Error("SUPABASE_SERVICE_ROLE_KEY is required for admin operations");o=(0,r.eI)(i,a,{auth:{autoRefreshToken:!1,persistSession:!1}})}return o},c=()=>s()},51223:(e,t,n)=>{"use strict";n.d(t,{cn:()=>i});var r=n(41135),o=n(31009);function i(...e){return(0,o.m6)((0,r.W)(e))}},21791:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/app/admin/questions/page.tsx#default`)},61158:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s,metadata:()=>l});var r=n(19510),o=n(45317),i=n.n(o);let a=(0,n(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/components/tempo-init.tsx#TempoInit`);n(5023);let l={title:"Tempo - Modern SaaS Starter",description:"A modern full-stack starter template powered by Next.js"};function s({children:e}){return r.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:(0,r.jsxs)("body",{className:i().className,children:[e,r.jsx(a,{})]})})}},73881:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>o});var r=n(66621);let o=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[948,837,753,815,194,79],()=>n(41696));module.exports=r})();