(()=>{var e={};e.id=674,e.ids=[674],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},55315:e=>{"use strict";e.exports=require("path")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},47057:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>o}),r(57459),r(61158),r(35866);var t=r(23191),a=r(88716),i=r(37922),n=r.n(i),l=r(95231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let o=["",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,57459)),"/Users/<USER>/Nextjs/marriage-map/src/app/admin/users/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,61158)),"/Users/<USER>/Nextjs/marriage-map/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Nextjs/marriage-map/src/app/admin/users/page.tsx"],u="/admin/users/page",m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/users/page",pathname:"/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},77833:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},42896:(e,s,r)=>{Promise.resolve().then(r.bind(r,94750))},85677:(e,s,r)=>{Promise.resolve().then(r.bind(r,19505))},80239:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("chart-no-axes-column-increasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},41291:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},88319:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},36283:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},77506:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},88307:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},88378:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},8798:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},74975:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},24061:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},35047:(e,s,r)=>{"use strict";var t=r(77389);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},19505:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var t=r(10326),a=r(17577),i=r(35047),n=r(29752),l=r(91664),d=r(41190);r(69701);var o=r(77506),c=r(88378),u=r(80239),m=r(36283),x=r(24061),p=r(8798),h=r(88319),f=r(74975),y=r(41291),g=r(88307),v=r(90434);function j(){(0,i.useRouter)();let[e,s]=(0,a.useState)(!0),[r,j]=(0,a.useState)(null),[b,N]=(0,a.useState)([]),[k,w]=(0,a.useState)(""),[Z,P]=(0,a.useState)(!1),q=b.filter(e=>e.full_name?.toLowerCase().includes(k.toLowerCase())||e.email?.toLowerCase().includes(k.toLowerCase()));return e?(0,t.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[t.jsx(o.Z,{className:"h-8 w-8 animate-spin text-primary"}),t.jsx("span",{className:"ml-2",children:"Loading users..."})]}):Z?t.jsx("div",{className:"min-h-screen bg-background",children:(0,t.jsxs)("div",{className:"flex",children:[t.jsx("div",{className:"hidden md:flex w-64 flex-col fixed inset-y-0 z-50 border-r bg-background",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-6 p-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 px-2 py-4",children:[t.jsx(c.Z,{className:"h-6 w-6 text-primary"}),t.jsx("span",{className:"text-xl font-bold",children:"Admin Panel"})]}),(0,t.jsxs)("nav",{className:"flex flex-col space-y-1",children:[(0,t.jsxs)(v.default,{href:"/admin/dashboard",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[t.jsx(u.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Dashboard"})]}),(0,t.jsxs)(v.default,{href:"/admin/questions",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[t.jsx(m.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Questions"})]}),(0,t.jsxs)(v.default,{href:"/admin/users",className:"flex items-center gap-3 rounded-lg bg-primary/10 px-3 py-2 text-primary transition-all hover:text-primary",children:[t.jsx(x.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Users"})]}),(0,t.jsxs)(v.default,{href:"/admin/counselors",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[t.jsx(p.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Counselors"})]}),(0,t.jsxs)(v.default,{href:"/admin/database",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[t.jsx(h.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Database"})]})]})]})}),t.jsx("div",{className:"flex-1 md:pl-64",children:(0,t.jsxs)("div",{className:"container py-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[t.jsx("h1",{className:"text-3xl font-bold",children:"User Management"}),t.jsx(v.default,{href:"/admin/register",children:(0,t.jsxs)(l.z,{className:"flex items-center gap-2",children:[t.jsx(f.Z,{className:"h-4 w-4"}),"Add Admin User"]})})]}),r&&t.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(y.Z,{className:"h-4 w-4 mr-2"}),r]})}),t.jsx(n.Zb,{className:"mb-6",children:t.jsx(n.aY,{className:"pt-6",children:(0,t.jsxs)("div",{className:"relative",children:[t.jsx(g.Z,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),t.jsx(d.I,{type:"search",placeholder:"Search users by name or email...",className:"pl-8",value:k,onChange:e=>w(e.target.value)})]})})}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[t.jsx(n.ll,{children:"Users"}),t.jsx(n.SZ,{children:"Manage all users in the system"})]}),t.jsx(n.aY,{children:t.jsx("div",{className:"rounded-md border",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-border",children:[t.jsx("thead",{children:(0,t.jsxs)("tr",{className:"bg-muted/50",children:[t.jsx("th",{className:"px-4 py-3.5 text-left text-sm font-semibold",children:"Name"}),t.jsx("th",{className:"px-4 py-3.5 text-left text-sm font-semibold",children:"Email"}),t.jsx("th",{className:"px-4 py-3.5 text-left text-sm font-semibold",children:"Role"}),t.jsx("th",{className:"px-4 py-3.5 text-right text-sm font-semibold",children:"Actions"})]})}),t.jsx("tbody",{className:"divide-y divide-border",children:q.length>0?q.map(e=>(0,t.jsxs)("tr",{children:[t.jsx("td",{className:"px-4 py-4 text-sm",children:e.full_name}),t.jsx("td",{className:"px-4 py-4 text-sm",children:e.email}),t.jsx("td",{className:"px-4 py-4 text-sm",children:t.jsx("span",{className:`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${"admin"===e.role?"bg-purple-100 text-purple-800":"counselor"===e.role?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:e.role})}),t.jsx("td",{className:"px-4 py-4 text-sm text-right",children:t.jsx(l.z,{variant:"ghost",size:"sm",onClick:()=>{},children:"View"})})]},e.id)):t.jsx("tr",{children:t.jsx("td",{colSpan:4,className:"px-4 py-8 text-center text-muted-foreground",children:"No users found"})})})]})})})]})]})})]})}):t.jsx("div",{className:"container py-8",children:"Checking permissions..."})}},94750:(e,s,r)=>{"use strict";function t(){return null}r.d(s,{TempoInit:()=>t}),r(65411),r(17577)},91664:(e,s,r)=>{"use strict";r.d(s,{z:()=>o});var t=r(10326),a=r(17577),i=r(34214),n=r(79360),l=r(51223);let d=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef(({className:e,variant:s,size:r,asChild:a=!1,...n},o)=>{let c=a?i.g7:"button";return t.jsx(c,{className:(0,l.cn)(d({variant:s,size:r,className:e})),ref:o,...n})});o.displayName="Button"},29752:(e,s,r)=>{"use strict";r.d(s,{Ol:()=>l,SZ:()=>o,Zb:()=>n,aY:()=>c,eW:()=>u,ll:()=>d});var t=r(10326),a=r(17577),i=r(51223);let n=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...s}));n.displayName="Card";let l=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let d=a.forwardRef(({className:e,...s},r)=>t.jsx("h3",{ref:r,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let o=a.forwardRef(({className:e,...s},r)=>t.jsx("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)(" flex items-center p-6 pt-0",e),...s}));u.displayName="CardFooter"},41190:(e,s,r)=>{"use strict";r.d(s,{I:()=>n});var t=r(10326),a=r(17577),i=r(51223);let n=a.forwardRef(({className:e,type:s,...r},a)=>t.jsx("input",{type:s,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...r}));n.displayName="Input"},69701:(e,s,r)=>{"use strict";r.d(s,{eI:()=>l,s3:()=>o});var t=r(65815);let a=null,i="https://eqghwtejdnzgopmcjlho.supabase.co",n=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!i)throw Error("Missing Supabase environment variables");let l=()=>{throw Error("createClient should only be called on the client side")},d=()=>{if(!a){if(!n)throw Error("SUPABASE_SERVICE_ROLE_KEY is required for admin operations");a=(0,t.eI)(i,n,{auth:{autoRefreshToken:!1,persistSession:!1}})}return a},o=()=>d()},51223:(e,s,r)=>{"use strict";r.d(s,{cn:()=>i});var t=r(41135),a=r(31009);function i(...e){return(0,a.m6)((0,t.W)(e))}},57459:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/app/admin/users/page.tsx#default`)},61158:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d,metadata:()=>l});var t=r(19510),a=r(45317),i=r.n(a);let n=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/components/tempo-init.tsx#TempoInit`);r(5023);let l={title:"Tempo - Modern SaaS Starter",description:"A modern full-stack starter template powered by Next.js"};function d({children:e}){return t.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:(0,t.jsxs)("body",{className:i().className,children:[e,t.jsx(n,{})]})})}},73881:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(66621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[948,837,753,815,705],()=>r(47057));module.exports=t})();