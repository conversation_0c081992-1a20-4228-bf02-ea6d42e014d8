(()=>{var e={};e.id=680,e.ids=[680],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},29812:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l}),t(44170),t(61158),t(35866);var a=t(23191),s=t(88716),i=t(37922),n=t.n(i),o=t(95231),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l=["",{children:["admin",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,44170)),"/Users/<USER>/Nextjs/marriage-map/src/app/admin/register/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,61158)),"/Users/<USER>/Nextjs/marriage-map/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Nextjs/marriage-map/src/app/admin/register/page.tsx"],u="/admin/register/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/admin/register/page",pathname:"/admin/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},77833:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,12994,23)),Promise.resolve().then(t.t.bind(t,96114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,79671,23)),Promise.resolve().then(t.t.bind(t,41868,23)),Promise.resolve().then(t.t.bind(t,84759,23))},42896:(e,r,t)=>{Promise.resolve().then(t.bind(t,94750))},95146:(e,r,t)=>{Promise.resolve().then(t.bind(t,96559))},41291:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},77506:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},88378:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});let a=(0,t(62881).Z)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},35047:(e,r,t)=>{"use strict";var a=t(77389);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},96559:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var a=t(10326),s=t(17577),i=t(35047),n=t(29752),o=t(91664),d=t(41190),l=t(44794),c=t(88378),u=t(41291),m=t(77506),p=t(90434);function f(){let e=(0,i.useRouter)(),[r,t]=(0,s.useState)(""),[f,x]=(0,s.useState)(""),[h,g]=(0,s.useState)(""),[v,y]=(0,s.useState)(!1),[b,j]=(0,s.useState)(null),w=async t=>{t.preventDefault(),y(!0),j(null);try{if("MarriageMapAdmin"!==h)throw Error("Invalid admin code");let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r,password:f,role:"admin"})}),a=await t.json();if(!t.ok)throw Error(a.error||"Admin registration failed");e.push("/login?role=admin")}catch(e){console.error("Admin registration error:",e),j(e instanceof Error?e.message:"An error occurred during admin registration")}finally{y(!1)}};return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[a.jsx("div",{className:"flex justify-center mb-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(c.Z,{className:"h-8 w-8 text-primary"}),a.jsx("span",{className:"text-2xl font-bold",children:"Admin Registration"})]})}),(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[a.jsx(n.ll,{children:"Register as Admin"}),a.jsx(n.SZ,{children:"Create an administrator account (requires authorization code)"})]}),(0,a.jsxs)("form",{onSubmit:w,children:[(0,a.jsxs)(n.aY,{className:"space-y-4",children:[b&&a.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(u.Z,{className:"h-4 w-4 mr-2"}),b]})}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(l._,{htmlFor:"email",children:"Email"}),a.jsx(d.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:r,onChange:e=>t(e.target.value),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(l._,{htmlFor:"password",children:"Password"}),a.jsx(d.I,{id:"password",type:"password",placeholder:"••••••••",value:f,onChange:e=>x(e.target.value),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(l._,{htmlFor:"admin-code",children:"Admin Authorization Code"}),a.jsx(d.I,{id:"admin-code",type:"password",placeholder:"Enter admin code",value:h,onChange:e=>g(e.target.value),required:!0})]})]}),(0,a.jsxs)(n.eW,{className:"flex flex-col space-y-4",children:[a.jsx(o.z,{type:"submit",className:"w-full",disabled:v,children:v?(0,a.jsxs)(a.Fragment,{children:[a.jsx(m.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating admin account..."]}):"Register as Admin"}),a.jsx("div",{className:"text-center w-full",children:a.jsx(p.default,{href:"/login",className:"text-sm text-primary hover:underline",children:"Back to Login"})})]})]})]})]})})}},94750:(e,r,t)=>{"use strict";function a(){return null}t.d(r,{TempoInit:()=>a}),t(65411),t(17577)},91664:(e,r,t)=>{"use strict";t.d(r,{z:()=>l});var a=t(10326),s=t(17577),i=t(34214),n=t(79360),o=t(51223);let d=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef(({className:e,variant:r,size:t,asChild:s=!1,...n},l)=>{let c=s?i.g7:"button";return a.jsx(c,{className:(0,o.cn)(d({variant:r,size:t,className:e})),ref:l,...n})});l.displayName="Button"},29752:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>o,SZ:()=>l,Zb:()=>n,aY:()=>c,eW:()=>u,ll:()=>d});var a=t(10326),s=t(17577),i=t(51223);let n=s.forwardRef(({className:e,...r},t)=>a.jsx("div",{ref:t,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...r}));n.displayName="Card";let o=s.forwardRef(({className:e,...r},t)=>a.jsx("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let d=s.forwardRef(({className:e,...r},t)=>a.jsx("h3",{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle";let l=s.forwardRef(({className:e,...r},t)=>a.jsx("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let c=s.forwardRef(({className:e,...r},t)=>a.jsx("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=s.forwardRef(({className:e,...r},t)=>a.jsx("div",{ref:t,className:(0,i.cn)(" flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},41190:(e,r,t)=>{"use strict";t.d(r,{I:()=>n});var a=t(10326),s=t(17577),i=t(51223);let n=s.forwardRef(({className:e,type:r,...t},s)=>a.jsx("input",{type:r,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));n.displayName="Input"},44794:(e,r,t)=>{"use strict";t.d(r,{_:()=>l});var a=t(10326),s=t(17577),i=t(34478),n=t(79360),o=t(51223);let d=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=s.forwardRef(({className:e,...r},t)=>a.jsx(i.f,{ref:t,className:(0,o.cn)(d(),e),...r}));l.displayName=i.f.displayName},51223:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var a=t(41135),s=t(31009);function i(...e){return(0,s.m6)((0,a.W)(e))}},44170:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/app/admin/register/page.tsx#default`)},61158:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,metadata:()=>o});var a=t(19510),s=t(45317),i=t.n(s);let n=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/components/tempo-init.tsx#TempoInit`);t(5023);let o={title:"Tempo - Modern SaaS Starter",description:"A modern full-stack starter template powered by Next.js"};function d({children:e}){return a.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:(0,a.jsxs)("body",{className:i().className,children:[e,a.jsx(n,{})]})})}},73881:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var a=t(66621);let s=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{},34478:(e,r,t)=>{"use strict";t.d(r,{f:()=>o});var a=t(17577),s=t(45226),i=t(10326),n=a.forwardRef((e,r)=>(0,i.jsx)(s.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var o=n},45226:(e,r,t)=>{"use strict";t.d(r,{WV:()=>o,jH:()=>d});var a=t(17577),s=t(60962),i=t(34214),n=t(10326),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=a.forwardRef((e,t)=>{let{asChild:a,...s}=e,o=a?i.g7:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(o,{...s,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{});function d(e,r){e&&s.flushSync(()=>e.dispatchEvent(r))}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[948,837,753,705],()=>t(29812));module.exports=a})();