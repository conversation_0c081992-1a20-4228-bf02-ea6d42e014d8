(()=>{var e={};e.id=427,e.ids=[427],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},55315:e=>{"use strict";e.exports=require("path")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},31129:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>o}),t(13624),t(61158),t(35866);var r=t(23191),a=t(88716),n=t(37922),i=t.n(n),l=t(95231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o=["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,13624)),"/Users/<USER>/Nextjs/marriage-map/src/app/admin/dashboard/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,61158)),"/Users/<USER>/Nextjs/marriage-map/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Nextjs/marriage-map/src/app/admin/dashboard/page.tsx"],u="/admin/dashboard/page",m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},77833:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,12994,23)),Promise.resolve().then(t.t.bind(t,96114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,79671,23)),Promise.resolve().then(t.t.bind(t,41868,23)),Promise.resolve().then(t.t.bind(t,84759,23))},42896:(e,s,t)=>{Promise.resolve().then(t.bind(t,94750))},50154:(e,s,t)=>{Promise.resolve().then(t.bind(t,15426))},80239:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("chart-no-axes-column-increasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},41291:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},88319:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},36283:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},77506:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},71810:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},88378:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},8798:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},24061:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},35047:(e,s,t)=>{"use strict";var r=t(77389);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},15426:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var r=t(10326),a=t(17577),n=t(35047),i=t(29752),l=t(91664),d=t(99744);t(69701);var o=t(77506),c=t(88378),u=t(80239),m=t(36283),x=t(24061),p=t(8798),h=t(88319),f=t(71810),g=t(41291),v=t(1929),y=t(90434);function j(){let e=(0,n.useRouter)(),[s,t]=(0,a.useState)(!0),[j,b]=(0,a.useState)(null),[N,w]=(0,a.useState)({totalUsers:0,totalCouples:0,totalCounselors:0,completedAssessments:0}),[k,P]=(0,a.useState)(!1);return s?(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[r.jsx(o.Z,{className:"h-8 w-8 animate-spin text-primary"}),r.jsx("span",{className:"ml-2",children:"Loading admin dashboard..."})]}):k?r.jsx("div",{className:"min-h-screen bg-background",children:(0,r.jsxs)("div",{className:"flex",children:[r.jsx("div",{className:"hidden md:flex w-64 flex-col fixed inset-y-0 z-50 border-r bg-background",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-6 p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 px-2 py-4",children:[r.jsx(c.Z,{className:"h-6 w-6 text-primary"}),r.jsx("span",{className:"text-xl font-bold",children:"Admin Panel"})]}),(0,r.jsxs)("nav",{className:"flex flex-col space-y-1",children:[(0,r.jsxs)(y.default,{href:"/admin/dashboard",className:"flex items-center gap-3 rounded-lg bg-primary/10 px-3 py-2 text-primary transition-all hover:text-primary",children:[r.jsx(u.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Dashboard"})]}),(0,r.jsxs)(y.default,{href:"/admin/questions",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[r.jsx(m.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Questions"})]}),(0,r.jsxs)(y.default,{href:"/admin/users",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[r.jsx(x.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Users"})]}),(0,r.jsxs)(y.default,{href:"/admin/counselors",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[r.jsx(p.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Counselors"})]}),(0,r.jsxs)(y.default,{href:"/admin/database",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[r.jsx(h.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Database"})]}),(0,r.jsxs)("button",{onClick:v.h,className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:bg-red-50 hover:text-red-600 w-full mt-4",children:[r.jsx(f.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Logout"})]})]})]})}),r.jsx("div",{className:"flex-1 md:pl-64",children:(0,r.jsxs)("div",{className:"container py-8",children:[r.jsx("h1",{className:"text-3xl font-bold mb-6",children:"Admin Dashboard"}),j&&r.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(g.Z,{className:"h-4 w-4 mr-2"}),j]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{className:"pb-2",children:r.jsx(i.ll,{className:"text-lg",children:"Total Users"})}),r.jsx(i.aY,{children:r.jsx("div",{className:"text-3xl font-bold",children:N.totalUsers})})]}),(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{className:"pb-2",children:r.jsx(i.ll,{className:"text-lg",children:"Total Couples"})}),r.jsx(i.aY,{children:r.jsx("div",{className:"text-3xl font-bold",children:N.totalCouples})})]}),(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{className:"pb-2",children:r.jsx(i.ll,{className:"text-lg",children:"Total Counselors"})}),r.jsx(i.aY,{children:r.jsx("div",{className:"text-3xl font-bold",children:N.totalCounselors})})]}),(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{className:"pb-2",children:r.jsx(i.ll,{className:"text-lg",children:"Completed Assessments"})}),r.jsx(i.aY,{children:r.jsx("div",{className:"text-3xl font-bold",children:N.completedAssessments})})]})]}),(0,r.jsxs)(i.Zb,{className:"mb-8",children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(i.ll,{children:"Quick Actions"}),r.jsx(i.SZ,{children:"Common administrative tasks you can perform"})]}),(0,r.jsxs)(i.aY,{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)(l.z,{variant:"outline",className:"flex items-center justify-start gap-2",onClick:()=>e.push("/admin/questions"),children:[r.jsx(m.Z,{className:"h-4 w-4"}),"Manage Assessment Questions"]}),(0,r.jsxs)(l.z,{variant:"outline",className:"flex items-center justify-start gap-2",onClick:()=>e.push("/admin/users"),children:[r.jsx(x.Z,{className:"h-4 w-4"}),"Manage Users"]}),(0,r.jsxs)(l.z,{variant:"outline",className:"flex items-center justify-start gap-2",onClick:()=>e.push("/admin/counselors"),children:[r.jsx(p.Z,{className:"h-4 w-4"}),"Manage Counselors"]})]})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(i.ll,{children:"Recent Activity"}),r.jsx(i.SZ,{children:"Latest actions and events in the system"})]}),r.jsx(i.aY,{children:(0,r.jsxs)(d.mQ,{defaultValue:"registrations",children:[(0,r.jsxs)(d.dr,{className:"mb-4",children:[r.jsx(d.SP,{value:"registrations",children:"New Registrations"}),r.jsx(d.SP,{value:"assessments",children:"Completed Assessments"}),r.jsx(d.SP,{value:"connections",children:"Couple Connections"})]}),r.jsx(d.nU,{value:"registrations",children:r.jsx("div",{className:"space-y-4",children:r.jsx("p",{className:"text-muted-foreground",children:"Recent user registrations will appear here."})})}),r.jsx(d.nU,{value:"assessments",children:r.jsx("div",{className:"space-y-4",children:r.jsx("p",{className:"text-muted-foreground",children:"Recently completed assessments will appear here."})})}),r.jsx(d.nU,{value:"connections",children:r.jsx("div",{className:"space-y-4",children:r.jsx("p",{className:"text-muted-foreground",children:"Recent couple connections will appear here."})})})]})}),r.jsx(i.eW,{children:r.jsx(l.z,{variant:"outline",className:"w-full",children:"View All Activity"})})]})]})})]})}):r.jsx("div",{className:"container py-8",children:"Checking permissions..."})}},94750:(e,s,t)=>{"use strict";function r(){return null}t.d(s,{TempoInit:()=>r}),t(65411),t(17577)},91664:(e,s,t)=>{"use strict";t.d(s,{z:()=>o});var r=t(10326),a=t(17577),n=t(34214),i=t(79360),l=t(51223);let d=(0,i.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef(({className:e,variant:s,size:t,asChild:a=!1,...i},o)=>{let c=a?n.g7:"button";return r.jsx(c,{className:(0,l.cn)(d({variant:s,size:t,className:e})),ref:o,...i})});o.displayName="Button"},29752:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>l,SZ:()=>o,Zb:()=>i,aY:()=>c,eW:()=>u,ll:()=>d});var r=t(10326),a=t(17577),n=t(51223);let i=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...s}));i.displayName="Card";let l=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("h3",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)(" flex items-center p-6 pt-0",e),...s}));u.displayName="CardFooter"},99744:(e,s,t)=>{"use strict";t.d(s,{mQ:()=>M,nU:()=>A,dr:()=>R,SP:()=>S});var r=t(10326),a=t(17577),n=t(82561),i=t(93095),l=t(15594),d=t(9815),o=t(45226),c=t(17124),u=t(52067),m=t(88957),x="Tabs",[p,h]=(0,i.b)(x,[l.Pc]),f=(0,l.Pc)(),[g,v]=p(x),y=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,onValueChange:n,defaultValue:i,orientation:l="horizontal",dir:d,activationMode:x="automatic",...p}=e,h=(0,c.gm)(d),[f,v]=(0,u.T)({prop:a,onChange:n,defaultProp:i});return(0,r.jsx)(g,{scope:t,baseId:(0,m.M)(),value:f,onValueChange:v,orientation:l,dir:h,activationMode:x,children:(0,r.jsx)(o.WV.div,{dir:h,"data-orientation":l,...p,ref:s})})});y.displayName=x;var j="TabsList",b=a.forwardRef((e,s)=>{let{__scopeTabs:t,loop:a=!0,...n}=e,i=v(j,t),d=f(t);return(0,r.jsx)(l.fC,{asChild:!0,...d,orientation:i.orientation,dir:i.dir,loop:a,children:(0,r.jsx)(o.WV.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:s})})});b.displayName=j;var N="TabsTrigger",w=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,disabled:i=!1,...d}=e,c=v(N,t),u=f(t),m=Z(c.baseId,a),x=C(c.baseId,a),p=a===c.value;return(0,r.jsx)(l.ck,{asChild:!0,...u,focusable:!i,active:p,children:(0,r.jsx)(o.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":x,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:m,...d,ref:s,onMouseDown:(0,n.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,n.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,n.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||i||!e||c.onValueChange(a)})})})});w.displayName=N;var k="TabsContent",P=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:n,forceMount:i,children:l,...c}=e,u=v(k,t),m=Z(u.baseId,n),x=C(u.baseId,n),p=n===u.value,h=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(d.z,{present:i||p,children:({present:t})=>(0,r.jsx)(o.WV.div,{"data-state":p?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":m,hidden:!t,id:x,tabIndex:0,...c,ref:s,style:{...e.style,animationDuration:h.current?"0s":void 0},children:t&&l})})});function Z(e,s){return`${e}-trigger-${s}`}function C(e,s){return`${e}-content-${s}`}P.displayName=k;var q=t(51223);let M=y,R=a.forwardRef(({className:e,...s},t)=>r.jsx(b,{ref:t,className:(0,q.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",e),...s}));R.displayName=b.displayName;let S=a.forwardRef(({className:e,...s},t)=>r.jsx(w,{ref:t,className:(0,q.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",e),...s}));S.displayName=w.displayName;let A=a.forwardRef(({className:e,...s},t)=>r.jsx(P,{ref:t,className:(0,q.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));A.displayName=P.displayName},1929:(e,s,t)=>{"use strict";t.d(s,{h:()=>a});var r=t(69701);async function a(){try{let e=(0,r.s3)(),{error:s}=await e.auth.signOut();if(s)return console.error("Error signing out:",s),!1;return window.location.href="/login",!0}catch(e){return console.error("Unexpected error during logout:",e),!1}}},69701:(e,s,t)=>{"use strict";t.d(s,{eI:()=>l,s3:()=>o});var r=t(65815);let a=null,n="https://eqghwtejdnzgopmcjlho.supabase.co",i=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!n)throw Error("Missing Supabase environment variables");let l=()=>{throw Error("createClient should only be called on the client side")},d=()=>{if(!a){if(!i)throw Error("SUPABASE_SERVICE_ROLE_KEY is required for admin operations");a=(0,r.eI)(n,i,{auth:{autoRefreshToken:!1,persistSession:!1}})}return a},o=()=>d()},51223:(e,s,t)=>{"use strict";t.d(s,{cn:()=>n});var r=t(41135),a=t(31009);function n(...e){return(0,a.m6)((0,r.W)(e))}},13624:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/app/admin/dashboard/page.tsx#default`)},61158:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,metadata:()=>l});var r=t(19510),a=t(45317),n=t.n(a);let i=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/components/tempo-init.tsx#TempoInit`);t(5023);let l={title:"Tempo - Modern SaaS Starter",description:"A modern full-stack starter template powered by Next.js"};function d({children:e}){return r.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:(0,r.jsxs)("body",{className:n().className,children:[e,r.jsx(i,{})]})})}},73881:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(66621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[948,837,753,815,705,194],()=>t(31129));module.exports=r})();