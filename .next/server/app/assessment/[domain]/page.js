(()=>{var e={};e.id=530,e.ids=[530],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},55315:e=>{"use strict";e.exports=require("path")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},49914:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(36237),t(61158),t(35866);var r=t(23191),a=t(88716),i=t(37922),n=t.n(i),o=t(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d=["",{children:["assessment",{children:["[domain]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36237)),"/Users/<USER>/Nextjs/marriage-map/src/app/assessment/[domain]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,61158)),"/Users/<USER>/Nextjs/marriage-map/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Nextjs/marriage-map/src/app/assessment/[domain]/page.tsx"],u="/assessment/[domain]/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/assessment/[domain]/page",pathname:"/assessment/[domain]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},26046:(e,s,t)=>{Promise.resolve().then(t.bind(t,97116))},97116:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ei});var r=t(10326),a=t(17577),i=t(35047),n=t(29752),o=t(91664),l=t(41190),d=t(82015),c=t(76812),u=t(82561),m=t(48051),p=t(93095),h=t(45226),x=t(15594),f=t(52067),g=t(17124),v=t(2566),y=t(53405),w=t(9815),b="Radio",[j,q]=(0,p.b)(b),[N,k]=j(b),E=a.forwardRef((e,s)=>{let{__scopeRadio:t,name:i,checked:n=!1,required:o,disabled:l,value:d="on",onCheck:c,form:p,...x}=e,[f,g]=a.useState(null),v=(0,m.e)(s,e=>g(e)),y=a.useRef(!1),w=!f||p||!!f.closest("form");return(0,r.jsxs)(N,{scope:t,checked:n,disabled:l,children:[(0,r.jsx)(h.WV.button,{type:"button",role:"radio","aria-checked":n,"data-state":R(n),"data-disabled":l?"":void 0,disabled:l,value:d,...x,ref:v,onClick:(0,u.M)(e.onClick,e=>{n||c?.(),w&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),w&&(0,r.jsx)(P,{control:f,bubbles:!y.current,name:i,value:d,checked:n,required:o,disabled:l,form:p,style:{transform:"translateX(-100%)"}})]})});E.displayName=b;var _="RadioIndicator",C=a.forwardRef((e,s)=>{let{__scopeRadio:t,forceMount:a,...i}=e,n=k(_,t);return(0,r.jsx)(w.z,{present:a||n.checked,children:(0,r.jsx)(h.WV.span,{"data-state":R(n.checked),"data-disabled":n.disabled?"":void 0,...i,ref:s})})});C.displayName=_;var P=e=>{let{control:s,checked:t,bubbles:i=!0,...n}=e,o=a.useRef(null),l=(0,y.D)(t),d=(0,v.t)(s);return a.useEffect(()=>{let e=o.current,s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(l!==t&&s){let r=new Event("click",{bubbles:i});s.call(e,t),e.dispatchEvent(r)}},[l,t,i]),(0,r.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:t,...n,tabIndex:-1,ref:o,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function R(e){return e?"checked":"unchecked"}var I=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],S="RadioGroup",[L,F]=(0,p.b)(S,[x.Pc,q]),M=(0,x.Pc)(),A=q(),[G,V]=L(S),z=a.forwardRef((e,s)=>{let{__scopeRadioGroup:t,name:a,defaultValue:i,value:n,required:o=!1,disabled:l=!1,orientation:d,dir:c,loop:u=!0,onValueChange:m,...p}=e,v=M(t),y=(0,g.gm)(c),[w,b]=(0,f.T)({prop:n,defaultProp:i,onChange:m});return(0,r.jsx)(G,{scope:t,name:a,required:o,disabled:l,value:w,onValueChange:b,children:(0,r.jsx)(x.fC,{asChild:!0,...v,orientation:d,dir:y,loop:u,children:(0,r.jsx)(h.WV.div,{role:"radiogroup","aria-required":o,"aria-orientation":d,"data-disabled":l?"":void 0,dir:y,...p,ref:s})})})});z.displayName=S;var D="RadioGroupItem",U=a.forwardRef((e,s)=>{let{__scopeRadioGroup:t,disabled:i,...n}=e,o=V(D,t),l=o.disabled||i,d=M(t),c=A(t),p=a.useRef(null),h=(0,m.e)(s,p),f=o.value===n.value,g=a.useRef(!1);return a.useEffect(()=>{let e=e=>{I.includes(e.key)&&(g.current=!0)},s=()=>g.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",s),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",s)}},[]),(0,r.jsx)(x.ck,{asChild:!0,...d,focusable:!l,active:f,children:(0,r.jsx)(E,{disabled:l,required:o.required,checked:f,...c,...n,name:o.name,ref:h,onCheck:()=>o.onValueChange(n.value),onKeyDown:(0,u.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,u.M)(n.onFocus,()=>{g.current&&p.current?.click()})})})});U.displayName=D;var T=a.forwardRef((e,s)=>{let{__scopeRadioGroup:t,...a}=e,i=A(t);return(0,r.jsx)(C,{...i,...a,ref:s})});T.displayName="RadioGroupIndicator";var Z=t(51223);let $=a.forwardRef(({className:e,...s},t)=>r.jsx(z,{className:(0,Z.cn)("grid gap-2",e),...s,ref:t}));$.displayName=z.displayName;let K=a.forwardRef(({className:e,children:s,...t},a)=>r.jsx(U,{ref:a,className:(0,Z.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:r.jsx(T,{className:"flex items-center justify-center",children:r.jsx(c.nQG,{className:"h-3.5 w-3.5 fill-primary"})})}));K.displayName=U.displayName;var O=t(44794),W=t(94880),H=t(62881);let Q=(0,H.Z)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),X=(0,H.Z)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),B=(0,H.Z)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var Y=t(24336),J=t(48880);let ee="keuangan",es=({domain:e=ee,questions:s,onSubmit:t,onSave:i})=>{let[c,u]=(0,a.useState)([]),[m,p]=(0,a.useState)(!1),[h,x]=(0,a.useState)(null);(0,a.useEffect)(()=>{if(s){u(s);return}(async()=>{try{p(!0),x(null);let s=await (0,J.Z7)(e);if(s&&s.length>0)u(s);else{let s=(0,Y.gK)(e);s.length>0?u(s):u((0,Y.gK)("keuangan"))}}catch(t){console.error("Error loading questions:",t),x("Failed to load questions. Using local fallback.");let s=(0,Y.gK)(e);s.length>0?u(s):u((0,Y.gK)("keuangan"))}finally{p(!1)}})()},[e,s]);let[f,g]=(0,a.useState)(0),[v,y]=(0,a.useState)({}),[w,b]=(0,a.useState)({}),j=c[f]||{id:"",type:"multiple-choice",text:"Loading..."},q=c.length>0?(f+1)/c.length*100:0,N=()=>{let e=c.filter(e=>e.required).filter(e=>!v[e.id]);if(e.length>0){let s={};e.forEach(e=>{s[e.id]="This question requires an answer"}),b(s),g(c.findIndex(s=>s.id===e[0].id));return}t&&t(v)},k=(e,s)=>{if(y({...v,[e]:s}),w[e]){let s={...w};delete s[e],b(s)}};return(0,r.jsxs)(n.Zb,{className:"w-full max-w-4xl mx-auto bg-white",children:[(0,r.jsxs)(n.Ol,{children:[(0,r.jsxs)(n.ll,{className:"text-2xl",children:[e," Assessment"]}),(0,r.jsxs)("div",{className:"mt-2",children:[r.jsx(W.E,{value:q,className:"h-2"}),(0,r.jsxs)("div",{className:"flex justify-between mt-1 text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:["Question ",f+1," of ",c.length]}),(0,r.jsxs)("span",{children:[Math.round(q),"% Complete"]})]})]}),m&&r.jsx("p",{className:"text-sm text-blue-500 mt-2",children:"Loading questions..."}),h&&r.jsx("p",{className:"text-sm text-red-500 mt-2",children:h})]}),r.jsx(n.aY,{children:r.jsx("div",{className:"space-y-6",children:(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-medium",children:j.text}),w[j.id]&&r.jsx("p",{className:"text-sm text-red-500 mt-1",children:w[j.id]}),(()=>{switch(j.type){case"multiple-choice":return r.jsx($,{value:v[j.id]||"",onValueChange:e=>k(j.id,e),className:"space-y-3 mt-4",children:j.options?.map((e,s)=>r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(K,{value:e,id:`option-${s}`}),r.jsx(O._,{htmlFor:`option-${s}`,children:e})]},s))});case"scale":return r.jsx($,{value:v[j.id]||"",onValueChange:e=>k(j.id,e),className:"space-y-3 mt-4",children:j.options?.map((e,s)=>r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(K,{value:e,id:`scale-${s}`}),r.jsx(O._,{htmlFor:`scale-${s}`,children:e})]},s))});case"scenario":return r.jsx($,{value:v[j.id]||"",onValueChange:e=>k(j.id,e),className:"space-y-3 mt-4",children:j.options?.map((e,s)=>r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(K,{value:e,id:`scenario-${s}`}),r.jsx(O._,{htmlFor:`scenario-${s}`,children:e})]},s))});case"ranking":return r.jsx("div",{className:"space-y-4 mt-4",children:j.options?.map((e,s)=>r.jsx("div",{className:"flex items-center space-x-3",children:r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(l.I,{type:"number",min:"1",max:j.options?.length,className:"w-16",value:v[j.id]?.[e]||"",onChange:s=>{let t=s.target.value,r=v[j.id]||{};k(j.id,{...r,[e]:t})}}),r.jsx(O._,{children:e})]})},s))});case"open-ended":return r.jsx(d.g,{className:"mt-4",placeholder:"Type your answer here...",value:v[j.id]||"",onChange:e=>k(j.id,e.target.value),rows:5});default:return null}})()]})})}),(0,r.jsxs)(n.eW,{className:"flex justify-between",children:[r.jsx("div",{children:(0,r.jsxs)(o.z,{variant:"outline",onClick:()=>{f>0&&g(f-1)},disabled:0===f,children:[r.jsx(Q,{className:"mr-2 h-4 w-4"}),"Previous"]})}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(o.z,{variant:"outline",onClick:()=>{i&&i(v)},children:[r.jsx(X,{className:"mr-2 h-4 w-4"}),"Save Progress"]}),r.jsx(o.z,{onClick:()=>{if(c.length>0&&j.required&&!v[j.id]){b({...w,[j.id]:"This question requires an answer"});return}if(w[j.id]){let e={...w};delete e[j.id],b(e)}f<c.length-1?g(f+1):N()},children:f<c.length-1?(0,r.jsxs)(r.Fragment,{children:["Next",r.jsx(B,{className:"ml-2 h-4 w-4"})]}):"Submit"})]})]})]})};var et=t(86333),er=t(69701),ea=t(69498);function ei(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),t=e.domain,[n,l]=(0,a.useState)(!1),[d,c]=(0,a.useState)(null),u=async e=>{try{l(!0);let r=(0,er.s3)(),{data:{user:a}}=await r.auth.getUser();if(!a)throw Error("User not authenticated");let i=Object.entries(e).map(([e,s])=>({questionId:e,answer:s,domain:t})),n=(0,ea.yI)(t,i),o=n.score,{data:d}=await r.from("individual_results").select("*").eq("user_id",a.id).single();if(d){let s=[...d.domains],a=s.findIndex(e=>e.domain.toLowerCase()===t.toLowerCase());a>=0?s[a]={...s[a],score:o,responses:e,subcategories:n.subcategories}:s.push({domain:(0,ea.bc)(t),score:o,responses:e,subcategories:n.subcategories});let i=Math.round(s.reduce((e,s)=>e+s.score,0)/s.length);await r.from("individual_results").update({domains:s,overall_score:i,updated_at:new Date().toISOString()}).eq("id",d.id)}else await r.from("individual_results").insert({user_id:a.id,domains:[{domain:(0,ea.bc)(t),score:o,responses:e,subcategories:n.subcategories}],overall_score:o});s.push("/couple/dashboard")}catch(e){console.error("Error saving assessment:",e),c(e instanceof Error?e.message:"An error occurred while saving your assessment")}finally{l(!1)}},m=async e=>{alert("Progress saved!")};return(0,r.jsxs)("div",{className:"container py-8",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)(o.z,{variant:"ghost",onClick:()=>s.push("/dashboard"),className:"mb-4",children:[r.jsx(et.Z,{className:"mr-2 h-4 w-4"}),"Back to Dashboard"]}),(0,r.jsxs)("h1",{className:"text-3xl font-bold",children:[(0,ea.bc)(t)," Assessment"]}),(0,r.jsxs)("p",{className:"text-muted-foreground",children:["Complete the following questions to assess your ",t.toLowerCase()," ","compatibility."]})]}),d&&r.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:d}),r.jsx(es,{domain:t,onSubmit:u,onSave:m})]})}},41190:(e,s,t)=>{"use strict";t.d(s,{I:()=>n});var r=t(10326),a=t(17577),i=t(51223);let n=a.forwardRef(({className:e,type:s,...t},a)=>r.jsx("input",{type:s,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));n.displayName="Input"},44794:(e,s,t)=>{"use strict";t.d(s,{_:()=>d});var r=t(10326),a=t(17577),i=t(34478),n=t(79360),o=t(51223);let l=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...s},t)=>r.jsx(i.f,{ref:t,className:(0,o.cn)(l(),e),...s}));d.displayName=i.f.displayName},82015:(e,s,t)=>{"use strict";t.d(s,{g:()=>n});var r=t(10326),a=t(17577),i=t(51223);let n=a.forwardRef(({className:e,...s},t)=>r.jsx("textarea",{className:(0,i.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));n.displayName="Textarea"},48880:(e,s,t)=>{"use strict";t.d(s,{Ds:()=>o,Km:()=>l,Z7:()=>a,xL:()=>n,xz:()=>i});var r=t(69701);async function a(e){let s=(0,r.eI)(),{data:t,error:a}=await s.from("assessment_questions").select("*").eq("domain",e.toLowerCase());if(a)throw console.error("Error fetching questions:",a),Error(`Failed to fetch ${e} questions`);return t.map(e=>({id:e.id,type:e.type,text:e.text,options:e.options,required:e.required,domain:e.domain,weight:e.weight,category:e.category}))}async function i(){let e=(0,r.eI)(),{data:s,error:t}=await e.from("assessment_questions").select("*");if(t)throw console.error("Error fetching all questions:",t),Error("Failed to fetch questions");let a={};return s.forEach(e=>{let s={id:e.id,type:e.type,text:e.text,options:e.options,required:e.required,domain:e.domain,weight:e.weight,category:e.category};a[e.domain]||(a[e.domain]=[]),a[e.domain].push(s)}),a}async function n(e){let s=(0,r.eI)(),{error:t}=await s.from("assessment_questions").insert([{id:e.id,domain:e.domain,type:e.type,text:e.text,options:e.options,required:e.required,weight:e.weight,category:e.category}]);if(t)throw console.error("Error adding question:",t),Error("Failed to add question")}async function o(e){let s=(0,r.eI)(),{error:t}=await s.from("assessment_questions").update({domain:e.domain,type:e.type,text:e.text,options:e.options,required:e.required,weight:e.weight,category:e.category}).eq("id",e.id);if(t)throw console.error("Error updating question:",t),Error("Failed to update question")}async function l(e){let s=(0,r.eI)(),{error:t}=await s.from("assessment_questions").delete().eq("id",e);if(t)throw console.error("Error deleting question:",t),Error("Failed to delete question")}},36237:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/app/assessment/[domain]/page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[948,837,753,815,194,79,119],()=>t(49914));module.exports=r})();