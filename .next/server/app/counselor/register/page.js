(()=>{var e={};e.id=219,e.ids=[219],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},83139:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(1990),t(61158),t(35866);var s=t(23191),a=t(88716),o=t(37922),n=t.n(o),i=t(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d=["",{children:["counselor",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1990)),"/Users/<USER>/Nextjs/marriage-map/src/app/counselor/register/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,61158)),"/Users/<USER>/Nextjs/marriage-map/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Nextjs/marriage-map/src/app/counselor/register/page.tsx"],u="/counselor/register/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/counselor/register/page",pathname:"/counselor/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},77833:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,12994,23)),Promise.resolve().then(t.t.bind(t,96114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,79671,23)),Promise.resolve().then(t.t.bind(t,41868,23)),Promise.resolve().then(t.t.bind(t,84759,23))},42896:(e,r,t)=>{Promise.resolve().then(t.bind(t,94750))},7814:(e,r,t)=>{Promise.resolve().then(t.bind(t,71972))},41291:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},77506:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},8798:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(62881).Z)("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},35047:(e,r,t)=>{"use strict";var s=t(77389);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},71972:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var s=t(10326),a=t(17577),o=t(35047),n=t(29752),i=t(91664),l=t(41190),d=t(44794),c=t(8798),u=t(41291),m=t(77506),p=t(90434);function x(){let e=(0,o.useRouter)(),[r,t]=(0,a.useState)(""),[x,f]=(0,a.useState)(""),[h,g]=(0,a.useState)(""),[v,y]=(0,a.useState)(""),[b,j]=(0,a.useState)(!1),[w,N]=(0,a.useState)(null),P=async t=>{t.preventDefault(),j(!0),N(null);try{if("MarriageMapCounselor"!==v)throw Error("Invalid counselor authorization code");let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r,password:x,role:"counselor",fullName:h||r.split("@")[0]})}),s=await t.json();if(!t.ok)throw Error(s.error||"Counselor registration failed");e.push("/login?role=counselor")}catch(e){console.error("Counselor registration error:",e),N(e instanceof Error?e.message:"An error occurred during counselor registration")}finally{j(!1)}};return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[s.jsx("div",{className:"flex justify-center mb-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(c.Z,{className:"h-8 w-8 text-primary"}),s.jsx("span",{className:"text-2xl font-bold",children:"Counselor Registration"})]})}),(0,s.jsxs)(n.Zb,{children:[(0,s.jsxs)(n.Ol,{children:[s.jsx(n.ll,{children:"Register as Counselor"}),s.jsx(n.SZ,{children:"Create a counselor account (requires authorization code)"})]}),(0,s.jsxs)("form",{onSubmit:P,children:[(0,s.jsxs)(n.aY,{className:"space-y-4",children:[w&&s.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(u.Z,{className:"h-4 w-4 mr-2"}),w]})}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(d._,{htmlFor:"email",children:"Email"}),s.jsx(l.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:r,onChange:e=>t(e.target.value),required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(d._,{htmlFor:"full-name",children:"Full Name"}),s.jsx(l.I,{id:"full-name",type:"text",placeholder:"John Doe",value:h,onChange:e=>g(e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(d._,{htmlFor:"password",children:"Password"}),s.jsx(l.I,{id:"password",type:"password",placeholder:"••••••••",value:x,onChange:e=>f(e.target.value),required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(d._,{htmlFor:"counselor-code",children:"Counselor Authorization Code"}),s.jsx(l.I,{id:"counselor-code",type:"password",placeholder:"Enter counselor code",value:v,onChange:e=>y(e.target.value),required:!0})]})]}),(0,s.jsxs)(n.eW,{className:"flex flex-col space-y-4",children:[s.jsx(i.z,{type:"submit",className:"w-full",disabled:b,children:b?(0,s.jsxs)(s.Fragment,{children:[s.jsx(m.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating counselor account..."]}):"Register as Counselor"}),s.jsx("div",{className:"text-center w-full",children:s.jsx(p.default,{href:"/login",className:"text-sm text-primary hover:underline",children:"Back to Login"})})]})]})]})]})})}},94750:(e,r,t)=>{"use strict";function s(){return null}t.d(r,{TempoInit:()=>s}),t(65411),t(17577)},91664:(e,r,t)=>{"use strict";t.d(r,{z:()=>d});var s=t(10326),a=t(17577),o=t(34214),n=t(79360),i=t(51223);let l=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...n},d)=>{let c=a?o.g7:"button";return s.jsx(c,{className:(0,i.cn)(l({variant:r,size:t,className:e})),ref:d,...n})});d.displayName="Button"},29752:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>i,SZ:()=>d,Zb:()=>n,aY:()=>c,eW:()=>u,ll:()=>l});var s=t(10326),a=t(17577),o=t(51223);let n=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...r}));n.displayName="Card";let i=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...r}));i.displayName="CardHeader";let l=a.forwardRef(({className:e,...r},t)=>s.jsx("h3",{ref:t,className:(0,o.cn)("font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},t)=>s.jsx("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)(" flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},41190:(e,r,t)=>{"use strict";t.d(r,{I:()=>n});var s=t(10326),a=t(17577),o=t(51223);let n=a.forwardRef(({className:e,type:r,...t},a)=>s.jsx("input",{type:r,className:(0,o.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));n.displayName="Input"},44794:(e,r,t)=>{"use strict";t.d(r,{_:()=>d});var s=t(10326),a=t(17577),o=t(34478),n=t(79360),i=t(51223);let l=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...r},t)=>s.jsx(o.f,{ref:t,className:(0,i.cn)(l(),e),...r}));d.displayName=o.f.displayName},51223:(e,r,t)=>{"use strict";t.d(r,{cn:()=>o});var s=t(41135),a=t(31009);function o(...e){return(0,a.m6)((0,s.W)(e))}},1990:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/app/counselor/register/page.tsx#default`)},61158:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>i});var s=t(19510),a=t(45317),o=t.n(a);let n=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/components/tempo-init.tsx#TempoInit`);t(5023);let i={title:"Tempo - Modern SaaS Starter",description:"A modern full-stack starter template powered by Next.js"};function l({children:e}){return s.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsxs)("body",{className:o().className,children:[e,s.jsx(n,{})]})})}},73881:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(66621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{},34478:(e,r,t)=>{"use strict";t.d(r,{f:()=>i});var s=t(17577),a=t(45226),o=t(10326),n=s.forwardRef((e,r)=>(0,o.jsx)(a.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var i=n},45226:(e,r,t)=>{"use strict";t.d(r,{WV:()=>i,jH:()=>l});var s=t(17577),a=t(60962),o=t(34214),n=t(10326),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=s.forwardRef((e,t)=>{let{asChild:s,...a}=e,i=s?o.g7:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(i,{...a,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{});function l(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[948,837,753,705],()=>t(83139));module.exports=s})();