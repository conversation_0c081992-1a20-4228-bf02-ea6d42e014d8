(()=>{var e={};e.id=667,e.ids=[667],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},55315:e=>{"use strict";e.exports=require("path")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},41963:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d}),r(29528),r(61158),r(35866);var t=r(23191),a=r(88716),i=r(37922),n=r.n(i),l=r(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d=["",{children:["counselor",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,29528)),"/Users/<USER>/Nextjs/marriage-map/src/app/counselor/dashboard/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,61158)),"/Users/<USER>/Nextjs/marriage-map/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Nextjs/marriage-map/src/app/counselor/dashboard/page.tsx"],u="/counselor/dashboard/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/counselor/dashboard/page",pathname:"/counselor/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},77833:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},42896:(e,s,r)=>{Promise.resolve().then(r.bind(r,94750))},40177:(e,s,r)=>{Promise.resolve().then(r.bind(r,48892))},37358:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41291:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},36283:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},77506:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},71810:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},39730:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},88307:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8798:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},24061:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(62881).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},35047:(e,s,r)=>{"use strict";var t=r(77389);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},48892:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var t=r(10326),a=r(17577),i=r(35047),n=r(29752),l=r(91664),o=r(99744);r(69701);var d=r(77506),c=r(41291),u=r(8798),m=r(24061),h=r(37358),x=r(36283),p=r(71810),f=r(88307),g=r(39730),j=r(1929),y=r(41190),v=r(90434);function b(){let e=(0,i.useRouter)(),[s,r]=(0,a.useState)(!0),[b,N]=(0,a.useState)(null),[w,k]=(0,a.useState)({assignedCouples:0,scheduledSessions:0,completedSessions:0}),[S,Z]=(0,a.useState)(!1),[P,C]=(0,a.useState)("");return s?(0,t.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[t.jsx(d.Z,{className:"h-8 w-8 animate-spin text-primary"}),t.jsx("span",{className:"ml-2",children:"Loading counselor dashboard..."})]}):b?t.jsx("div",{className:"container mx-auto p-4 min-h-screen flex flex-col items-center justify-center",children:(0,t.jsxs)(n.Zb,{className:"w-full max-w-md",children:[t.jsx(n.Ol,{children:t.jsx(n.ll,{className:"text-destructive",children:"Access Error"})}),(0,t.jsxs)(n.aY,{children:[(0,t.jsxs)("div",{className:"flex items-center text-destructive",children:[t.jsx(c.Z,{className:"h-5 w-5 mr-2"}),t.jsx("p",{children:b})]}),t.jsx("p",{className:"mt-4 text-sm text-muted-foreground",children:"There was an issue verifying your counselor status or loading necessary data. If this persists, please contact support. The Supabase RLS policy for 'admin_users' might be causing an infinite recursion."})]}),t.jsx(n.eW,{children:t.jsx(l.z,{onClick:()=>e.push("/login"),className:"w-full",children:"Go to Login"})})]})}):s?(0,t.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[t.jsx(d.Z,{className:"h-8 w-8 animate-spin text-primary"}),t.jsx("span",{className:"ml-2",children:"Loading counselor dashboard..."})]}):S?t.jsx("div",{className:"min-h-screen bg-background",children:(0,t.jsxs)("div",{className:"flex",children:[t.jsx("div",{className:"hidden md:flex w-64 flex-col fixed inset-y-0 z-50 border-r bg-background",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-6 p-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 px-2 py-4",children:[t.jsx(u.Z,{className:"h-6 w-6 text-primary"}),t.jsx("span",{className:"text-xl font-bold",children:"Counselor Portal"})]}),(0,t.jsxs)("nav",{className:"flex flex-col space-y-1",children:[(0,t.jsxs)(v.default,{href:"/counselor/dashboard",className:"flex items-center gap-3 rounded-lg bg-primary/10 px-3 py-2 text-primary transition-all hover:text-primary",children:[t.jsx(m.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Dashboard"})]}),(0,t.jsxs)(v.default,{href:"/counselor/couples",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[t.jsx(m.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"My Couples"})]}),(0,t.jsxs)(v.default,{href:"/counselor/sessions",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[t.jsx(h.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Sessions"})]}),(0,t.jsxs)(v.default,{href:"/counselor/resources",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[t.jsx(x.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Resources"})]}),(0,t.jsxs)(v.default,{href:"/counselor/profile",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[t.jsx(u.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"My Profile"})]}),(0,t.jsxs)("button",{onClick:j.h,className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:bg-red-50 hover:text-red-600 w-full mt-4",children:[t.jsx(p.Z,{className:"h-5 w-5"}),t.jsx("span",{children:"Logout"})]})]})]})}),t.jsx("div",{className:"flex-1 md:pl-64",children:(0,t.jsxs)("div",{className:"container py-8",children:[t.jsx("h1",{className:"text-3xl font-bold mb-6",children:"Counselor Dashboard"}),b&&t.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(c.Z,{className:"h-4 w-4 mr-2"}),b]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,t.jsxs)(n.Zb,{children:[t.jsx(n.Ol,{className:"pb-2",children:t.jsx(n.ll,{className:"text-lg",children:"Assigned Couples"})}),t.jsx(n.aY,{children:t.jsx("div",{className:"text-3xl font-bold",children:w.assignedCouples})}),t.jsx(n.eW,{children:t.jsx(l.z,{variant:"ghost",className:"w-full text-primary",onClick:()=>e.push("/counselor/couples"),children:"View All"})})]}),(0,t.jsxs)(n.Zb,{children:[t.jsx(n.Ol,{className:"pb-2",children:t.jsx(n.ll,{className:"text-lg",children:"Scheduled Sessions"})}),t.jsx(n.aY,{children:t.jsx("div",{className:"text-3xl font-bold",children:w.scheduledSessions})}),t.jsx(n.eW,{children:t.jsx(l.z,{variant:"ghost",className:"w-full text-primary",onClick:()=>e.push("/counselor/sessions?filter=scheduled"),children:"View Schedule"})})]}),(0,t.jsxs)(n.Zb,{children:[t.jsx(n.Ol,{className:"pb-2",children:t.jsx(n.ll,{className:"text-lg",children:"Completed Sessions"})}),t.jsx(n.aY,{children:t.jsx("div",{className:"text-3xl font-bold",children:w.completedSessions})}),t.jsx(n.eW,{children:t.jsx(l.z,{variant:"ghost",className:"w-full text-primary",onClick:()=>e.push("/counselor/sessions?filter=completed"),children:"View History"})})]})]}),(0,t.jsxs)(n.Zb,{className:"mb-8",children:[(0,t.jsxs)(n.Ol,{children:[t.jsx(n.ll,{children:"Find Couple"}),t.jsx(n.SZ,{children:"Search for couples by name or couple code"})]}),t.jsx(n.aY,{children:(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[t.jsx(f.Z,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),t.jsx(y.I,{type:"search",placeholder:"Search by name or couple code...",className:"pl-8",value:P,onChange:e=>C(e.target.value)})]}),t.jsx(l.z,{children:"Search"})]})})]}),(0,t.jsxs)(n.Zb,{className:"mb-8",children:[(0,t.jsxs)(n.Ol,{children:[t.jsx(n.ll,{children:"Upcoming Sessions"}),t.jsx(n.SZ,{children:"Your scheduled counseling sessions"})]}),t.jsx(n.aY,{children:t.jsx("div",{className:"space-y-4",children:t.jsx("p",{className:"text-muted-foreground",children:"No upcoming sessions scheduled. Sessions will appear here when couples book time with you."})})}),t.jsx(n.eW,{children:t.jsx(l.z,{variant:"outline",className:"w-full",onClick:()=>e.push("/counselor/sessions"),children:"View All Sessions"})})]}),(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[t.jsx(n.ll,{children:"Recent Couple Activity"}),t.jsx(n.SZ,{children:"Latest assessment completions and updates"})]}),t.jsx(n.aY,{children:(0,t.jsxs)(o.mQ,{defaultValue:"assessments",children:[(0,t.jsxs)(o.dr,{className:"mb-4",children:[t.jsx(o.SP,{value:"assessments",children:"Assessments"}),t.jsx(o.SP,{value:"messages",children:"Messages"})]}),t.jsx(o.nU,{value:"assessments",children:t.jsx("div",{className:"space-y-4",children:t.jsx("p",{className:"text-muted-foreground",children:"Recent assessment completions will appear here."})})}),t.jsx(o.nU,{value:"messages",children:(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("p",{className:"text-muted-foreground",children:"Recent messages from couples will appear here."}),(0,t.jsxs)(l.z,{className:"flex items-center gap-2",onClick:()=>e.push("/counselor/messages"),children:[t.jsx(g.Z,{className:"h-4 w-4"}),"Open Messages"]})]})})]})})]})]})})]})}):t.jsx("div",{className:"container mx-auto p-4 min-h-screen flex flex-col items-center justify-center",children:(0,t.jsxs)(n.Zb,{className:"w-full max-w-md",children:[t.jsx(n.Ol,{children:t.jsx(n.ll,{children:"Permission Issue"})}),t.jsx(n.aY,{children:t.jsx("p",{children:"Could not verify counselor permissions. You will be redirected to login."})}),t.jsx(n.eW,{children:t.jsx(l.z,{onClick:()=>e.push("/login"),className:"w-full",children:"Go to Login"})})]})})}},94750:(e,s,r)=>{"use strict";function t(){return null}r.d(s,{TempoInit:()=>t}),r(65411),r(17577)},91664:(e,s,r)=>{"use strict";r.d(s,{z:()=>d});var t=r(10326),a=r(17577),i=r(34214),n=r(79360),l=r(51223);let o=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:s,size:r,asChild:a=!1,...n},d)=>{let c=a?i.g7:"button";return t.jsx(c,{className:(0,l.cn)(o({variant:s,size:r,className:e})),ref:d,...n})});d.displayName="Button"},29752:(e,s,r)=>{"use strict";r.d(s,{Ol:()=>l,SZ:()=>d,Zb:()=>n,aY:()=>c,eW:()=>u,ll:()=>o});var t=r(10326),a=r(17577),i=r(51223);let n=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...s}));n.displayName="Card";let l=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...s},r)=>t.jsx("h3",{ref:r,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...s},r)=>t.jsx("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)(" flex items-center p-6 pt-0",e),...s}));u.displayName="CardFooter"},41190:(e,s,r)=>{"use strict";r.d(s,{I:()=>n});var t=r(10326),a=r(17577),i=r(51223);let n=a.forwardRef(({className:e,type:s,...r},a)=>t.jsx("input",{type:s,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...r}));n.displayName="Input"},99744:(e,s,r)=>{"use strict";r.d(s,{mQ:()=>M,nU:()=>_,dr:()=>R,SP:()=>q});var t=r(10326),a=r(17577),i=r(82561),n=r(93095),l=r(15594),o=r(9815),d=r(45226),c=r(17124),u=r(52067),m=r(88957),h="Tabs",[x,p]=(0,n.b)(h,[l.Pc]),f=(0,l.Pc)(),[g,j]=x(h),y=a.forwardRef((e,s)=>{let{__scopeTabs:r,value:a,onValueChange:i,defaultValue:n,orientation:l="horizontal",dir:o,activationMode:h="automatic",...x}=e,p=(0,c.gm)(o),[f,j]=(0,u.T)({prop:a,onChange:i,defaultProp:n});return(0,t.jsx)(g,{scope:r,baseId:(0,m.M)(),value:f,onValueChange:j,orientation:l,dir:p,activationMode:h,children:(0,t.jsx)(d.WV.div,{dir:p,"data-orientation":l,...x,ref:s})})});y.displayName=h;var v="TabsList",b=a.forwardRef((e,s)=>{let{__scopeTabs:r,loop:a=!0,...i}=e,n=j(v,r),o=f(r);return(0,t.jsx)(l.fC,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:a,children:(0,t.jsx)(d.WV.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:s})})});b.displayName=v;var N="TabsTrigger",w=a.forwardRef((e,s)=>{let{__scopeTabs:r,value:a,disabled:n=!1,...o}=e,c=j(N,r),u=f(r),m=Z(c.baseId,a),h=P(c.baseId,a),x=a===c.value;return(0,t.jsx)(l.ck,{asChild:!0,...u,focusable:!n,active:x,children:(0,t.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":h,"data-state":x?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:m,...o,ref:s,onMouseDown:(0,i.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;x||n||!e||c.onValueChange(a)})})})});w.displayName=N;var k="TabsContent",S=a.forwardRef((e,s)=>{let{__scopeTabs:r,value:i,forceMount:n,children:l,...c}=e,u=j(k,r),m=Z(u.baseId,i),h=P(u.baseId,i),x=i===u.value,p=a.useRef(x);return a.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(o.z,{present:n||x,children:({present:r})=>(0,t.jsx)(d.WV.div,{"data-state":x?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":m,hidden:!r,id:h,tabIndex:0,...c,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&l})})});function Z(e,s){return`${e}-trigger-${s}`}function P(e,s){return`${e}-content-${s}`}S.displayName=k;var C=r(51223);let M=y,R=a.forwardRef(({className:e,...s},r)=>t.jsx(b,{ref:r,className:(0,C.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",e),...s}));R.displayName=b.displayName;let q=a.forwardRef(({className:e,...s},r)=>t.jsx(w,{ref:r,className:(0,C.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",e),...s}));q.displayName=w.displayName;let _=a.forwardRef(({className:e,...s},r)=>t.jsx(S,{ref:r,className:(0,C.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));_.displayName=S.displayName},1929:(e,s,r)=>{"use strict";r.d(s,{h:()=>a});var t=r(69701);async function a(){try{let e=(0,t.s3)(),{error:s}=await e.auth.signOut();if(s)return console.error("Error signing out:",s),!1;return window.location.href="/login",!0}catch(e){return console.error("Unexpected error during logout:",e),!1}}},69701:(e,s,r)=>{"use strict";r.d(s,{eI:()=>l,s3:()=>d});var t=r(65815);let a=null,i="https://eqghwtejdnzgopmcjlho.supabase.co",n=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!i)throw Error("Missing Supabase environment variables");let l=()=>{throw Error("createClient should only be called on the client side")},o=()=>{if(!a){if(!n)throw Error("SUPABASE_SERVICE_ROLE_KEY is required for admin operations");a=(0,t.eI)(i,n,{auth:{autoRefreshToken:!1,persistSession:!1}})}return a},d=()=>o()},51223:(e,s,r)=>{"use strict";r.d(s,{cn:()=>i});var t=r(41135),a=r(31009);function i(...e){return(0,a.m6)((0,t.W)(e))}},29528:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/app/counselor/dashboard/page.tsx#default`)},61158:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o,metadata:()=>l});var t=r(19510),a=r(45317),i=r.n(a);let n=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/components/tempo-init.tsx#TempoInit`);r(5023);let l={title:"Tempo - Modern SaaS Starter",description:"A modern full-stack starter template powered by Next.js"};function o({children:e}){return t.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:(0,t.jsxs)("body",{className:i().className,children:[e,t.jsx(n,{})]})})}},73881:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(66621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[948,837,753,815,705,194],()=>r(41963));module.exports=t})();