(()=>{var e={};e.id=702,e.ids=[702],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},55315:e=>{"use strict";e.exports=require("path")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},29925:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(38256),r(61158),r(35866);var a=r(23191),s=r(88716),i=r(37922),n=r.n(i),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38256)),"/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,61158)),"/Users/<USER>/Nextjs/marriage-map/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx"],u="/dashboard/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},77833:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},42896:(e,t,r)=>{Promise.resolve().then(r.bind(r,94750))},96087:(e,t,r)=>{Promise.resolve().then(r.bind(r,55743))},41291:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},30361:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48998:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},77506:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(62881).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55743:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_});var a=r(10326),s=r(17577),i=r(90434),n=r(29752),o=r(91664),l=r(94880),d=r(99744),c=r(41190),u=r(30361),m=r(41291),p=r(62881);let h=(0,p.Z)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),x=(0,p.Z)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var f=r(33261),g=r(38443),v=r(48998);let b=(0,p.Z)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var y=r(51223);let j=({title:e,description:t,icon:r,status:s,domainId:o,className:l})=>a.jsx(i.default,{href:`/assessment/${o}`,className:"block",children:(0,a.jsxs)(n.Zb,{className:(0,y.cn)("h-full transition-all duration-200 hover:shadow-md hover:scale-[1.02] cursor-pointer group","completed"===s&&"border-green-200 bg-green-50/50",l),children:[a.jsx(n.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"text-2xl",children:r}),a.jsx("div",{className:"flex-1",children:a.jsx(n.ll,{className:"text-base font-semibold group-hover:text-primary transition-colors",children:e})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(()=>{switch(s){case"completed":return a.jsx(u.Z,{className:"h-4 w-4 text-green-500"});case"in-progress":return a.jsx(v.Z,{className:"h-4 w-4 text-amber-500"});default:return a.jsx(v.Z,{className:"h-4 w-4 text-gray-400"})}})(),a.jsx(b,{className:"h-4 w-4 text-gray-400 group-hover:text-primary transition-colors"})]})]})}),(0,a.jsxs)(n.aY,{className:"pt-0",children:[a.jsx(n.SZ,{className:"text-sm text-gray-600 mb-3",children:t}),a.jsx("div",{className:"flex justify-between items-center",children:(()=>{switch(s){case"completed":return a.jsx(g.C,{variant:"secondary",className:"bg-green-100 text-green-700 border-green-200",children:"Completed"});case"in-progress":return a.jsx(g.C,{variant:"secondary",className:"bg-amber-100 text-amber-700 border-amber-200",children:"In Progress"});default:return a.jsx(g.C,{variant:"outline",className:"text-gray-600",children:"Not Started"})}})()})]})]})});var w=r(69701);let N=({userName:e="John",coupleCode:t="",partnerName:r="",isConnected:p=!1,completedDomains:g=[]})=>{let[v,b]=(0,s.useState)("overview"),[y,N]=(0,s.useState)(t||""),[k,_]=(0,s.useState)(""),[C,P]=(0,s.useState)(p?"connected":"idle"),[S,E]=(0,s.useState)(!1),[D,q]=(0,s.useState)(""),Z=[{id:"visi-hidup",title:"Visi Hidup",description:"Aspirasi dan tujuan jangka panjang dalam pernikahan",icon:"\uD83D\uDD2D",completed:g.includes("visi-hidup"),status:g.includes("visi-hidup")?"completed":"not-started"},{id:"keuangan",title:"Keuangan",description:"Pengelolaan keuangan dan transparansi finansial",icon:"\uD83D\uDCB0",completed:g.includes("keuangan"),status:g.includes("keuangan")?"completed":"not-started"},{id:"pengasuhan",title:"Pengasuhan Anak",description:"Gaya dan filosofi dalam mendidik anak",icon:"\uD83D\uDC76",completed:g.includes("pengasuhan"),status:g.includes("pengasuhan")?"completed":"not-started"},{id:"komunikasi",title:"Komunikasi",description:"Cara berkomunikasi dan menyelesaikan konflik",icon:"\uD83D\uDCAC",completed:g.includes("komunikasi"),status:g.includes("komunikasi")?"completed":"not-started"},{id:"fungsi-dan-peran",title:"Fungsi dan Peran",description:"Peran suami-istri berdasarkan nilai-nilai Alkitab",icon:"\uD83D\uDD04",completed:g.includes("fungsi-dan-peran"),status:g.includes("fungsi-dan-peran")?"completed":"not-started"},{id:"seks",title:"Keintiman Seksual",description:"Pandangan dan ekspektasi tentang keintiman",icon:"❤️",completed:g.includes("seks"),status:g.includes("seks")?"completed":"not-started"},{id:"spiritualitas",title:"Spiritualitas",description:"Pertumbuhan iman dan praktik spiritual bersama",icon:"✝️",completed:g.includes("spiritualitas"),status:g.includes("spiritualitas")?"completed":"not-started"},{id:"sisi-gelap",title:"Sisi Gelap",description:"Pengelolaan emosi negatif dan potensi masalah",icon:"\uD83C\uDF11",completed:g.includes("sisi-gelap"),status:g.includes("sisi-gelap")?"completed":"not-started"}],I=Math.round(g.length/Z.length*100),R=async()=>{E(!0),q("");try{let e;let t=(0,w.eI)(),{data:{user:r},error:a}=await t.auth.getUser();if(a||!r)throw Error("You must be logged in to generate a code");let{data:s}=await t.from("couples").select("couple_id").or(`user_id_1.eq.${r.id},user_id_2.eq.${r.id}`).single();if(s)throw Error("You are already connected with a partner");await t.from("couple_invitation_codes").update({is_active:!1}).eq("creator_user_id",r.id).eq("is_active",!0);let i=!1,n=0;do{e=Math.random().toString(36).substring(2,8).toUpperCase();let{data:r}=await t.from("couple_invitation_codes").select("id").eq("code",e).eq("is_active",!0).single();i=!r,n++}while(!i&&n<10);if(!i)throw Error("Failed to generate unique code. Please try again.");let{data:o,error:l}=await t.from("couple_invitation_codes").insert({code:e,creator_user_id:r.id,expires_at:new Date(Date.now()+6048e5).toISOString()}).select().single();if(l)throw console.error("Error creating invitation code:",l),Error("Failed to create invitation code");N(o.code)}catch(e){console.error("Error generating code:",e),q(e instanceof Error?e.message:"Failed to generate code")}finally{E(!1)}},A=async()=>{if(6!==k.length){q("Please enter a valid 6-character code");return}E(!0),P("pending"),q("");try{let e=(0,w.eI)(),{data:{user:t},error:r}=await e.auth.getUser();if(r||!t)throw Error("You must be logged in to connect");let{data:a}=await e.from("couples").select("couple_id").or(`user_id_1.eq.${t.id},user_id_2.eq.${t.id}`).single();if(a)throw Error("You are already connected with a partner");console.log("Searching for code:",k.toUpperCase());let{data:s,error:i}=await e.from("couple_invitation_codes").select("*").eq("code",k.toUpperCase()).eq("is_active",!0).gte("expires_at",new Date().toISOString()).single();if(console.log("Code search result:",{invitationCode:s,codeError:i}),i||!s){let{data:t}=await e.from("couple_invitation_codes").select("*").eq("code",k.toUpperCase()).single();if(console.log("Any code with this value:",t),t&&!t.is_active)throw Error("This invitation code has been used or deactivated");throw Error("Invalid or expired invitation code")}if(new Date(s.expires_at)<new Date)throw Error("Invitation code has expired");if(s.used_by_user_id)throw Error("Invitation code has already been used");if(s.creator_user_id===t.id)throw Error("You cannot use your own invitation code");let{data:n}=await e.from("couples").select("couple_id").or(`user_id_1.eq.${s.creator_user_id},user_id_2.eq.${s.creator_user_id}`).single();if(n)throw Error("The code creator is already connected with someone else");let{data:o,error:l}=await e.from("couples").insert({user_id_1:s.creator_user_id,user_id_2:t.id}).select().single();if(l)throw console.error("Error creating couple:",l),Error("Failed to create couple connection");let{error:d}=await e.from("couple_invitation_codes").update({used_by_user_id:t.id,used_at:new Date().toISOString(),is_active:!1}).eq("id",s.id);d?console.error("Error updating invitation code:",d):console.log("Invitation code updated successfully"),P("connected"),_(""),window.location.reload()}catch(e){console.error("Error connecting with partner:",e),P("error"),q(e instanceof Error?e.message:"Failed to connect with partner")}finally{E(!1)}};return(0,a.jsxs)("div",{className:"bg-background w-full max-w-7xl mx-auto p-4 md:p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Assessment Dashboard"}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:["Welcome back, ",e,". Continue your marriage assessment journey."]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(o.z,{variant:"outline",onClick:()=>b("overview"),children:"Overview"}),a.jsx(o.z,{variant:"outline",onClick:()=>b("connect"),children:"Connect"}),I>0&&a.jsx(o.z,{variant:"outline",onClick:()=>b("results"),children:"Results"}),a.jsx(o.z,{variant:"default",asChild:!0,children:a.jsx(i.default,{href:"/couple/dashboard",children:"Couple Dashboard"})})]})]}),"overview"===v&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{className:"pb-2",children:[a.jsx(n.ll,{children:"Assessment Progress"}),a.jsx(n.SZ,{children:"Complete all 8 domains to get comprehensive insights"})]}),a.jsx(n.aY,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{children:[g.length," of ",Z.length," completed"]}),(0,a.jsxs)("span",{children:[I,"%"]})]}),a.jsx(l.E,{value:I,className:"h-2"})]})})]}),"connected"===C?(0,a.jsxs)(f.bZ,{className:"bg-green-50 border-green-200",children:[a.jsx(u.Z,{className:"h-4 w-4 text-green-600"}),(0,a.jsxs)(f.X,{children:["You are connected with ",r||"your partner",". Complete your assessments to view compatibility results."]})]}):(0,a.jsxs)(f.bZ,{children:[a.jsx(m.Z,{className:"h-4 w-4"}),a.jsx(f.X,{children:"Connect with your partner to compare assessment results and get compatibility insights."})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Z.map(e=>a.jsx(j,{title:e.title,description:e.description,icon:e.icon,status:e.status,domainId:e.id},e.id))})]}),"connect"===v&&(0,a.jsxs)(n.Zb,{className:"w-full",children:[(0,a.jsxs)(n.Ol,{children:[a.jsx(n.ll,{children:"Connect with Your Partner"}),a.jsx(n.SZ,{children:"Generate a code to share with your partner or enter the code they shared with you"})]}),a.jsx(n.aY,{children:(0,a.jsxs)(d.mQ,{defaultValue:"generate",className:"w-full",children:[(0,a.jsxs)(d.dr,{className:"grid w-full grid-cols-2",children:[a.jsx(d.SP,{value:"generate",children:"Generate Code"}),a.jsx(d.SP,{value:"enter",children:"Enter Code"})]}),a.jsx(d.nU,{value:"generate",className:"space-y-4",children:(0,a.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(c.I,{value:y,readOnly:!0,className:"font-mono text-center text-lg",placeholder:"No code generated"}),a.jsx(o.z,{variant:"outline",size:"icon",onClick:()=>{navigator.clipboard.writeText(y)},disabled:!y,children:a.jsx(h,{className:"h-4 w-4"})})]}),(0,a.jsxs)(o.z,{onClick:R,className:"w-full",disabled:S,children:[a.jsx(x,{className:`mr-2 h-4 w-4 ${S?"animate-spin":""}`}),S?"Generating...":"Generate New Code"]}),a.jsx("p",{className:"text-sm text-muted-foreground text-center",children:"Share this code with your partner so they can connect with you"}),D&&(0,a.jsxs)(f.bZ,{variant:"destructive",children:[a.jsx(m.Z,{className:"h-4 w-4"}),a.jsx(f.X,{children:D})]})]})}),a.jsx(d.nU,{value:"enter",className:"space-y-4",children:(0,a.jsxs)("div",{className:"mt-6 space-y-4",children:[a.jsx(c.I,{value:k,onChange:e=>_(e.target.value),className:"font-mono text-center text-lg",placeholder:"Enter 6-digit code",maxLength:6}),a.jsx(o.z,{onClick:A,className:"w-full",disabled:S||"pending"===C||6!==k.length,children:"pending"===C||S?(0,a.jsxs)(a.Fragment,{children:[a.jsx(x,{className:"mr-2 h-4 w-4 animate-spin"}),"Connecting..."]}):"Connect with Partner"}),("error"===C||D)&&(0,a.jsxs)(f.bZ,{variant:"destructive",children:[a.jsx(m.Z,{className:"h-4 w-4"}),a.jsx(f.X,{children:D||"Invalid code. Please check and try again."})]})]})})]})})]}),"results"===v&&(0,a.jsxs)(n.Zb,{children:[(0,a.jsxs)(n.Ol,{children:[a.jsx(n.ll,{children:"Assessment Results"}),a.jsx(n.SZ,{children:"connected"===C?"View your compatibility results and insights":"Connect with your partner to view compatibility results"})]}),a.jsx(n.aY,{children:"connected"===C?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx("p",{className:"text-muted-foreground mb-4",children:g.length===Z.length?"All assessments completed! View your detailed results below.":"Complete all assessment domains to view detailed compatibility results."}),a.jsx(o.z,{disabled:g.length!==Z.length,children:"View Detailed Results"})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx("p",{className:"text-muted-foreground mb-4",children:"You need to connect with your partner first to view compatibility results."}),a.jsx(o.z,{onClick:()=>b("connect"),children:"Connect with Partner"})]})})]})]})};var k=r(77506);function _(){let[e,t]=(0,s.useState)(!0),[r,i]=(0,s.useState)(null),[n,o]=(0,s.useState)({userName:"",coupleCode:"",partnerName:"",isConnected:!1,completedDomains:[]});return e?(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[a.jsx(k.Z,{className:"h-8 w-8 animate-spin text-primary"}),a.jsx("span",{className:"ml-2",children:"Loading your dashboard..."})]}):r?a.jsx("div",{className:"container py-8",children:a.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:r})}):a.jsx("div",{className:"min-h-screen bg-background",children:a.jsx(N,{userName:n.userName,coupleCode:n.coupleCode,partnerName:n.partnerName,isConnected:n.isConnected,completedDomains:n.completedDomains})})}},94750:(e,t,r)=>{"use strict";function a(){return null}r.d(t,{TempoInit:()=>a}),r(65411),r(17577)},33261:(e,t,r)=>{"use strict";r.d(t,{X:()=>d,bZ:()=>l});var a=r(10326),s=r(17577),i=r(79360),n=r(51223);let o=(0,i.j)("relative w-full rounded-lg border px-4 py-3 text-sm [&:has(svg)]:pl-11 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=s.forwardRef(({className:e,variant:t,...r},s)=>a.jsx("div",{ref:s,role:"alert",className:(0,n.cn)(o({variant:t}),e),...r}));l.displayName="Alert",s.forwardRef(({className:e,...t},r)=>a.jsx("h5",{ref:r,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let d=s.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...t}));d.displayName="AlertDescription"},38443:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var a=r(10326);r(17577);var s=r(79360),i=r(51223);let n=(0,s.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return a.jsx("div",{className:(0,i.cn)(n({variant:t}),e),...r})}},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var a=r(10326),s=r(17577),i=r(34214),n=r(79360),o=r(51223);let l=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef(({className:e,variant:t,size:r,asChild:s=!1,...n},d)=>{let c=s?i.g7:"button";return a.jsx(c,{className:(0,o.cn)(l({variant:t,size:r,className:e})),ref:d,...n})});d.displayName="Button"},29752:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>o,SZ:()=>d,Zb:()=>n,aY:()=>c,eW:()=>u,ll:()=>l});var a=r(10326),s=r(17577),i=r(51223);let n=s.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));n.displayName="Card";let o=s.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=s.forwardRef(({className:e,...t},r)=>a.jsx("h3",{ref:r,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=s.forwardRef(({className:e,...t},r)=>a.jsx("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=s.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=s.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,i.cn)(" flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},41190:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});var a=r(10326),s=r(17577),i=r(51223);let n=s.forwardRef(({className:e,type:t,...r},s)=>a.jsx("input",{type:t,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...r}));n.displayName="Input"},94880:(e,t,r)=>{"use strict";r.d(t,{E:()=>j});var a=r(10326),s=r(17577),i=r(93095),n=r(45226),o="Progress",[l,d]=(0,i.b)(o),[c,u]=l(o),m=s.forwardRef((e,t)=>{var r,s;let{__scopeProgress:i,value:o=null,max:l,getValueLabel:d=x,...u}=e;(l||0===l)&&!v(l)&&console.error((r=`${l}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=v(l)?l:100;null===o||b(o,m)||console.error((s=`${o}`,`Invalid prop \`value\` of value \`${s}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let p=b(o,m)?o:null,h=g(p)?d(p,m):void 0;return(0,a.jsx)(c,{scope:i,value:p,max:m,children:(0,a.jsx)(n.WV.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":g(p)?p:void 0,"aria-valuetext":h,role:"progressbar","data-state":f(p,m),"data-value":p??void 0,"data-max":m,...u,ref:t})})});m.displayName=o;var p="ProgressIndicator",h=s.forwardRef((e,t)=>{let{__scopeProgress:r,...s}=e,i=u(p,r);return(0,a.jsx)(n.WV.div,{"data-state":f(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...s,ref:t})});function x(e,t){return`${Math.round(e/t*100)}%`}function f(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function v(e){return g(e)&&!isNaN(e)&&e>0}function b(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=p;var y=r(51223);let j=s.forwardRef(({className:e,value:t,...r},s)=>a.jsx(m,{ref:s,className:(0,y.cn)("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",e),...r,children:a.jsx(h,{className:"h-full w-full flex-1 bg-primary transition-all bg-white",style:{transform:`translateX(-${100-(t||0)}%)`}})}));j.displayName=m.displayName},99744:(e,t,r)=>{"use strict";r.d(t,{mQ:()=>E,nU:()=>Z,dr:()=>D,SP:()=>q});var a=r(10326),s=r(17577),i=r(82561),n=r(93095),o=r(15594),l=r(9815),d=r(45226),c=r(17124),u=r(52067),m=r(88957),p="Tabs",[h,x]=(0,n.b)(p,[o.Pc]),f=(0,o.Pc)(),[g,v]=h(p),b=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,onValueChange:i,defaultValue:n,orientation:o="horizontal",dir:l,activationMode:p="automatic",...h}=e,x=(0,c.gm)(l),[f,v]=(0,u.T)({prop:s,onChange:i,defaultProp:n});return(0,a.jsx)(g,{scope:r,baseId:(0,m.M)(),value:f,onValueChange:v,orientation:o,dir:x,activationMode:p,children:(0,a.jsx)(d.WV.div,{dir:x,"data-orientation":o,...h,ref:t})})});b.displayName=p;var y="TabsList",j=s.forwardRef((e,t)=>{let{__scopeTabs:r,loop:s=!0,...i}=e,n=v(y,r),l=f(r);return(0,a.jsx)(o.fC,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:s,children:(0,a.jsx)(d.WV.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:t})})});j.displayName=y;var w="TabsTrigger",N=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,disabled:n=!1,...l}=e,c=v(w,r),u=f(r),m=C(c.baseId,s),p=P(c.baseId,s),h=s===c.value;return(0,a.jsx)(o.ck,{asChild:!0,...u,focusable:!n,active:h,children:(0,a.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":p,"data-state":h?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:m,...l,ref:t,onMouseDown:(0,i.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(s)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(s)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;h||n||!e||c.onValueChange(s)})})})});N.displayName=w;var k="TabsContent",_=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:i,forceMount:n,children:o,...c}=e,u=v(k,r),m=C(u.baseId,i),p=P(u.baseId,i),h=i===u.value,x=s.useRef(h);return s.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(l.z,{present:n||h,children:({present:r})=>(0,a.jsx)(d.WV.div,{"data-state":h?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":m,hidden:!r,id:p,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:r&&o})})});function C(e,t){return`${e}-trigger-${t}`}function P(e,t){return`${e}-content-${t}`}_.displayName=k;var S=r(51223);let E=b,D=s.forwardRef(({className:e,...t},r)=>a.jsx(j,{ref:r,className:(0,S.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",e),...t}));D.displayName=j.displayName;let q=s.forwardRef(({className:e,...t},r)=>a.jsx(N,{ref:r,className:(0,S.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",e),...t}));q.displayName=N.displayName;let Z=s.forwardRef(({className:e,...t},r)=>a.jsx(_,{ref:r,className:(0,S.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));Z.displayName=_.displayName},69701:(e,t,r)=>{"use strict";r.d(t,{eI:()=>o,s3:()=>d});var a=r(65815);let s=null,i="https://eqghwtejdnzgopmcjlho.supabase.co",n=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!i)throw Error("Missing Supabase environment variables");let o=()=>{throw Error("createClient should only be called on the client side")},l=()=>{if(!s){if(!n)throw Error("SUPABASE_SERVICE_ROLE_KEY is required for admin operations");s=(0,a.eI)(i,n,{auth:{autoRefreshToken:!1,persistSession:!1}})}return s},d=()=>l()},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var a=r(41135),s=r(31009);function i(...e){return(0,s.m6)((0,a.W)(e))}},38256:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/app/dashboard/page.tsx#default`)},61158:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>o});var a=r(19510),s=r(45317),i=r.n(s);let n=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/components/tempo-init.tsx#TempoInit`);r(5023);let o={title:"Tempo - Modern SaaS Starter",description:"A modern full-stack starter template powered by Next.js"};function l({children:e}){return a.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:(0,a.jsxs)("body",{className:i().className,children:[e,a.jsx(n,{})]})})}},73881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(66621);let s=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[948,837,753,815,705,194],()=>r(29925));module.exports=a})();