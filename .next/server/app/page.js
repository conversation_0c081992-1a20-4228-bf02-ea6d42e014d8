(()=>{var e={};e.id=931,e.ids=[931],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},22250:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>h,pages:()=>c,routeModule:()=>p,tree:()=>d}),s(35480),s(61158),s(35866);var i=s(23191),r=s(88716),a=s(37922),n=s.n(a),l=s(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,35480)),"/Users/<USER>/Nextjs/marriage-map/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,61158)),"/Users/<USER>/Nextjs/marriage-map/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Nextjs/marriage-map/src/app/page.tsx"],h="/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},77833:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,12994,23)),Promise.resolve().then(s.t.bind(s,96114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,79671,23)),Promise.resolve().then(s.t.bind(s,41868,23)),Promise.resolve().then(s.t.bind(s,84759,23))},42896:(e,t,s)=>{Promise.resolve().then(s.bind(s,94750))},57546:(e,t,s)=>{Promise.resolve().then(s.bind(s,69311))},91377:e=>{var t="function"==typeof Float32Array;function s(e,t,s){return(((1-3*s+3*t)*e+(3*s-6*t))*e+3*t)*e}function i(e,t,s){return 3*(1-3*s+3*t)*e*e+2*(3*s-6*t)*e+3*t}function r(e){return e}e.exports=function(e,a,n,l){if(!(0<=e&&e<=1&&0<=n&&n<=1))throw Error("bezier x values must be in [0, 1] range");if(e===a&&n===l)return r;for(var o=t?new Float32Array(11):Array(11),d=0;d<11;++d)o[d]=s(.1*d,e,n);return function(t){return 0===t?0:1===t?1:s(function(t){for(var r=0,a=1;10!==a&&o[a]<=t;++a)r+=.1;var l=r+(t-o[--a])/(o[a+1]-o[a])*.1,d=i(l,e,n);return d>=.001?function(e,t,r,a){for(var n=0;n<4;++n){var l=i(t,r,a);if(0===l)break;var o=s(t,r,a)-e;t-=o/l}return t}(t,l,e,n):0===d?l:function(e,t,i,r,a){var n,l,o=0;do(n=s(l=t+(i-t)/2,r,a)-e)>0?i=l:t=l;while(Math.abs(n)>1e-7&&++o<10);return l}(t,r,r+.1,e,n)}(t),a,l)}}},30361:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});let i=(0,s(62881).Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},67427:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});let i=(0,s(62881).Z)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},39730:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});let i=(0,s(62881).Z)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},24061:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});let i=(0,s(62881).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},69311:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ed});var i,r,a,n,l,o,d=s(10326),c=s(17577),h=s.n(c),u=s(90434),p=s(91664),m=s(29752),f=s(99744),x=s(67427),g=s(24061);let v=(0,s(62881).Z)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var b=s(39730),w=s(30361),y=s(91377),j=s.n(y),E=function(e){this.startX=e.startX,this.startY=e.startY,this.endX=e.endX,this.endY=e.endY,this.totalX=this.endX-this.startX,this.totalY=this.endY-this.startY,this.startMultiplierX=e.startMultiplierX||1,this.endMultiplierX=e.endMultiplierX||1,this.startMultiplierY=e.startMultiplierY||1,this.endMultiplierY=e.endMultiplierY||1};function N(){return(N=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(e[i]=s[i])}return e}).apply(this,arguments)}(function(e){e.speed="speed",e.translateX="translateX",e.translateY="translateY",e.rotate="rotate",e.rotateX="rotateX",e.rotateY="rotateY",e.rotateZ="rotateZ",e.scale="scale",e.scaleX="scaleX",e.scaleY="scaleY",e.scaleZ="scaleZ",e.opacity="opacity"})(i||(i={})),function(e){e.px="px",e["%"]="%",e.vh="vh",e.vw="vw"}(r||(r={})),function(e){e.deg="deg",e.turn="turn",e.rad="rad"}(a||(a={})),(n||(n={}))[""]="",function(e){e.vertical="vertical",e.horizontal="horizontal"}(l||(l={})),function(e){e.ease="ease",e.easeIn="easeIn",e.easeOut="easeOut",e.easeInOut="easeInOut",e.easeInQuad="easeInQuad",e.easeInCubic="easeInCubic",e.easeInQuart="easeInQuart",e.easeInQuint="easeInQuint",e.easeInSine="easeInSine",e.easeInExpo="easeInExpo",e.easeInCirc="easeInCirc",e.easeOutQuad="easeOutQuad",e.easeOutCubic="easeOutCubic",e.easeOutQuart="easeOutQuart",e.easeOutQuint="easeOutQuint",e.easeOutSine="easeOutSine",e.easeOutExpo="easeOutExpo",e.easeOutCirc="easeOutCirc",e.easeInOutQuad="easeInOutQuad",e.easeInOutCubic="easeInOutCubic",e.easeInOutQuart="easeInOutQuart",e.easeInOutQuint="easeInOutQuint",e.easeInOutSine="easeInOutSine",e.easeInOutExpo="easeInOutExpo",e.easeInOutCirc="easeInOutCirc",e.easeInBack="easeInBack",e.easeOutBack="easeOutBack",e.easeInOutBack="easeInOutBack"}(o||(o={}));var C=0,_=function(){function e(e){var t=e.el.getBoundingClientRect();if(e.view.scrollContainer){var s=e.view.scrollContainer.getBoundingClientRect();t=N({},t,{top:t.top-s.top,right:t.right-s.left,bottom:t.bottom-s.top,left:t.left-s.left})}this.height=e.el.offsetHeight,this.width=e.el.offsetWidth,this.left=t.left,this.right=t.right,this.top=t.top,this.bottom=t.bottom,e.rootMargin&&this._setRectWithRootMargin(e.rootMargin)}return e.prototype._setRectWithRootMargin=function(e){var t=e.top+e.bottom,s=e.left+e.right;this.top-=e.top,this.right+=e.right,this.bottom+=e.bottom,this.left-=e.left,this.height+=t,this.width+=s},e}(),P=[n[""],r.px,r["%"],r.vh,r.vw,a.deg,a.turn,a.rad];function S(e,t){void 0===t&&(t=r["%"]);var s={value:0,unit:t};if(void 0===e)return s;if(!("number"==typeof e||"string"==typeof e))throw Error("Invalid value provided. Must provide a value as a string or number");if(e=String(e),s.value=parseFloat(e),s.unit=e.match(/[\d.\-+]*\s*(.*)/)[1]||t,!P.includes(s.unit))throw Error("Invalid unit provided.");return s}var I={ease:[.25,.1,.25,1],easeIn:[.42,0,1,1],easeOut:[0,0,.58,1],easeInOut:[.42,0,.58,1],easeInQuad:[.55,.085,.68,.53],easeInCubic:[.55,.055,.675,.19],easeInQuart:[.895,.03,.685,.22],easeInQuint:[.755,.05,.855,.06],easeInSine:[.47,0,.745,.715],easeInExpo:[.95,.05,.795,.035],easeInCirc:[.6,.04,.98,.335],easeOutQuad:[.25,.46,.45,.94],easeOutCubic:[.215,.61,.355,1],easeOutQuart:[.165,.84,.44,1],easeOutQuint:[.23,1,.32,1],easeOutSine:[.39,.575,.565,1],easeOutExpo:[.19,1,.22,1],easeOutCirc:[.075,.82,.165,1],easeInOutQuad:[.455,.03,.515,.955],easeInOutCubic:[.645,.045,.355,1],easeInOutQuart:[.77,0,.175,1],easeInOutQuint:[.86,0,.07,1],easeInOutSine:[.445,.05,.55,.95],easeInOutExpo:[1,0,0,1],easeInOutCirc:[.785,.135,.15,.86],easeInBack:[.6,-.28,.735,.045],easeOutBack:[.175,.885,.32,1.275],easeInOutBack:[.68,-.55,.265,1.55]};function O(e){if(Array.isArray(e))return j()(e[0],e[1],e[2],e[3]);if("string"==typeof e&&void 0!==I[e]){var t=I[e];return j()(t[0],t[1],t[2],t[3])}}var M=Object.values(i),k={speed:"px",translateX:"%",translateY:"%",rotate:"deg",rotateX:"deg",rotateY:"deg",rotateZ:"deg",scale:"",scaleX:"",scaleY:"",scaleZ:"",opacity:""};function A(e,t){var s={};return M.forEach(function(i){var r=k[i];if("number"==typeof(null==e?void 0:e[i])){var a=null==e?void 0:e[i],n=S(10*(a||0)+"px"),o=S(-10*(a||0)+"px"),d={start:n.value,end:o.value,unit:n.unit};t===l.vertical&&(s.translateY=d),t===l.horizontal&&(s.translateX=d)}if(Array.isArray(null==e?void 0:e[i])){var c=null==e?void 0:e[i];if(void 0!==c[0]&&void 0!==c[1]){var h=S(null==c?void 0:c[0],r),u=S(null==c?void 0:c[1],r),p=O(null==c?void 0:c[2]);if(s[i]={start:h.value,end:u.value,unit:h.unit,easing:p},h.unit!==u.unit)throw Error("Must provide matching units for the min and max offset values of each axis.")}}}),s}function Y(e,t,s,i){var r=(s-e)/t;return i&&(r=i(r)),r}function X(e,t){var s,i;return{value:(s="function"==typeof e.easing?e.easing(t):t,i=(null==e?void 0:e.start)||0,(((null==e?void 0:e.end)||0)-i)*(s-0)/1+i),unit:null==e?void 0:e.unit}}var z=Object.values(i).filter(function(e){return"opacity"!==e});function Z(e){var t=e.el;t&&(t.style.transform="",t.style.opacity="")}function R(e,t,s){return Math.max(s/(s+(Math.abs(e)+Math.abs(t))*(t>e?-1:1)),1)}function W(e,t){var s=e.start,i=e.end,r=e.unit;if("%"===r){var a=t/100;s*=a,i*=a}if("vw"===r){var n=s/100,l=i/100;s=window.innerWidth*n,i=window.innerWidth*l}if("vh"===r){var o=s/100,d=i/100;s=window.innerHeight*o,i=window.innerHeight*d}return{start:s,end:i}}var H={start:0,end:0,unit:""},V=function(e,t,s){return Math.min(Math.max(e,t),s)},L=function(){function e(e){var t,s;this.el=e.el,this.props=e.props,this.scrollAxis=e.scrollAxis,this.disabledParallaxController=e.disabledParallaxController||!1,this.id=++C,this.effects=A(this.props,this.scrollAxis),this.isInView=null,this.progress=0,this._setElementEasing(e.props.easing),t=e.el,s=Object.keys(this.effects).includes("opacity"),t.style.willChange="transform"+(s?",opacity":"")}var t=e.prototype;return t.updateProps=function(e){return this.props=N({},this.props,e),this.effects=A(e,this.scrollAxis),this._setElementEasing(e.easing),this},t.setCachedAttributes=function(e,t){Z(this),this.rect=new _({el:this.props.targetElement||this.el,rootMargin:this.props.rootMargin,view:e});var s,i,r,a,n,o,d,c,h,u,p,m,f=(s=this.props,i=this.effects,r=this.scrollAxis,!s.rootMargin&&!s.targetElement&&!s.shouldDisableScalingTranslations&&(!!i.translateX&&r===l.horizontal||!!i.translateY&&r===l.vertical));return"number"==typeof this.props.startScroll&&"number"==typeof this.props.endScroll?this.limits=new E({startX:this.props.startScroll,startY:this.props.startScroll,endX:this.props.endScroll,endY:this.props.endScroll}):f?(this.limits=function(e,t,s,i,r,a){var n=s.translateX||H,o=s.translateY||H,d=W(n,e.width),c=d.start,h=d.end,u=W(o,e.height),p=u.start,m=u.end,f=e.top-t.height,x=e.left-t.width,g=e.bottom,v=e.right,b=1,w=1;r===l.vertical&&(w=b=R(p,m,t.height+e.height));var y=1,j=1;if(r===l.horizontal&&(j=y=R(c,h,t.width+e.width)),p<0&&(f+=p*b),m>0&&(g+=m*w),c<0&&(x+=c*y),h>0&&(v+=h*j),x+=i.x,v+=i.x,f+=i.y,g+=i.y,a){var N=i.y+e.top<t.height,C=i.x+e.left<t.width,_=i.y+e.bottom>t.scrollHeight-t.height,P=i.x+e.right>t.scrollWidth-t.height;N&&_&&(b=1,w=1,f=0,g=t.scrollHeight-t.height),C&&P&&(y=1,j=1,x=0,v=t.scrollWidth-t.width),!N&&_&&(f=e.top-t.height+i.y,b=R(p,m,(g=t.scrollHeight-t.height)-f),w=1,p<0&&(f+=p*b)),!C&&P&&(x=e.left-t.width+i.x,y=R(c,h,(v=t.scrollWidth-t.width)-x),j=1,c<0&&(x+=c*y)),N&&!_&&(f=0,b=1,w=R(p,m,(g=e.bottom+i.y)-f),m>0&&(g+=m*w)),C&&!P&&(x=0,y=1,j=R(c,h,(v=e.right+i.x)-x),h>0&&(v+=h*j))}return new E({startX:x,startY:f,endX:v,endY:g,startMultiplierX:y,endMultiplierX:j,startMultiplierY:b,endMultiplierY:w})}(this.rect,e,this.effects,t,this.scrollAxis,this.props.shouldAlwaysCompleteAnimation),this.scaledEffects=(a=this.effects,n=this.limits,(o=N({},a)).translateX&&(o.translateX=N({},a.translateX,{start:o.translateX.start*n.startMultiplierX,end:o.translateX.end*n.endMultiplierX})),o.translateY&&(o.translateY=N({},a.translateY,{start:o.translateY.start*n.startMultiplierY,end:o.translateY.end*n.endMultiplierY})),o)):this.limits=(d=this.rect,c=this.props.shouldAlwaysCompleteAnimation,h=d.top-e.height,u=d.left-e.width,p=d.bottom,m=d.right,u+=t.x,m+=t.x,h+=t.y,p+=t.y,c&&(t.y+d.top<e.height&&(h=0),t.x+d.left<e.width&&(u=0),p>e.scrollHeight-e.height&&(p=e.scrollHeight-e.height),m>e.scrollWidth-e.width&&(m=e.scrollWidth-e.width)),new E({startX:u,startY:h,endX:m,endY:p})),this._setElementStyles(),this},t._updateElementIsInView=function(e){var t=null===this.isInView;e!==this.isInView&&(e?this.props.onEnter&&this.props.onEnter(this):!t&&(this._setFinalProgress(),this._setElementStyles(),this.props.onExit&&this.props.onExit(this))),this.isInView=e},t._setFinalProgress=function(){var e=V(Math.round(this.progress),0,1);this._updateElementProgress(e)},t._setElementStyles=function(){this.props.disabled||this.disabledParallaxController||function(e,t,s){if(s){var i,r=z.reduce(function(s,i){var r=e[i]&&X(e[i],t);return void 0===r||void 0===r.value||void 0===r.unit?s:s+(i+"("+r.value)+r.unit+")"},""),a=void 0===(i=e.opacity&&X(e.opacity,t))||void 0===i.value||void 0===i.unit?"":""+i.value;s.style.transform=r,s.style.opacity=a}}(this.scaledEffects||this.effects,this.progress,this.el)},t._updateElementProgress=function(e){this.progress=e,this.props.onProgressChange&&this.props.onProgressChange(this.progress),this.props.onChange&&this.props.onChange(this)},t._setElementEasing=function(e){this.easing=O(e)},t.updateElementOptions=function(e){this.scrollAxis=e.scrollAxis,this.disabledParallaxController=e.disabledParallaxController||!1},t.updatePosition=function(e){if(!this.limits)return this;var t=this.scrollAxis===l.vertical,s=null===this.isInView,i=t?this.limits.startY:this.limits.startX,r=t?this.limits.endY:this.limits.endX,a=t?this.limits.totalY:this.limits.totalX,n=t?e.y:e.x,o=n>=i&&n<=r;if(this._updateElementIsInView(o),o){var d=Y(i,a,n,this.easing);this._updateElementProgress(d),this._setElementStyles()}else s&&(this.progress=V(Math.round(Y(i,a,n,this.easing)),0,1),this._setElementStyles());return this},e}(),Q=function(){function e(e){this.scrollContainer=e.scrollContainer,this.width=e.width,this.height=e.height,this.scrollHeight=e.scrollHeight,this.scrollWidth=e.scrollWidth}var t=e.prototype;return t.hasChanged=function(e){return e.width!==this.width||e.height!==this.height||e.scrollWidth!==this.scrollWidth||e.scrollHeight!==this.scrollHeight},t.setSize=function(e){return this.width=e.width,this.height=e.height,this.scrollHeight=e.scrollHeight,this.scrollWidth=e.scrollWidth,this},e}(),D=function(){function e(e,t){this.x=e,this.y=t,this.dx=0,this.dy=0}return e.prototype.setScroll=function(e,t){return this.dx=e-this.x,this.dy=t-this.y,this.x=e,this.y=t,this},e}(),T=function(){function e(e){var t=e.scrollAxis,s=void 0===t?l.vertical:t,i=e.scrollContainer,r=e.disabled;this.disabled=void 0!==r&&r,this.scrollAxis=s,this.elements=[],this._hasScrollContainer=!!i,this.viewEl=null!=i?i:window;var a=this._getScrollPosition(),n=a[0],o=a[1];this.scroll=new D(n,o),this.view=new Q({width:0,height:0,scrollWidth:0,scrollHeight:0,scrollContainer:this._hasScrollContainer?i:void 0}),this._ticking=!1,this._supportsPassive=function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){return e=!0,!0}});window.addEventListener("test",null,t),window.removeEventListener("test",null,t)}catch(e){}return e}(),this._bindAllMethods(),this.disabled||(this._addListeners(this.viewEl),this._addResizeObserver(),this._setViewSize())}e.init=function(t){if(!("undefined"!=typeof window))throw Error("Looks like ParallaxController.init() was called on the server. This method must be called on the client.");return new e(t)};var t=e.prototype;return t._bindAllMethods=function(){var e=this;["_addListeners","_removeListeners","_getScrollPosition","_handleScroll","_handleUpdateCache","_updateAllElements","_updateElementPosition","_setViewSize","_addResizeObserver","_checkIfViewHasChanged","_getViewParams","getElements","createElement","removeElementById","resetElementStyles","updateElementPropsById","update","updateScrollContainer","destroy"].forEach(function(t){e[t]=e[t].bind(e)})},t._addListeners=function(e){e.addEventListener("scroll",this._handleScroll,!!this._supportsPassive&&{passive:!0}),window.addEventListener("resize",this._handleUpdateCache,!1),window.addEventListener("blur",this._handleUpdateCache,!1),window.addEventListener("focus",this._handleUpdateCache,!1),window.addEventListener("load",this._handleUpdateCache,!1)},t._removeListeners=function(e){var t;e.removeEventListener("scroll",this._handleScroll,!1),window.removeEventListener("resize",this._handleUpdateCache,!1),window.removeEventListener("blur",this._handleUpdateCache,!1),window.removeEventListener("focus",this._handleUpdateCache,!1),window.removeEventListener("load",this._handleUpdateCache,!1),null==(t=this._resizeObserver)||t.disconnect()},t._addResizeObserver=function(){var e=this;try{var t=this._hasScrollContainer?this.viewEl:document.documentElement;this._resizeObserver=new ResizeObserver(function(){return e.update()}),this._resizeObserver.observe(t)}catch(e){console.warn("Failed to create the resize observer in the ParallaxContoller")}},t._getScrollPosition=function(){return[this._hasScrollContainer?this.viewEl.scrollLeft:window.pageXOffset,this._hasScrollContainer?this.viewEl.scrollTop:window.pageYOffset]},t._handleScroll=function(){var e,t=this._getScrollPosition(),s=t[0],i=t[1];this.scroll.setScroll(s,i),!this._ticking&&(null==(e=this.elements)?void 0:e.length)>0&&(this._ticking=!0,window.requestAnimationFrame(this._updateAllElements))},t._handleUpdateCache=function(){this._setViewSize(),this._updateAllElements({updateCache:!0})},t._updateAllElements=function(e){var t=this,s=(void 0===e?{}:e).updateCache;this.elements&&this.elements.forEach(function(e){s&&e.setCachedAttributes(t.view,t.scroll),t._updateElementPosition(e)}),this._ticking=!1},t._updateElementPosition=function(e){e.props.disabled||this.disabled||e.updatePosition(this.scroll)},t._getViewParams=function(){if(this._hasScrollContainer){var e=this.viewEl.offsetWidth,t=this.viewEl.offsetHeight,s=this.viewEl.scrollHeight,i=this.viewEl.scrollWidth;return this.view.setSize({width:e,height:t,scrollHeight:s,scrollWidth:i})}var r=document.documentElement;return{width:window.innerWidth||r.clientWidth,height:window.innerHeight||r.clientHeight,scrollHeight:r.scrollHeight,scrollWidth:r.scrollWidth}},t._setViewSize=function(){return this.view.setSize(this._getViewParams())},t._checkIfViewHasChanged=function(){return this.view.hasChanged(this._getViewParams())},t.getElements=function(){return this.elements},t.createElement=function(e){var t=new L(N({},e,{scrollAxis:this.scrollAxis,disabledParallaxController:this.disabled}));return t.setCachedAttributes(this.view,this.scroll),this.elements=this.elements?[].concat(this.elements,[t]):[t],this._updateElementPosition(t),this._checkIfViewHasChanged()&&this.update(),t},t.removeElementById=function(e){this.elements&&(this.elements=this.elements.filter(function(t){return t.id!==e}))},t.updateElementPropsById=function(e,t){this.elements&&(this.elements=this.elements.map(function(s){return s.id===e?s.updateProps(t):s})),this.update()},t.resetElementStyles=function(e){Z(e)},t.update=function(){var e=this._getScrollPosition(),t=e[0],s=e[1];this.scroll.setScroll(t,s),this._setViewSize(),this._updateAllElements({updateCache:!0})},t.updateScrollContainer=function(e){this._removeListeners(this.viewEl),this.viewEl=e,this._hasScrollContainer=!!e,this.view=new Q({width:0,height:0,scrollWidth:0,scrollHeight:0,scrollContainer:e}),this._setViewSize(),this._addListeners(this.viewEl),this._updateAllElements({updateCache:!0})},t.disableParallaxController=function(){this.disabled=!0,this._removeListeners(this.viewEl),this.elements&&this.elements.forEach(function(e){return Z(e)})},t.enableParallaxController=function(){var e=this;this.disabled=!1,this.elements&&this.elements.forEach(function(t){return t.updateElementOptions({disabledParallaxController:!1,scrollAxis:e.scrollAxis})}),this._addListeners(this.viewEl),this._addResizeObserver(),this._setViewSize()},t.disableAllElements=function(){console.warn("deprecated: use disableParallaxController() instead"),this.elements&&(this.elements=this.elements.map(function(e){return e.updateProps({disabled:!0})})),this.update()},t.enableAllElements=function(){console.warn("deprecated: use enableParallaxController() instead"),this.elements&&(this.elements=this.elements.map(function(e){return e.updateProps({disabled:!1})})),this.update()},t.destroy=function(){this._removeListeners(this.viewEl),this.elements&&this.elements.forEach(function(e){return Z(e)}),this.elements=void 0},e}();function U(){return(U=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(e[i]=s[i])}return e}).apply(this,arguments)}function B(e,t){if(null==e)return{};var s,i,r={},a=Object.keys(e);for(i=0;i<a.length;i++)s=a[i],t.indexOf(s)>=0||(r[s]=e[s]);return r}var F=["disabled","easing","endScroll","onChange","onEnter","onExit","onProgressChange","opacity","rootMargin","rotate","rotateX","rotateY","rotateZ","scale","scaleX","scaleY","scaleZ","shouldAlwaysCompleteAnimation","shouldDisableScalingTranslations","speed","startScroll","targetElement","translateX","translateY"];function q(e){var t,s=e.disabled,i=e.easing,r=e.endScroll,a=e.onChange,n=e.onEnter,l=e.onExit,o=e.onProgressChange,d=e.opacity,c=e.rootMargin,h=e.rotate,u=e.rotateX,p=e.rotateY,m=e.rotateZ,f=e.scale,x=e.scaleX,g=e.scaleY,v=e.scaleZ,b=e.shouldAlwaysCompleteAnimation,w=e.shouldDisableScalingTranslations,y=e.speed,j=e.startScroll,E=e.targetElement,N=e.translateX,C=e.translateY,_=B(e,F);return{parallaxProps:(Object.keys(t={disabled:s,easing:i,endScroll:r,onChange:a,onEnter:n,onExit:l,onProgressChange:o,opacity:d,rootMargin:c,rotate:h,rotateX:u,rotateY:p,rotateZ:m,scale:f,scaleX:x,scaleY:g,scaleZ:v,shouldAlwaysCompleteAnimation:b,shouldDisableScalingTranslations:w,speed:y,startScroll:j,targetElement:E,translateX:N,translateY:C}).forEach(function(e){return void 0===t[e]&&delete t[e]}),t),rest:_}}var G=h().createContext(null);function $(e){var t=function(){var e=(0,c.useContext)(G);if("undefined"==typeof window)return null;if(!e)throw Error("Could not find `react-scroll-parallax` context value. Please ensure the component is wrapped in a <ParallaxProvider>");return e}(),s=(0,c.useRef)(null),i=q(e).parallaxProps;(0,c.useEffect)(function(){var e=t instanceof T;if("undefined"!=typeof window&&!t&&!e)throw Error("Must wrap your application's <Parallax /> components in a <ParallaxProvider />.")},[t]);var r=(0,c.useState)(),a=r[0],n=r[1];return(0,c.useEffect)(function(){var e;if(s.current instanceof HTMLElement){var r={el:s.current,props:i};n(e=null==t?void 0:t.createElement(r))}else throw Error("You must assign the ref returned by the useParallax() hook to an HTML Element.");return function(){e&&(null==t||t.removeElementById(e.id))}},[]),(0,c.useEffect)(function(){a&&(e.disabled&&(null==t||t.resetElementStyles(a)),null==t||t.updateElementPropsById(a.id,i))},[e.disabled,e.easing,e.endScroll,e.onChange,e.onEnter,e.onExit,e.onProgressChange,e.opacity,e.rootMargin,e.rotate,e.rotateX,e.rotateY,e.rotateZ,e.scale,e.scaleX,e.scaleY,e.scaleZ,e.shouldAlwaysCompleteAnimation,e.shouldDisableScalingTranslations,e.speed,e.startScroll,e.targetElement,e.translateX,e.translateY]),{ref:s,controller:t,element:a}}function K(e){var t=q(e),s=t.parallaxProps,i=t.rest,r=$(s).ref;return h().createElement("div",Object.assign({ref:r},i),e.children)}var J={height:0},ee=["children","disabled","style","expanded","image","testId"],et={position:"absolute",top:0,left:0,right:0,bottom:0},es=function(e){var t=q(e),s=t.parallaxProps,i=t.rest,r=i.style,a=i.expanded,n=i.testId,l=B(i,ee),o=e.image?{backgroundImage:"url("+e.image+")",backgroundPosition:"center",backgroundSize:"cover"}:{},d=void 0===a||a?function(e){if(Array.isArray(e.translateY)){var t=S(e.translateY[0]),s=S(e.translateY[1]);if("px"===t.unit&&"px"===s.unit)return{top:-1*Math.abs(s.value)+"px",bottom:-1*Math.abs(t.value)+"px"};if("%"===t.unit&&"%"===s.unit){var i,r,a=null!=(i=null==(r=e.targetElement)?void 0:r.getBoundingClientRect())?i:J;return{top:-1*Math.abs(.01*a.height*s.value)+"px",bottom:-1*Math.abs(.01*a.height*t.value)+"px"}}}if(e.speed){var n=-10*Math.abs(e.speed||0);return{top:n+"px",bottom:n+"px"}}return{}}(e):{},c=$(U({targetElement:e.targetElement,shouldDisableScalingTranslations:!0},s));return h().createElement("div",Object.assign({"data-testid":n,ref:c.ref,style:U({},o,et,d,r)},l),i.children)},ei=["disabled","style","layers"],er={position:"relative",overflow:"hidden",width:"100%"},ea=function(e){var t=(0,c.useState)(null),s=t[0],i=t[1],r=(0,c.useRef)(null);(0,c.useEffect)(function(){i(r.current)},[]);var a=e.style,n=e.layers,l=void 0===n?[]:n,o=B(e,ei);return h().createElement("div",Object.assign({ref:r,style:U({},er,a)},o),s&&l&&l.length>0?l.map(function(e,t){return h().createElement(es,Object.assign({},e,{targetElement:s,key:"layer-"+t,testId:"layer-"+t}))}):null,s?h().Children.map(e.children,function(e){return(null==e?void 0:e.type)===es?h().cloneElement(e,{targetElement:s}):e}):null)};function en(e){var t,s=(0,c.useRef)(null);return s.current||(s.current=(t={scrollAxis:e.scrollAxis||l.vertical,scrollContainer:e.scrollContainer,disabled:e.isDisabled},"undefined"!=typeof window?T.init(t):null)),(0,c.useEffect)(function(){e.scrollContainer&&s.current&&s.current.updateScrollContainer(e.scrollContainer)},[e.scrollContainer,s.current]),(0,c.useEffect)(function(){e.isDisabled&&s.current&&s.current.disableParallaxController(),!e.isDisabled&&s.current&&s.current.enableParallaxController()},[e.isDisabled,s.current]),(0,c.useEffect)(function(){return function(){(null==s?void 0:s.current)&&(null==s||s.current.destroy())}},[]),h().createElement(G.Provider,{value:s.current},e.children)}let el=({children:e,speed:t=10,className:s=""})=>{let{ref:i}=$({speed:t});return d.jsx("div",{ref:i,className:s,children:e})},eo=({children:e,bgImage:t,className:s=""})=>(0,d.jsxs)(ea,{layers:[{image:t,speed:-15,shouldAlwaysCompleteAnimation:!0,expanded:!1}],className:`relative h-[500px] flex items-center justify-center ${s}`,children:[d.jsx("div",{className:"absolute inset-0 bg-black/30"}),d.jsx("div",{className:"relative z-10 container mx-auto px-4",children:e})]});function ed(){return d.jsx(en,{children:(0,d.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[d.jsx("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,d.jsxs)("div",{className:"container flex h-16 items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[d.jsx(x.Z,{className:"h-6 w-6 text-primary"}),d.jsx("span",{className:"text-xl font-bold",children:"Marriage Assessment"})]}),(0,d.jsxs)("nav",{className:"hidden md:flex items-center gap-6",children:[d.jsx(u.default,{href:"#features",className:"text-sm font-medium hover:text-primary transition-colors",children:"Features"}),d.jsx(u.default,{href:"#how-it-works",className:"text-sm font-medium hover:text-primary transition-colors",children:"How It Works"}),d.jsx(u.default,{href:"#testimonials",className:"text-sm font-medium hover:text-primary transition-colors",children:"Testimonials"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[d.jsx(p.z,{variant:"outline",size:"sm",asChild:!0,children:d.jsx(u.default,{href:"/login",children:"Log In"})}),d.jsx(p.z,{size:"sm",asChild:!0,children:d.jsx(u.default,{href:"/login?tab=register",children:"Sign Up"})})]})]})}),d.jsx("section",{className:"container py-12 md:py-24 lg:py-32",children:(0,d.jsxs)("div",{className:"flex flex-col items-center text-center space-y-4",children:[d.jsx("h1",{className:"text-3xl md:text-5xl font-bold tracking-tighter",children:"Build a Stronger Foundation for Your Marriage"}),d.jsx("p",{className:"text-muted-foreground md:text-xl max-w-[42rem]",children:"Our comprehensive pre-marital assessment helps couples identify potential areas of conflict and alignment across 8 key relationship domains."})]})}),(0,d.jsxs)("section",{className:"container py-12 md:py-24",id:"features",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[d.jsx("h2",{className:"text-3xl font-bold tracking-tight mb-4",children:"8 Key Relationship Domains"}),d.jsx("p",{className:"text-muted-foreground md:text-lg max-w-[42rem] mx-auto",children:"Our assessment covers the most important aspects of your relationship to provide a comprehensive view of your compatibility."})]}),d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{title:"Vision",icon:d.jsx(g.Z,{className:"h-10 w-10 text-primary"}),description:"Align your future goals and aspirations"},{title:"Finances",icon:d.jsx(v,{className:"h-10 w-10 text-primary"}),description:"Navigate financial decisions and responsibilities"},{title:"Parenting",icon:d.jsx(g.Z,{className:"h-10 w-10 text-primary"}),description:"Explore your parenting styles and expectations"},{title:"Communication",icon:d.jsx(b.Z,{className:"h-10 w-10 text-primary"}),description:"Understand your communication patterns"},{title:"Roles",icon:d.jsx(g.Z,{className:"h-10 w-10 text-primary"}),description:"Define your roles and responsibilities"},{title:"Sexuality",icon:d.jsx(x.Z,{className:"h-10 w-10 text-primary"}),description:"Discuss intimacy and expectations"},{title:"Spirituality",icon:d.jsx(v,{className:"h-10 w-10 text-primary"}),description:"Connect on spiritual beliefs and practices"},{title:"Dark Sides",icon:d.jsx(v,{className:"h-10 w-10 text-primary"}),description:"Address potential challenges and conflicts"}].map((e,t)=>d.jsx(m.Zb,{className:"flex flex-col h-full",children:(0,d.jsxs)(m.Ol,{children:[d.jsx("div",{className:"mb-2",children:e.icon}),d.jsx(m.ll,{children:e.title}),d.jsx(m.SZ,{children:e.description})]})},t))})]}),(0,d.jsxs)("section",{id:"features",className:"py-20 bg-muted/50 relative overflow-hidden",children:[d.jsx(K,{speed:-20,className:"absolute -top-40 -right-40 opacity-10",children:d.jsx("div",{className:"w-80 h-80 rounded-full bg-primary"})}),d.jsx(K,{speed:-10,className:"absolute -bottom-20 -left-20 opacity-10",children:d.jsx("div",{className:"w-64 h-64 rounded-full bg-secondary"})}),(0,d.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[d.jsx(K,{speed:5,children:(0,d.jsxs)("div",{className:"max-w-3xl mx-auto text-center mb-16",children:[d.jsx("h2",{className:"text-3xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent",children:"Powerful Features"}),d.jsx("p",{className:"text-muted-foreground text-lg",children:"Our platform offers everything you need to assess and improve your relationship."})]})}),d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[{title:"Complete Assessment",description:"Each partner completes their own assessment covering all 8 relationship domains.",icon:d.jsx(w.Z,{className:"h-10 w-10 text-primary"})},{title:"View Results",description:"Receive detailed compatibility analysis with visual representations highlighting areas of alignment and potential conflict.",icon:d.jsx(g.Z,{className:"h-10 w-10 text-primary"})},{title:"Connect with Counselor",description:"Share your results with a professional counselor for guided discussions and personalized recommendations.",icon:d.jsx(b.Z,{className:"h-10 w-10 text-primary"})}].map((e,t)=>(0,d.jsxs)("div",{className:"flex flex-col items-center text-center",children:[d.jsx("div",{className:"bg-background rounded-full p-4 mb-4",children:e.icon}),d.jsx("h3",{className:"text-xl font-bold mb-2",children:e.title}),d.jsx("p",{className:"text-muted-foreground",children:e.description})]},t))})]})]}),d.jsx("section",{className:"bg-muted py-12 md:py-24",id:"how-it-works",children:(0,d.jsxs)("div",{className:"container",children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[d.jsx("h2",{className:"text-3xl font-bold tracking-tight mb-4",children:"How It Works"}),d.jsx("p",{className:"text-muted-foreground md:text-lg max-w-[42rem] mx-auto",children:"Our simple process helps you gain valuable insights about your relationship."})]}),d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[{title:"Complete Assessment",description:"Each partner completes their own assessment covering all 8 relationship domains.",icon:d.jsx(w.Z,{className:"h-10 w-10 text-primary"})},{title:"View Results",description:"Receive detailed compatibility analysis with visual representations highlighting areas of alignment and potential conflict.",icon:d.jsx(g.Z,{className:"h-10 w-10 text-primary"})},{title:"Connect with Counselor",description:"Share your results with a professional counselor for guided discussions and personalized recommendations.",icon:d.jsx(b.Z,{className:"h-10 w-10 text-primary"})}].map((e,t)=>(0,d.jsxs)("div",{className:"flex flex-col items-center text-center",children:[d.jsx("div",{className:"bg-background rounded-full p-4 mb-4",children:e.icon}),d.jsx("h3",{className:"text-xl font-bold mb-2",children:e.title}),d.jsx("p",{className:"text-muted-foreground",children:e.description})]},t))})]})}),(0,d.jsxs)("section",{id:"testimonials",className:"py-20 bg-muted/50 relative overflow-hidden",children:[d.jsx(K,{speed:-15,className:"absolute top-0 right-0 w-1/3 h-full bg-primary/5 -z-10"}),(0,d.jsxs)("div",{className:"container mx-auto px-4 relative",children:[d.jsx(K,{speed:5,children:(0,d.jsxs)("div",{className:"max-w-3xl mx-auto text-center mb-16",children:[d.jsx("h2",{className:"text-3xl md:text-5xl font-bold mb-4",children:"What Couples Are Saying"}),d.jsx("p",{className:"text-muted-foreground text-lg",children:"Hear from couples who have improved their relationship with our assessment."})]})}),(0,d.jsxs)(f.mQ,{defaultValue:"couple1",className:"w-full max-w-3xl mx-auto",children:[(0,d.jsxs)(f.dr,{className:"grid w-full grid-cols-3",children:[d.jsx(f.SP,{value:"couple1",children:"Sarah & John"}),d.jsx(f.SP,{value:"couple2",children:"Maria & David"}),d.jsx(f.SP,{value:"couple3",children:"Lisa & Michael"})]}),d.jsx(f.nU,{value:"couple1",className:"mt-6",children:d.jsx(m.Zb,{children:(0,d.jsxs)(m.aY,{className:"pt-6",children:[d.jsx("p",{className:"italic text-muted-foreground mb-4",children:'"The assessment helped us discover differences in our financial expectations that we hadn\'t discussed before. Working through these issues with our counselor gave us the tools to create a financial plan we both feel good about."'}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[d.jsx("div",{className:"rounded-full bg-primary/10 w-12 h-12 flex items-center justify-center",children:d.jsx("span",{className:"font-bold text-primary",children:"S&J"})}),(0,d.jsxs)("div",{children:[d.jsx("p",{className:"font-medium",children:"Sarah & John"}),d.jsx("p",{className:"text-sm text-muted-foreground",children:"Engaged for 8 months"})]})]})]})})}),d.jsx(f.nU,{value:"couple2",className:"mt-6",children:d.jsx(m.Zb,{children:(0,d.jsxs)(m.aY,{className:"pt-6",children:[d.jsx("p",{className:"italic text-muted-foreground mb-4",children:'"We were surprised to see how different our parenting styles were in the assessment results. Having these conversations early has helped us develop a unified approach before we even have children."'}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[d.jsx("div",{className:"rounded-full bg-primary/10 w-12 h-12 flex items-center justify-center",children:d.jsx("span",{className:"font-bold text-primary",children:"M&D"})}),(0,d.jsxs)("div",{children:[d.jsx("p",{className:"font-medium",children:"Maria & David"}),d.jsx("p",{className:"text-sm text-muted-foreground",children:"Married for 1 year"})]})]})]})})}),d.jsx(f.nU,{value:"couple3",className:"mt-6",children:d.jsx(m.Zb,{children:(0,d.jsxs)(m.aY,{className:"pt-6",children:[d.jsx("p",{className:"italic text-muted-foreground mb-4",children:'"The spiritual domain assessment revealed that we had more common ground than we thought. It\'s given us a foundation to build our spiritual life together in a way that respects both our backgrounds."'}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[d.jsx("div",{className:"rounded-full bg-primary/10 w-12 h-12 flex items-center justify-center",children:d.jsx("span",{className:"font-bold text-primary",children:"L&M"})}),(0,d.jsxs)("div",{children:[d.jsx("p",{className:"font-medium",children:"Lisa & Michael"}),d.jsx("p",{className:"text-sm text-muted-foreground",children:"Engaged for 6 months"})]})]})]})})})]})]})]}),d.jsx(eo,{bgImage:"https://images.unsplash.com/photo-1508615039623-8a20a76ba499?q=80&w=2070&auto=format&fit=crop",className:"py-20 md:py-32",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 text-center text-white",children:[d.jsx(el,{speed:10,children:d.jsx("h2",{className:"text-3xl md:text-5xl font-bold mb-6 drop-shadow-md",children:"Ready to Strengthen Your Relationship?"})}),d.jsx(el,{speed:5,children:d.jsx("p",{className:"text-xl md:text-2xl text-white/90 mb-10 max-w-2xl mx-auto drop-shadow-md",children:"Take the first step towards a better relationship today."})}),d.jsx(K,{speed:10,children:d.jsx(u.default,{href:"/login?tab=register",children:d.jsx(p.z,{size:"lg",className:"bg-white text-primary hover:bg-white/90 px-8 py-6 text-lg font-semibold transition-all hover:scale-105 shadow-lg",children:"Get Started for Free"})})})]})}),d.jsx("footer",{className:"border-t py-12 bg-background",children:(0,d.jsxs)(K,{speed:5,children:[" ",d.jsx("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-4 md:mb-0",children:[d.jsx(x.Z,{className:"h-6 w-6 text-primary"}),d.jsx("span",{className:"text-lg font-bold",children:"Marriage Assessment"})]}),(0,d.jsxs)("p",{className:"text-sm text-muted-foreground",children:["\xa9 ",new Date().getFullYear()," Marriage Assessment. All rights reserved."]})]})})]})})]})})}},94750:(e,t,s)=>{"use strict";function i(){return null}s.d(t,{TempoInit:()=>i}),s(65411),s(17577)},91664:(e,t,s)=>{"use strict";s.d(t,{z:()=>d});var i=s(10326),r=s(17577),a=s(34214),n=s(79360),l=s(51223);let o=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef(({className:e,variant:t,size:s,asChild:r=!1,...n},d)=>{let c=r?a.g7:"button";return i.jsx(c,{className:(0,l.cn)(o({variant:t,size:s,className:e})),ref:d,...n})});d.displayName="Button"},29752:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>l,SZ:()=>d,Zb:()=>n,aY:()=>c,eW:()=>h,ll:()=>o});var i=s(10326),r=s(17577),a=s(51223);let n=r.forwardRef(({className:e,...t},s)=>i.jsx("div",{ref:s,className:(0,a.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));n.displayName="Card";let l=r.forwardRef(({className:e,...t},s)=>i.jsx("div",{ref:s,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let o=r.forwardRef(({className:e,...t},s)=>i.jsx("h3",{ref:s,className:(0,a.cn)("font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let d=r.forwardRef(({className:e,...t},s)=>i.jsx("p",{ref:s,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=r.forwardRef(({className:e,...t},s)=>i.jsx("div",{ref:s,className:(0,a.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let h=r.forwardRef(({className:e,...t},s)=>i.jsx("div",{ref:s,className:(0,a.cn)(" flex items-center p-6 pt-0",e),...t}));h.displayName="CardFooter"},99744:(e,t,s)=>{"use strict";s.d(t,{mQ:()=>I,nU:()=>k,dr:()=>O,SP:()=>M});var i=s(10326),r=s(17577),a=s(82561),n=s(93095),l=s(15594),o=s(9815),d=s(45226),c=s(17124),h=s(52067),u=s(88957),p="Tabs",[m,f]=(0,n.b)(p,[l.Pc]),x=(0,l.Pc)(),[g,v]=m(p),b=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:a,defaultValue:n,orientation:l="horizontal",dir:o,activationMode:p="automatic",...m}=e,f=(0,c.gm)(o),[x,v]=(0,h.T)({prop:r,onChange:a,defaultProp:n});return(0,i.jsx)(g,{scope:s,baseId:(0,u.M)(),value:x,onValueChange:v,orientation:l,dir:f,activationMode:p,children:(0,i.jsx)(d.WV.div,{dir:f,"data-orientation":l,...m,ref:t})})});b.displayName=p;var w="TabsList",y=r.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...a}=e,n=v(w,s),o=x(s);return(0,i.jsx)(l.fC,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:r,children:(0,i.jsx)(d.WV.div,{role:"tablist","aria-orientation":n.orientation,...a,ref:t})})});y.displayName=w;var j="TabsTrigger",E=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:n=!1,...o}=e,c=v(j,s),h=x(s),u=_(c.baseId,r),p=P(c.baseId,r),m=r===c.value;return(0,i.jsx)(l.ck,{asChild:!0,...h,focusable:!n,active:m,children:(0,i.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":p,"data-state":m?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...o,ref:t,onMouseDown:(0,a.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,a.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,a.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;m||n||!e||c.onValueChange(r)})})})});E.displayName=j;var N="TabsContent",C=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,forceMount:n,children:l,...c}=e,h=v(N,s),u=_(h.baseId,a),p=P(h.baseId,a),m=a===h.value,f=r.useRef(m);return r.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,i.jsx)(o.z,{present:n||m,children:({present:s})=>(0,i.jsx)(d.WV.div,{"data-state":m?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:p,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:s&&l})})});function _(e,t){return`${e}-trigger-${t}`}function P(e,t){return`${e}-content-${t}`}C.displayName=N;var S=s(51223);let I=b,O=r.forwardRef(({className:e,...t},s)=>i.jsx(y,{ref:s,className:(0,S.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",e),...t}));O.displayName=y.displayName;let M=r.forwardRef(({className:e,...t},s)=>i.jsx(E,{ref:s,className:(0,S.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",e),...t}));M.displayName=E.displayName;let k=r.forwardRef(({className:e,...t},s)=>i.jsx(C,{ref:s,className:(0,S.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));k.displayName=C.displayName},51223:(e,t,s)=>{"use strict";s.d(t,{cn:()=>a});var i=s(41135),r=s(31009);function a(...e){return(0,r.m6)((0,i.W)(e))}},61158:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>l});var i=s(19510),r=s(45317),a=s.n(r);let n=(0,s(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/components/tempo-init.tsx#TempoInit`);s(5023);let l={title:"Tempo - Modern SaaS Starter",description:"A modern full-stack starter template powered by Next.js"};function o({children:e}){return i.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:(0,i.jsxs)("body",{className:a().className,children:[e,i.jsx(n,{})]})})}},35480:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});let i=(0,s(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/app/page.tsx#default`)},73881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var i=s(66621);let r=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[948,837,753,705,194],()=>s(22250));module.exports=i})();