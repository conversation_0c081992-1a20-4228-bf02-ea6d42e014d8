(()=>{var e={};e.id=18,e.ids=[18],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},55315:e=>{"use strict";e.exports=require("path")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},27522:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(46835),t(61158),t(35866);var r=t(23191),a=t(88716),n=t(37922),i=t.n(n),l=t(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["couple",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,46835)),"/Users/<USER>/Nextjs/marriage-map/src/app/couple/dashboard/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,61158)),"/Users/<USER>/Nextjs/marriage-map/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Nextjs/marriage-map/src/app/couple/dashboard/page.tsx"],u="/couple/dashboard/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/couple/dashboard/page",pathname:"/couple/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},77833:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,12994,23)),Promise.resolve().then(t.t.bind(t,96114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,79671,23)),Promise.resolve().then(t.t.bind(t,41868,23)),Promise.resolve().then(t.t.bind(t,84759,23))},42896:(e,s,t)=>{Promise.resolve().then(t.bind(t,94750))},26058:(e,s,t)=>{Promise.resolve().then(t.bind(t,25536))},37358:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41291:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},30361:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48998:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},36283:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},67427:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},77506:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},71810:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},39730:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},8798:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},35047:(e,s,t)=>{"use strict";var r=t(77389);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},25536:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(10326),a=t(17577),n=t(35047),i=t(29752),l=t(91664),o=t(94880);t(69701);var d=t(1929),c=t(77506),u=t(67427),m=t(36283),p=t(30361),x=t(37358),h=t(39730),f=t(71810),g=t(41291),v=t(48998),j=t(8798),y=t(90434);function N(){let e=(0,n.useRouter)(),[s,t]=(0,a.useState)(!0),[N,b]=(0,a.useState)(null),[w,k]=(0,a.useState)({userName:"",coupleCode:"",partnerName:"",isConnected:!1,completedDomains:[],counselorName:"",nextSessionDate:null}),C=[{id:"vision",title:"Vision",description:"Life goals and future plans",icon:"\uD83D\uDD2D",completed:w.completedDomains.includes("vision"),status:w.completedDomains.includes("vision")?"completed":"not-started"},{id:"finances",title:"Finances",description:"Money management and financial goals",icon:"\uD83D\uDCB0",completed:w.completedDomains.includes("finances"),status:w.completedDomains.includes("finances")?"completed":"not-started"},{id:"parenting",title:"Parenting",description:"Child-rearing philosophies and approaches",icon:"\uD83D\uDC76",completed:w.completedDomains.includes("parenting"),status:w.completedDomains.includes("parenting")?"completed":"not-started"},{id:"communication",title:"Communication",description:"Styles and patterns of interaction",icon:"\uD83D\uDCAC",completed:w.completedDomains.includes("communication"),status:w.completedDomains.includes("communication")?"completed":"not-started"},{id:"roles",title:"Roles",description:"Functions and responsibilities in marriage",icon:"\uD83D\uDD04",completed:w.completedDomains.includes("roles"),status:w.completedDomains.includes("roles")?"completed":"not-started"},{id:"sexuality",title:"Sexuality",description:"Intimacy and physical relationship",icon:"❤️",completed:w.completedDomains.includes("sexuality"),status:w.completedDomains.includes("sexuality")?"completed":"not-started"},{id:"spirituality",title:"Spirituality",description:"Faith practices and spiritual growth",icon:"✝️",completed:w.completedDomains.includes("spirituality"),status:w.completedDomains.includes("spirituality")?"completed":"not-started"},{id:"darkside",title:"Dark Side",description:"Potential challenges and negative patterns",icon:"\uD83C\uDF11",completed:w.completedDomains.includes("darkside"),status:w.completedDomains.includes("darkside")?"completed":"not-started"}],D=Math.round(w.completedDomains.length/C.length*100);return s?(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[r.jsx(c.Z,{className:"h-8 w-8 animate-spin text-primary"}),r.jsx("span",{className:"ml-2",children:"Loading your dashboard..."})]}):r.jsx("div",{className:"min-h-screen bg-background",children:(0,r.jsxs)("div",{className:"flex",children:[r.jsx("div",{className:"hidden md:flex w-64 flex-col fixed inset-y-0 z-50 border-r bg-background",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-6 p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 px-2 py-4",children:[r.jsx(u.Z,{className:"h-6 w-6 text-primary"}),r.jsx("span",{className:"text-xl font-bold",children:"Couple Portal"})]}),(0,r.jsxs)("nav",{className:"flex flex-col space-y-1",children:[(0,r.jsxs)(y.default,{href:"/couple/dashboard",className:"flex items-center gap-3 rounded-lg bg-primary/10 px-3 py-2 text-primary transition-all hover:text-primary",children:[r.jsx(u.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Dashboard"})]}),(0,r.jsxs)(y.default,{href:"/couple/assessments",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[r.jsx(m.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Assessments"})]}),(0,r.jsxs)(y.default,{href:"/couple/results",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[r.jsx(p.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Results"})]}),(0,r.jsxs)(y.default,{href:"/couple/sessions",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[r.jsx(x.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Sessions"})]}),(0,r.jsxs)(y.default,{href:"/couple/messages",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[r.jsx(h.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Messages"})]}),(0,r.jsxs)("button",{onClick:d.h,className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:bg-red-50 hover:text-red-600 w-full mt-4",children:[r.jsx(f.Z,{className:"h-5 w-5"}),r.jsx("span",{children:"Logout"})]})]})]})}),r.jsx("div",{className:"flex-1 md:pl-64",children:(0,r.jsxs)("div",{className:"container py-8",children:[r.jsx("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold tracking-tight",children:["Welcome, ",w.userName]}),r.jsx("p",{className:"text-muted-foreground",children:w.isConnected?`Connected with ${w.partnerName}`:"Complete your assessment and connect with your partner"})]})}),N&&r.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(g.Z,{className:"h-4 w-4 mr-2"}),N]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"md:col-span-2 space-y-6",children:[(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(i.ll,{children:"Assessment Progress"}),r.jsx(i.SZ,{children:"Complete all 8 domains to get comprehensive insights"})]}),(0,r.jsxs)(i.aY,{children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsxs)("span",{children:[w.completedDomains.length," of ",C.length," ","completed"]}),(0,r.jsxs)("span",{children:[D,"%"]})]}),r.jsx(o.E,{value:D,className:"h-2"})]}),r.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 mt-6",children:C.map(e=>(0,r.jsxs)("div",{className:"flex flex-col items-center p-3 border rounded-lg",children:[r.jsx("div",{className:"text-2xl mb-1",children:e.icon}),r.jsx("div",{className:"text-sm font-medium",children:e.title}),r.jsx("div",{className:"mt-1",children:e.completed?r.jsx(p.Z,{className:"h-4 w-4 text-green-500"}):r.jsx(v.Z,{className:"h-4 w-4 text-amber-500"})})]},e.id))})]}),r.jsx(i.eW,{children:r.jsx(l.z,{className:"w-full",onClick:()=>e.push("/dashboard"),children:"Continue Assessment"})})]}),w.isConnected&&w.completedDomains.length>0&&(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(i.ll,{children:"Compatibility Results"}),r.jsx(i.SZ,{children:"See how your responses align with your partner's"})]}),r.jsx(i.aY,{children:r.jsx("div",{className:"text-center py-8",children:r.jsx("p",{className:"text-muted-foreground mb-4",children:w.completedDomains.length===C.length?"All assessments completed! View your detailed results below.":"Complete all assessment domains to view detailed compatibility results."})})}),r.jsx(i.eW,{children:r.jsx(l.z,{className:"w-full",disabled:w.completedDomains.length!==C.length,onClick:()=>e.push("/couple/results"),children:"View Detailed Results"})})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{children:r.jsx(i.ll,{children:"Connection Status"})}),r.jsx(i.aY,{children:w.isConnected?(0,r.jsxs)("div",{className:"flex flex-col items-center text-center",children:[r.jsx("div",{className:"bg-green-50 rounded-full p-3 mb-3",children:r.jsx(p.Z,{className:"h-6 w-6 text-green-500"})}),r.jsx("p",{className:"font-medium",children:"Connected with Partner"}),r.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:w.partnerName}),(0,r.jsxs)("div",{className:"mt-4 p-2 bg-muted rounded-md w-full",children:[r.jsx("p",{className:"text-xs font-medium",children:"Couple Code"}),r.jsx("p",{className:"font-mono text-sm",children:w.coupleCode})]})]}):(0,r.jsxs)("div",{className:"flex flex-col items-center text-center",children:[r.jsx("div",{className:"bg-amber-50 rounded-full p-3 mb-3",children:r.jsx(g.Z,{className:"h-6 w-6 text-amber-500"})}),r.jsx("p",{className:"font-medium",children:"Not Connected"}),r.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Connect with your partner to compare results"}),r.jsx(l.z,{className:"mt-4 w-full",onClick:()=>e.push("/dashboard?tab=connect"),children:"Connect Now"})]})})]}),(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{children:r.jsx(i.ll,{children:"Counselor"})}),r.jsx(i.aY,{children:w.counselorName?(0,r.jsxs)("div",{className:"flex flex-col items-center text-center",children:[r.jsx("div",{className:"bg-primary/10 rounded-full p-3 mb-3",children:r.jsx(j.Z,{className:"h-6 w-6 text-primary"})}),r.jsx("p",{className:"font-medium",children:w.counselorName}),r.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Your assigned counselor"}),r.jsx(l.z,{className:"mt-4 w-full",variant:"outline",onClick:()=>e.push("/couple/messages"),children:"Message Counselor"})]}):(0,r.jsxs)("div",{className:"flex flex-col items-center text-center",children:[r.jsx("div",{className:"bg-muted rounded-full p-3 mb-3",children:r.jsx(j.Z,{className:"h-6 w-6 text-muted-foreground"})}),r.jsx("p",{className:"font-medium",children:"No Counselor Assigned"}),r.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Complete your assessments to get matched with a counselor"})]})})]}),w.nextSessionDate&&(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{children:r.jsx(i.ll,{children:"Next Session"})}),r.jsx(i.aY,{children:(0,r.jsxs)("div",{className:"flex flex-col items-center text-center",children:[r.jsx("div",{className:"bg-primary/10 rounded-full p-3 mb-3",children:r.jsx(x.Z,{className:"h-6 w-6 text-primary"})}),r.jsx("p",{className:"font-medium",children:w.nextSessionDate.toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric"})}),r.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:w.nextSessionDate.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit"})}),r.jsx(l.z,{className:"mt-4 w-full",onClick:()=>e.push("/couple/sessions"),children:"View Details"})]})})]}),(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{children:r.jsx(i.ll,{children:"Quick Actions"})}),(0,r.jsxs)(i.aY,{className:"space-y-2",children:[(0,r.jsxs)(l.z,{variant:"outline",className:"w-full justify-start",onClick:()=>e.push("/dashboard"),children:[r.jsx(m.Z,{className:"mr-2 h-4 w-4"}),"Continue Assessment"]}),(0,r.jsxs)(l.z,{variant:"outline",className:"w-full justify-start",onClick:()=>e.push("/couple/results"),disabled:0===w.completedDomains.length,children:[r.jsx(p.Z,{className:"mr-2 h-4 w-4"}),"View Results"]}),(0,r.jsxs)(l.z,{variant:"outline",className:"w-full justify-start",onClick:()=>e.push("/couple/sessions/schedule"),disabled:!w.counselorName,children:[r.jsx(x.Z,{className:"mr-2 h-4 w-4"}),"Schedule Session"]})]})]})]})]})]})})]})})}},94750:(e,s,t)=>{"use strict";function r(){return null}t.d(s,{TempoInit:()=>r}),t(65411),t(17577)},91664:(e,s,t)=>{"use strict";t.d(s,{z:()=>d});var r=t(10326),a=t(17577),n=t(34214),i=t(79360),l=t(51223);let o=(0,i.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:s,size:t,asChild:a=!1,...i},d)=>{let c=a?n.g7:"button";return r.jsx(c,{className:(0,l.cn)(o({variant:s,size:t,className:e})),ref:d,...i})});d.displayName="Button"},29752:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>l,SZ:()=>d,Zb:()=>i,aY:()=>c,eW:()=>u,ll:()=>o});var r=t(10326),a=t(17577),n=t(51223);let i=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...s}));i.displayName="Card";let l=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("h3",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)(" flex items-center p-6 pt-0",e),...s}));u.displayName="CardFooter"},94880:(e,s,t)=>{"use strict";t.d(s,{E:()=>N});var r=t(10326),a=t(17577),n=t(93095),i=t(45226),l="Progress",[o,d]=(0,n.b)(l),[c,u]=o(l),m=a.forwardRef((e,s)=>{var t,a;let{__scopeProgress:n,value:l=null,max:o,getValueLabel:d=h,...u}=e;(o||0===o)&&!v(o)&&console.error((t=`${o}`,`Invalid prop \`max\` of value \`${t}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=v(o)?o:100;null===l||j(l,m)||console.error((a=`${l}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let p=j(l,m)?l:null,x=g(p)?d(p,m):void 0;return(0,r.jsx)(c,{scope:n,value:p,max:m,children:(0,r.jsx)(i.WV.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":g(p)?p:void 0,"aria-valuetext":x,role:"progressbar","data-state":f(p,m),"data-value":p??void 0,"data-max":m,...u,ref:s})})});m.displayName=l;var p="ProgressIndicator",x=a.forwardRef((e,s)=>{let{__scopeProgress:t,...a}=e,n=u(p,t);return(0,r.jsx)(i.WV.div,{"data-state":f(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...a,ref:s})});function h(e,s){return`${Math.round(e/s*100)}%`}function f(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function g(e){return"number"==typeof e}function v(e){return g(e)&&!isNaN(e)&&e>0}function j(e,s){return g(e)&&!isNaN(e)&&e<=s&&e>=0}x.displayName=p;var y=t(51223);let N=a.forwardRef(({className:e,value:s,...t},a)=>r.jsx(m,{ref:a,className:(0,y.cn)("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",e),...t,children:r.jsx(x,{className:"h-full w-full flex-1 bg-primary transition-all bg-white",style:{transform:`translateX(-${100-(s||0)}%)`}})}));N.displayName=m.displayName},1929:(e,s,t)=>{"use strict";t.d(s,{h:()=>a});var r=t(69701);async function a(){try{let e=(0,r.s3)(),{error:s}=await e.auth.signOut();if(s)return console.error("Error signing out:",s),!1;return window.location.href="/login",!0}catch(e){return console.error("Unexpected error during logout:",e),!1}}},69701:(e,s,t)=>{"use strict";t.d(s,{eI:()=>l,s3:()=>d});var r=t(65815);let a=null,n="https://eqghwtejdnzgopmcjlho.supabase.co",i=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!n)throw Error("Missing Supabase environment variables");let l=()=>{throw Error("createClient should only be called on the client side")},o=()=>{if(!a){if(!i)throw Error("SUPABASE_SERVICE_ROLE_KEY is required for admin operations");a=(0,r.eI)(n,i,{auth:{autoRefreshToken:!1,persistSession:!1}})}return a},d=()=>o()},51223:(e,s,t)=>{"use strict";t.d(s,{cn:()=>n});var r=t(41135),a=t(31009);function n(...e){return(0,a.m6)((0,r.W)(e))}},46835:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/app/couple/dashboard/page.tsx#default`)},61158:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o,metadata:()=>l});var r=t(19510),a=t(45317),n=t.n(a);let i=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/components/tempo-init.tsx#TempoInit`);t(5023);let l={title:"Tempo - Modern SaaS Starter",description:"A modern full-stack starter template powered by Next.js"};function o({children:e}){return r.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:(0,r.jsxs)("body",{className:n().className,children:[e,r.jsx(i,{})]})})}},73881:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(66621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{},93095:(e,s,t)=>{"use strict";t.d(s,{b:()=>n});var r=t(17577),a=t(10326);function n(e,s=[]){let t=[],n=()=>{let s=t.map(e=>r.createContext(e));return function(t){let a=t?.[e]||s;return r.useMemo(()=>({[`__scope${e}`]:{...t,[e]:a}}),[t,a])}};return n.scopeName=e,[function(s,n){let i=r.createContext(n),l=t.length;t=[...t,n];let o=s=>{let{scope:t,children:n,...o}=s,d=t?.[e]?.[l]||i,c=r.useMemo(()=>o,Object.values(o));return(0,a.jsx)(d.Provider,{value:c,children:n})};return o.displayName=s+"Provider",[o,function(t,a){let o=a?.[e]?.[l]||i,d=r.useContext(o);if(d)return d;if(void 0!==n)return n;throw Error(`\`${t}\` must be used within \`${s}\``)}]},function(...e){let s=e[0];if(1===e.length)return s;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=t.reduce((s,{useScope:t,scopeName:r})=>{let a=t(e)[`__scope${r}`];return{...s,...a}},{});return r.useMemo(()=>({[`__scope${s.scopeName}`]:a}),[a])}};return t.scopeName=s.scopeName,t}(n,...s)]}},45226:(e,s,t)=>{"use strict";t.d(s,{WV:()=>l,jH:()=>o});var r=t(17577),a=t(60962),n=t(34214),i=t(10326),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,s)=>{let t=r.forwardRef((e,t)=>{let{asChild:r,...a}=e,l=r?n.g7:s;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...a,ref:t})});return t.displayName=`Primitive.${s}`,{...e,[s]:t}},{});function o(e,s){e&&a.flushSync(()=>e.dispatchEvent(s))}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[948,837,753,815,705],()=>t(27522));module.exports=r})();