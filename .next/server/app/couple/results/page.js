(()=>{var e={};e.id=734,e.ids=[734],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},55315:e=>{"use strict";e.exports=require("path")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},57157:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>o,routeModule:()=>p,tree:()=>d}),t(12091),t(61158),t(35866);var r=t(23191),a=t(88716),i=t(37922),n=t.n(i),l=t(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d=["",{children:["couple",{children:["results",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,12091)),"/Users/<USER>/Nextjs/marriage-map/src/app/couple/results/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,61158)),"/Users/<USER>/Nextjs/marriage-map/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/Users/<USER>/Nextjs/marriage-map/src/app/couple/results/page.tsx"],u="/couple/results/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/couple/results/page",pathname:"/couple/results",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},32889:(e,s,t)=>{Promise.resolve().then(t.bind(t,92702))},41291:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},30361:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},67427:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},77506:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},39730:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},24061:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(62881).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},35047:(e,s,t)=>{"use strict";var r=t(77389);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},92702:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>R});var r=t(10326),a=t(17577),i=t(91664),n=t(77506),l=t(86333),c=t(62881);let d=(0,c.Z)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var o=t(33261);t(69701);var u=t(35047),m=t(29752),p=t(38443),x=t(94880),h=t(45226),j="horizontal",g=["horizontal","vertical"],v=a.forwardRef((e,s)=>{let{decorative:t,orientation:a=j,...i}=e,n=g.includes(a)?a:j;return(0,r.jsx)(h.WV.div,{"data-orientation":n,...t?{role:"none"}:{"aria-orientation":"vertical"===n?n:void 0,role:"separator"},...i,ref:s})});v.displayName="Separator";var f=t(51223);let N=a.forwardRef(({className:e,orientation:s="horizontal",decorative:t=!0,...a},i)=>r.jsx(v,{ref:i,decorative:t,orientation:s,className:(0,f.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...a}));N.displayName=v.displayName;var y=t(30361);let b=(0,c.Z)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var k=t(41291),w=t(39730),A=t(24061),P=t(67427);let Z=(0,c.Z)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),S=({analysisReport:e,showCounselorNotes:s=!1})=>{let{overallCompatibility:t,compatibilityLevel:a,domainAnalyses:i,strengthAreas:n,challengeAreas:l,priorityRecommendations:c,counselorNotes:d}=e,u=e=>e>=80?"text-green-600":e>=60?"text-yellow-600":"text-red-600",h=e=>{switch(e){case"aligned":return r.jsx(y.Z,{className:"h-5 w-5 text-green-600"});case"moderate":return r.jsx(b,{className:"h-5 w-5 text-yellow-600"});case"conflict":return r.jsx(k.Z,{className:"h-5 w-5 text-red-600"})}},j=e=>({"visi-hidup":"\uD83D\uDD2D",keuangan:"\uD83D\uDCB0",pengasuhan:"\uD83D\uDC76",komunikasi:r.jsx(w.Z,{className:"h-5 w-5"}),"fungsi-dan-peran":r.jsx(A.Z,{className:"h-5 w-5"}),seks:r.jsx(P.Z,{className:"h-5 w-5"}),spiritualitas:"✝️","sisi-gelap":"\uD83C\uDF11"})[e]||"\uD83D\uDCCB";return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(m.Zb,{children:[r.jsx(m.Ol,{children:(0,r.jsxs)(m.ll,{className:"flex items-center gap-2",children:[r.jsx(Z,{className:"h-5 w-5"}),"Kompatibilitas Keseluruhan"]})}),r.jsx(m.aY,{children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsxs)("div",{className:`text-4xl font-bold ${u(t)}`,children:[t,"%"]}),r.jsx(p.C,{variant:t>=70?"default":"destructive",className:"text-lg px-4 py-2",children:a}),r.jsx(x.E,{value:t,className:"h-3"})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)(m.Zb,{children:[r.jsx(m.Ol,{children:r.jsx(m.ll,{className:"text-green-700",children:"Area Kekuatan"})}),r.jsx(m.aY,{children:n.length>0?r.jsx("div",{className:"space-y-2",children:n.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(y.Z,{className:"h-4 w-4 text-green-600"}),r.jsx("span",{children:e})]},s))}):r.jsx("p",{className:"text-muted-foreground",children:"Tidak ada area kekuatan yang teridentifikasi"})})]}),(0,r.jsxs)(m.Zb,{children:[r.jsx(m.Ol,{children:r.jsx(m.ll,{className:"text-red-700",children:"Area Tantangan"})}),r.jsx(m.aY,{children:l.length>0?r.jsx("div",{className:"space-y-2",children:l.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(k.Z,{className:"h-4 w-4 text-red-600"}),r.jsx("span",{children:e})]},s))}):r.jsx("p",{className:"text-muted-foreground",children:"Tidak ada area tantangan yang teridentifikasi"})})]})]}),(0,r.jsxs)(m.Zb,{children:[r.jsx(m.Ol,{children:r.jsx(m.ll,{children:"Analisis per Domain"})}),r.jsx(m.aY,{children:r.jsx("div",{className:"space-y-6",children:i.map((e,s)=>(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[j(e.domain),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-semibold",children:e.title}),r.jsx("p",{className:"text-sm text-muted-foreground",children:e.description})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[h(e.status),(0,r.jsxs)("span",{className:`font-semibold ${u(e.compatibilityScore)}`,children:[e.compatibilityScore,"%"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[r.jsx("span",{className:"text-muted-foreground",children:"Partner 1: "}),(0,r.jsxs)("span",{className:"font-medium",children:[e.partner1Score,"%"]})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"text-muted-foreground",children:"Partner 2: "}),(0,r.jsxs)("span",{className:"font-medium",children:[e.partner2Score,"%"]})]})]}),r.jsx(x.E,{value:e.compatibilityScore,className:"h-2"}),e.insights.length>0&&(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[r.jsx("h5",{className:"font-medium text-blue-900 mb-2",children:"Insights:"}),r.jsx("ul",{className:"text-sm text-blue-800 space-y-1",children:e.insights.map((e,s)=>(0,r.jsxs)("li",{children:["• ",e]},s))})]}),e.recommendations.length>0&&(0,r.jsxs)("div",{className:"bg-yellow-50 p-3 rounded-lg",children:[r.jsx("h5",{className:"font-medium text-yellow-900 mb-2",children:"Rekomendasi:"}),r.jsx("ul",{className:"text-sm text-yellow-800 space-y-1",children:e.recommendations.map((e,s)=>(0,r.jsxs)("li",{children:["• ",e]},s))})]}),s<i.length-1&&r.jsx(N,{})]},s))})})]}),(0,r.jsxs)(m.Zb,{children:[r.jsx(m.Ol,{children:r.jsx(m.ll,{className:"text-orange-700",children:"Rekomendasi Prioritas"})}),r.jsx(m.aY,{children:r.jsx("div",{className:"space-y-3",children:c.map((e,s)=>(0,r.jsxs)(o.bZ,{className:"border-orange-200 bg-orange-50",children:[r.jsx(k.Z,{className:"h-4 w-4 text-orange-600"}),r.jsx(o.X,{className:"text-orange-800",children:e})]},s))})})]}),s&&d.length>0&&(0,r.jsxs)(m.Zb,{className:"border-purple-200",children:[r.jsx(m.Ol,{children:r.jsx(m.ll,{className:"text-purple-700",children:"Catatan untuk Konselor"})}),r.jsx(m.aY,{children:r.jsx("div",{className:"space-y-2",children:d.map((e,s)=>r.jsx("div",{className:"bg-purple-50 p-3 rounded-lg",children:r.jsx("p",{className:"text-purple-800 text-sm",children:e})},s))})})]})]})};function R(){let e=(0,u.useRouter)(),[s,t]=(0,a.useState)(!0),[c,m]=(0,a.useState)(null),[p,x]=(0,a.useState)(null),[h,j]=(0,a.useState)(null);return s?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx(n.Z,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),r.jsx("p",{children:"Menganalisis kompatibilitas..."})]})}):c?(0,r.jsxs)("div",{className:"container py-8",children:[r.jsx(o.bZ,{variant:"destructive",children:r.jsx(o.X,{children:c})}),r.jsx("div",{className:"mt-4",children:(0,r.jsxs)(i.z,{onClick:()=>e.push("/couple/dashboard"),variant:"outline",children:[r.jsx(l.Z,{className:"mr-2 h-4 w-4"}),"Kembali ke Dashboard"]})})]}):(0,r.jsxs)("div",{className:"container py-8",children:[r.jsx("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold",children:"Hasil Kompatibilitas Pernikahan"}),h&&(0,r.jsxs)("p",{className:"text-muted-foreground mt-2",children:[h.partner1Name," & ",h.partner2Name]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(i.z,{onClick:()=>{if(!p||!h)return;let e=new Blob([`
LAPORAN KOMPATIBILITAS PERNIKAHAN
=================================

Partner 1: ${h.partner1Name}
Partner 2: ${h.partner2Name}
Tanggal: ${new Date().toLocaleDateString("id-ID")}

KOMPATIBILITAS KESELURUHAN: ${p.overallCompatibility}% (${p.compatibilityLevel})

AREA KEKUATAN:
${p.strengthAreas.map(e=>`- ${e}`).join("\n")}

AREA TANTANGAN:
${p.challengeAreas.map(e=>`- ${e}`).join("\n")}

REKOMENDASI PRIORITAS:
${p.priorityRecommendations.map((e,s)=>`${s+1}. ${e}`).join("\n")}

ANALISIS DETAIL PER DOMAIN:
${p.domainAnalyses.map(e=>`
${e.title}: ${e.compatibilityScore}%
- Partner 1: ${e.partner1Score}%
- Partner 2: ${e.partner2Score}%
- Status: ${e.status}
- Insights: ${e.insights.join("; ")}
- Rekomendasi: ${e.recommendations.join("; ")}
`).join("\n")}
    `],{type:"text/plain"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download=`laporan-kompatibilitas-${new Date().toISOString().split("T")[0]}.txt`,document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(s)},variant:"outline",children:[r.jsx(d,{className:"mr-2 h-4 w-4"}),"Download Laporan"]}),(0,r.jsxs)(i.z,{onClick:()=>e.push("/couple/dashboard"),variant:"outline",children:[r.jsx(l.Z,{className:"mr-2 h-4 w-4"}),"Kembali"]})]})]})}),p&&r.jsx(S,{analysisReport:p,showCounselorNotes:!1})]})}t(69498)},33261:(e,s,t)=>{"use strict";t.d(s,{X:()=>d,bZ:()=>c});var r=t(10326),a=t(17577),i=t(79360),n=t(51223);let l=(0,i.j)("relative w-full rounded-lg border px-4 py-3 text-sm [&:has(svg)]:pl-11 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=a.forwardRef(({className:e,variant:s,...t},a)=>r.jsx("div",{ref:a,role:"alert",className:(0,n.cn)(l({variant:s}),e),...t}));c.displayName="Alert",a.forwardRef(({className:e,...s},t)=>r.jsx("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...s}));d.displayName="AlertDescription"},38443:(e,s,t)=>{"use strict";t.d(s,{C:()=>l});var r=t(10326);t(17577);var a=t(79360),i=t(51223);let n=(0,a.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,...t}){return r.jsx("div",{className:(0,i.cn)(n({variant:s}),e),...t})}},12091:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`/Users/<USER>/Nextjs/marriage-map/src/app/couple/results/page.tsx#default`)},93095:(e,s,t)=>{"use strict";t.d(s,{b:()=>i});var r=t(17577),a=t(10326);function i(e,s=[]){let t=[],i=()=>{let s=t.map(e=>r.createContext(e));return function(t){let a=t?.[e]||s;return r.useMemo(()=>({[`__scope${e}`]:{...t,[e]:a}}),[t,a])}};return i.scopeName=e,[function(s,i){let n=r.createContext(i),l=t.length;t=[...t,i];let c=s=>{let{scope:t,children:i,...c}=s,d=t?.[e]?.[l]||n,o=r.useMemo(()=>c,Object.values(c));return(0,a.jsx)(d.Provider,{value:o,children:i})};return c.displayName=s+"Provider",[c,function(t,a){let c=a?.[e]?.[l]||n,d=r.useContext(c);if(d)return d;if(void 0!==i)return i;throw Error(`\`${t}\` must be used within \`${s}\``)}]},function(...e){let s=e[0];if(1===e.length)return s;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=t.reduce((s,{useScope:t,scopeName:r})=>{let a=t(e)[`__scope${r}`];return{...s,...a}},{});return r.useMemo(()=>({[`__scope${s.scopeName}`]:a}),[a])}};return t.scopeName=s.scopeName,t}(i,...s)]}},45226:(e,s,t)=>{"use strict";t.d(s,{WV:()=>l,jH:()=>c});var r=t(17577),a=t(60962),i=t(34214),n=t(10326),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,s)=>{let t=r.forwardRef((e,t)=>{let{asChild:r,...a}=e,l=r?i.g7:s;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(l,{...a,ref:t})});return t.displayName=`Primitive.${s}`,{...e,[s]:t}},{});function c(e,s){e&&a.flushSync(()=>e.dispatchEvent(s))}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[948,837,753,815,119],()=>t(57157));module.exports=r})();