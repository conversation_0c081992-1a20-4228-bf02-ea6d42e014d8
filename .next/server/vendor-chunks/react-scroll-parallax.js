"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-scroll-parallax";
exports.ids = ["vendor-chunks/react-scroll-parallax"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-scroll-parallax/dist/react-scroll-parallax.esm.js":
/*!******************************************************************************!*\
  !*** ./node_modules/react-scroll-parallax/dist/react-scroll-parallax.esm.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EasingPreset: () => (/* reexport safe */ parallax_controller__WEBPACK_IMPORTED_MODULE_0__.EasingPreset),\n/* harmony export */   Parallax: () => (/* binding */ Parallax),\n/* harmony export */   ParallaxBanner: () => (/* binding */ ParallaxBanner),\n/* harmony export */   ParallaxBannerLayer: () => (/* binding */ ParallaxBannerLayer),\n/* harmony export */   ParallaxContext: () => (/* binding */ ParallaxContext),\n/* harmony export */   ParallaxProvider: () => (/* binding */ ParallaxProvider),\n/* harmony export */   useParallax: () => (/* binding */ useParallax),\n/* harmony export */   useParallaxController: () => (/* binding */ useParallaxController)\n/* harmony export */ });\n/* harmony import */ var parallax_controller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! parallax-controller */ \"(ssr)/./node_modules/parallax-controller/dist/parallax-controller.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction removeUndefinedObjectKeys(obj) {\n  Object.keys(obj).forEach(function (key) {\n    return obj[key] === undefined && delete obj[key];\n  });\n  return obj;\n}\n\nvar _excluded = [\"disabled\", \"easing\", \"endScroll\", \"onChange\", \"onEnter\", \"onExit\", \"onProgressChange\", \"opacity\", \"rootMargin\", \"rotate\", \"rotateX\", \"rotateY\", \"rotateZ\", \"scale\", \"scaleX\", \"scaleY\", \"scaleZ\", \"shouldAlwaysCompleteAnimation\", \"shouldDisableScalingTranslations\", \"speed\", \"startScroll\", \"targetElement\", \"translateX\", \"translateY\"];\nfunction getIsolatedParallaxProps(props) {\n  var disabled = props.disabled,\n      easing = props.easing,\n      endScroll = props.endScroll,\n      onChange = props.onChange,\n      onEnter = props.onEnter,\n      onExit = props.onExit,\n      onProgressChange = props.onProgressChange,\n      opacity = props.opacity,\n      rootMargin = props.rootMargin,\n      rotate = props.rotate,\n      rotateX = props.rotateX,\n      rotateY = props.rotateY,\n      rotateZ = props.rotateZ,\n      scale = props.scale,\n      scaleX = props.scaleX,\n      scaleY = props.scaleY,\n      scaleZ = props.scaleZ,\n      shouldAlwaysCompleteAnimation = props.shouldAlwaysCompleteAnimation,\n      shouldDisableScalingTranslations = props.shouldDisableScalingTranslations,\n      speed = props.speed,\n      startScroll = props.startScroll,\n      targetElement = props.targetElement,\n      translateX = props.translateX,\n      translateY = props.translateY,\n      rest = _objectWithoutPropertiesLoose(props, _excluded);\n\n  var parallaxProps = removeUndefinedObjectKeys({\n    disabled: disabled,\n    easing: easing,\n    endScroll: endScroll,\n    onChange: onChange,\n    onEnter: onEnter,\n    onExit: onExit,\n    onProgressChange: onProgressChange,\n    opacity: opacity,\n    rootMargin: rootMargin,\n    rotate: rotate,\n    rotateX: rotateX,\n    rotateY: rotateY,\n    rotateZ: rotateZ,\n    scale: scale,\n    scaleX: scaleX,\n    scaleY: scaleY,\n    scaleZ: scaleZ,\n    shouldAlwaysCompleteAnimation: shouldAlwaysCompleteAnimation,\n    shouldDisableScalingTranslations: shouldDisableScalingTranslations,\n    speed: speed,\n    startScroll: startScroll,\n    targetElement: targetElement,\n    translateX: translateX,\n    translateY: translateY\n  });\n  return {\n    parallaxProps: parallaxProps,\n    rest: rest\n  };\n}\n\nfunction useVerifyController(controller) {\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var isServer = typeof window === 'undefined'; // Make sure the provided controller is an instance of the Parallax Controller\n\n    var isInstance = controller instanceof parallax_controller__WEBPACK_IMPORTED_MODULE_0__.ParallaxController; // Throw if neither context or global is available\n\n    if (!isServer && !controller && !isInstance) {\n      throw new Error(\"Must wrap your application's <Parallax /> components in a <ParallaxProvider />.\");\n    }\n  }, [controller]);\n}\n\nvar ParallaxContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createContext(null);\n\nfunction useParallaxController() {\n  var parallaxController = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ParallaxContext);\n  var isServer = typeof window === 'undefined';\n\n  if (isServer) {\n    return null;\n  }\n\n  if (!parallaxController) {\n    throw new Error('Could not find `react-scroll-parallax` context value. Please ensure the component is wrapped in a <ParallaxProvider>');\n  }\n\n  return parallaxController;\n}\n\nfunction useParallax(props) {\n  var controller = useParallaxController();\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n\n  var _getIsolatedParallaxP = getIsolatedParallaxProps(props),\n      parallaxProps = _getIsolatedParallaxP.parallaxProps;\n\n  useVerifyController(controller);\n\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(),\n      element = _useState[0],\n      setElement = _useState[1]; // create element\n\n\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var newElement;\n\n    if (ref.current instanceof HTMLElement) {\n      var options = {\n        el: ref.current,\n        props: parallaxProps\n      };\n      newElement = controller == null ? void 0 : controller.createElement(options);\n      setElement(newElement);\n    } else {\n      throw new Error('You must assign the ref returned by the useParallax() hook to an HTML Element.');\n    }\n\n    return function () {\n      if (newElement) {\n        controller == null ? void 0 : controller.removeElementById(newElement.id);\n      }\n    };\n  }, []); // update element\n\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (element) {\n      if (props.disabled) {\n        controller == null ? void 0 : controller.resetElementStyles(element);\n        controller == null ? void 0 : controller.updateElementPropsById(element.id, parallaxProps);\n      } else {\n        controller == null ? void 0 : controller.updateElementPropsById(element.id, parallaxProps);\n      }\n    }\n  }, [props.disabled, props.easing, props.endScroll, props.onChange, props.onEnter, props.onExit, props.onProgressChange, props.opacity, props.rootMargin, props.rotate, props.rotateX, props.rotateY, props.rotateZ, props.scale, props.scaleX, props.scaleY, props.scaleZ, props.shouldAlwaysCompleteAnimation, props.shouldDisableScalingTranslations, props.speed, props.startScroll, props.targetElement, props.translateX, props.translateY]);\n  return {\n    ref: ref,\n    controller: controller,\n    element: element\n  };\n}\n\nfunction Parallax(props) {\n  var _getIsolatedParallaxP = getIsolatedParallaxProps(props),\n      parallaxProps = _getIsolatedParallaxP.parallaxProps,\n      rest = _getIsolatedParallaxP.rest;\n\n  var _useParallax = useParallax(parallaxProps),\n      ref = _useParallax.ref;\n\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", Object.assign({\n    ref: ref\n  }, rest), props.children);\n}\n\nvar FALLBACK_RECT = {\n  height: 0\n};\nfunction getExpandedStyle(layer) {\n  if (Array.isArray(layer.translateY)) {\n    var translateYStart = (0,parallax_controller__WEBPACK_IMPORTED_MODULE_0__.parseValueAndUnit)(layer.translateY[0]);\n    var translateYEnd = (0,parallax_controller__WEBPACK_IMPORTED_MODULE_0__.parseValueAndUnit)(layer.translateY[1]);\n\n    if (translateYStart.unit === 'px' && translateYEnd.unit === 'px') {\n      return {\n        top: Math.abs(translateYEnd.value) * -1 + \"px\",\n        bottom: Math.abs(translateYStart.value) * -1 + \"px\"\n      };\n    }\n\n    if (translateYStart.unit === '%' && translateYEnd.unit === '%') {\n      var _layer$targetElement$, _layer$targetElement;\n\n      var clientRect = (_layer$targetElement$ = (_layer$targetElement = layer.targetElement) == null ? void 0 : _layer$targetElement.getBoundingClientRect()) != null ? _layer$targetElement$ : FALLBACK_RECT;\n      var top = Math.abs(clientRect.height * 0.01 * translateYEnd.value) * -1;\n      var bottom = Math.abs(clientRect.height * 0.01 * translateYStart.value) * -1;\n      return {\n        top: top + \"px\",\n        bottom: bottom + \"px\"\n      };\n    }\n  }\n\n  if (layer.speed) {\n    var speed = layer.speed || 0;\n    var absSpeed = Math.abs(speed) * 10 * -1;\n    return {\n      top: absSpeed + \"px\",\n      bottom: absSpeed + \"px\"\n    };\n  }\n\n  return {};\n}\n\nfunction getImageStyle(layer) {\n  return layer.image ? {\n    backgroundImage: \"url(\" + layer.image + \")\",\n    backgroundPosition: 'center',\n    backgroundSize: 'cover'\n  } : {};\n}\n\nvar _excluded$1 = [\"children\", \"disabled\", \"style\", \"expanded\", \"image\", \"testId\"];\nvar absoluteStyle = {\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  right: 0,\n  bottom: 0\n};\nvar ParallaxBannerLayer = function ParallaxBannerLayer(props) {\n  var _getIsolatedParallaxP = getIsolatedParallaxProps(props),\n      parallaxProps = _getIsolatedParallaxP.parallaxProps,\n      rest = _getIsolatedParallaxP.rest;\n\n  var style = rest.style,\n      _rest$expanded = rest.expanded,\n      expanded = _rest$expanded === void 0 ? true : _rest$expanded,\n      testId = rest.testId,\n      divProps = _objectWithoutPropertiesLoose(rest, _excluded$1);\n\n  var imageStyle = getImageStyle(props);\n  var expandedStyle = expanded ? getExpandedStyle(props) : {};\n  var parallax = useParallax(_extends({\n    targetElement: props.targetElement,\n    shouldDisableScalingTranslations: true\n  }, parallaxProps));\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", Object.assign({\n    \"data-testid\": testId,\n    ref: parallax.ref,\n    style: _extends({}, imageStyle, absoluteStyle, expandedStyle, style)\n  }, divProps), rest.children);\n};\n\nvar _excluded$2 = [\"disabled\", \"style\", \"layers\"];\nvar containerStyle = {\n  position: 'relative',\n  overflow: 'hidden',\n  width: '100%'\n};\nvar ParallaxBanner = function ParallaxBanner(props) {\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null),\n      targetElement = _useState[0],\n      setTargetElement = _useState[1];\n\n  var containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    setTargetElement(containerRef.current);\n  }, []);\n\n  var rootStyle = props.style,\n      _props$layers = props.layers,\n      layers = _props$layers === void 0 ? [] : _props$layers,\n      rootRest = _objectWithoutPropertiesLoose(props, _excluded$2);\n\n  function renderLayers() {\n    if (targetElement) {\n      var shouldUseLayers = layers && layers.length > 0;\n\n      if (shouldUseLayers) {\n        return layers.map(function (layer, i) {\n          return react__WEBPACK_IMPORTED_MODULE_1___default().createElement(ParallaxBannerLayer, Object.assign({}, layer, {\n            targetElement: targetElement,\n            key: \"layer-\" + i,\n            testId: \"layer-\" + i\n          }));\n        });\n      }\n    }\n\n    return null;\n  }\n\n  function renderChildren() {\n    if (targetElement) {\n      return react__WEBPACK_IMPORTED_MODULE_1___default().Children.map(props.children, function (child) {\n        var item = child; // adds the targetElement prop to any ParallaxBannerLayer components\n\n        if ((item == null ? void 0 : item.type) === ParallaxBannerLayer) {\n          var clone = react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(item, {\n            targetElement: targetElement\n          });\n          return clone;\n        }\n\n        return child;\n      });\n    }\n\n    return null;\n  }\n\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", Object.assign({\n    ref: containerRef,\n    style: _extends({}, containerStyle, rootStyle)\n  }, rootRest), renderLayers(), renderChildren());\n};\n\nvar createController = function createController(options) {\n  // Don't initialize on the server\n  var isServer = typeof window === 'undefined';\n\n  if (!isServer) {\n    // Must not be the server so kick it off...\n    return parallax_controller__WEBPACK_IMPORTED_MODULE_0__.ParallaxController.init(options);\n  }\n\n  return null;\n};\n\nfunction ParallaxProvider(props) {\n  var controller = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n\n  if (!controller.current) {\n    controller.current = createController({\n      scrollAxis: props.scrollAxis || parallax_controller__WEBPACK_IMPORTED_MODULE_0__.ScrollAxis.vertical,\n      scrollContainer: props.scrollContainer,\n      disabled: props.isDisabled\n    });\n  } // update scroll container\n\n\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (props.scrollContainer && controller.current) {\n      controller.current.updateScrollContainer(props.scrollContainer);\n    }\n  }, [props.scrollContainer, controller.current]); // disable/enable parallax\n\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (props.isDisabled && controller.current) {\n      controller.current.disableParallaxController();\n    }\n\n    if (!props.isDisabled && controller.current) {\n      controller.current.enableParallaxController();\n    }\n  }, [props.isDisabled, controller.current]); // remove the controller when unmounting\n\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    return function () {\n      (controller == null ? void 0 : controller.current) && (controller == null ? void 0 : controller.current.destroy());\n    };\n  }, []);\n  return react__WEBPACK_IMPORTED_MODULE_1___default().createElement(ParallaxContext.Provider, {\n    value: controller.current\n  }, props.children);\n}\n\n\n//# sourceMappingURL=react-scroll-parallax.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-scroll-parallax/dist/react-scroll-parallax.esm.js\n");

/***/ })

};
;