"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parallax-controller";
exports.ids = ["vendor-chunks/parallax-controller"];
exports.modules = {

/***/ "(ssr)/./node_modules/parallax-controller/dist/parallax-controller.esm.js":
/*!**************************************************************************!*\
  !*** ./node_modules/parallax-controller/dist/parallax-controller.esm.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EasingPreset: () => (/* binding */ EasingPreset),\n/* harmony export */   Element: () => (/* binding */ Element),\n/* harmony export */   Limits: () => (/* binding */ Limits),\n/* harmony export */   ParallaxController: () => (/* binding */ ParallaxController),\n/* harmony export */   Rect: () => (/* binding */ Rect),\n/* harmony export */   RotationUnits: () => (/* binding */ RotationUnits),\n/* harmony export */   ScaleUnits: () => (/* binding */ ScaleUnits),\n/* harmony export */   Scroll: () => (/* binding */ Scroll),\n/* harmony export */   ScrollAxis: () => (/* binding */ ScrollAxis),\n/* harmony export */   Units: () => (/* binding */ Units),\n/* harmony export */   ValidCSSEffects: () => (/* binding */ ValidCSSEffects),\n/* harmony export */   View: () => (/* binding */ View),\n/* harmony export */   createId: () => (/* binding */ createId),\n/* harmony export */   getProgressAmount: () => (/* binding */ getProgressAmount),\n/* harmony export */   isElementInView: () => (/* binding */ isElementInView),\n/* harmony export */   parseElementTransitionEffects: () => (/* binding */ parseElementTransitionEffects),\n/* harmony export */   parseValueAndUnit: () => (/* binding */ parseValueAndUnit),\n/* harmony export */   resetStyles: () => (/* binding */ resetStyles),\n/* harmony export */   scaleBetween: () => (/* binding */ scaleBetween),\n/* harmony export */   scaleEffectByProgress: () => (/* binding */ scaleEffectByProgress),\n/* harmony export */   setElementStyles: () => (/* binding */ setElementStyles),\n/* harmony export */   testForPassiveScroll: () => (/* binding */ testForPassiveScroll)\n/* harmony export */ });\n/* harmony import */ var bezier_easing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bezier-easing */ \"(ssr)/./node_modules/bezier-easing/src/index.js\");\n/* harmony import */ var bezier_easing__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bezier_easing__WEBPACK_IMPORTED_MODULE_0__);\n\n\nvar Limits = function Limits(properties) {\n  this.startX = properties.startX;\n  this.startY = properties.startY;\n  this.endX = properties.endX;\n  this.endY = properties.endY; // Used to calculate the progress of the element\n\n  this.totalX = this.endX - this.startX;\n  this.totalY = this.endY - this.startY; // Used to scale translate effects\n\n  this.startMultiplierX = properties.startMultiplierX || 1;\n  this.endMultiplierX = properties.endMultiplierX || 1;\n  this.startMultiplierY = properties.startMultiplierY || 1;\n  this.endMultiplierY = properties.endMultiplierY || 1;\n};\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nvar ValidCSSEffects;\n\n(function (ValidCSSEffects) {\n  ValidCSSEffects[\"speed\"] = \"speed\";\n  ValidCSSEffects[\"translateX\"] = \"translateX\";\n  ValidCSSEffects[\"translateY\"] = \"translateY\";\n  ValidCSSEffects[\"rotate\"] = \"rotate\";\n  ValidCSSEffects[\"rotateX\"] = \"rotateX\";\n  ValidCSSEffects[\"rotateY\"] = \"rotateY\";\n  ValidCSSEffects[\"rotateZ\"] = \"rotateZ\";\n  ValidCSSEffects[\"scale\"] = \"scale\";\n  ValidCSSEffects[\"scaleX\"] = \"scaleX\";\n  ValidCSSEffects[\"scaleY\"] = \"scaleY\";\n  ValidCSSEffects[\"scaleZ\"] = \"scaleZ\";\n  ValidCSSEffects[\"opacity\"] = \"opacity\";\n})(ValidCSSEffects || (ValidCSSEffects = {}));\n\nvar Units;\n\n(function (Units) {\n  Units[\"px\"] = \"px\";\n  Units[\"%\"] = \"%\";\n  Units[\"vh\"] = \"vh\";\n  Units[\"vw\"] = \"vw\";\n})(Units || (Units = {}));\n\nvar RotationUnits;\n\n(function (RotationUnits) {\n  RotationUnits[\"deg\"] = \"deg\";\n  RotationUnits[\"turn\"] = \"turn\";\n  RotationUnits[\"rad\"] = \"rad\";\n})(RotationUnits || (RotationUnits = {}));\n\nvar ScaleUnits;\n\n(function (ScaleUnits) {\n  ScaleUnits[\"\"] = \"\";\n})(ScaleUnits || (ScaleUnits = {}));\n\nvar ScrollAxis;\n\n(function (ScrollAxis) {\n  ScrollAxis[\"vertical\"] = \"vertical\";\n  ScrollAxis[\"horizontal\"] = \"horizontal\";\n})(ScrollAxis || (ScrollAxis = {}));\n\nvar EasingPreset;\n\n(function (EasingPreset) {\n  EasingPreset[\"ease\"] = \"ease\";\n  EasingPreset[\"easeIn\"] = \"easeIn\";\n  EasingPreset[\"easeOut\"] = \"easeOut\";\n  EasingPreset[\"easeInOut\"] = \"easeInOut\";\n  EasingPreset[\"easeInQuad\"] = \"easeInQuad\";\n  EasingPreset[\"easeInCubic\"] = \"easeInCubic\";\n  EasingPreset[\"easeInQuart\"] = \"easeInQuart\";\n  EasingPreset[\"easeInQuint\"] = \"easeInQuint\";\n  EasingPreset[\"easeInSine\"] = \"easeInSine\";\n  EasingPreset[\"easeInExpo\"] = \"easeInExpo\";\n  EasingPreset[\"easeInCirc\"] = \"easeInCirc\";\n  EasingPreset[\"easeOutQuad\"] = \"easeOutQuad\";\n  EasingPreset[\"easeOutCubic\"] = \"easeOutCubic\";\n  EasingPreset[\"easeOutQuart\"] = \"easeOutQuart\";\n  EasingPreset[\"easeOutQuint\"] = \"easeOutQuint\";\n  EasingPreset[\"easeOutSine\"] = \"easeOutSine\";\n  EasingPreset[\"easeOutExpo\"] = \"easeOutExpo\";\n  EasingPreset[\"easeOutCirc\"] = \"easeOutCirc\";\n  EasingPreset[\"easeInOutQuad\"] = \"easeInOutQuad\";\n  EasingPreset[\"easeInOutCubic\"] = \"easeInOutCubic\";\n  EasingPreset[\"easeInOutQuart\"] = \"easeInOutQuart\";\n  EasingPreset[\"easeInOutQuint\"] = \"easeInOutQuint\";\n  EasingPreset[\"easeInOutSine\"] = \"easeInOutSine\";\n  EasingPreset[\"easeInOutExpo\"] = \"easeInOutExpo\";\n  EasingPreset[\"easeInOutCirc\"] = \"easeInOutCirc\";\n  EasingPreset[\"easeInBack\"] = \"easeInBack\";\n  EasingPreset[\"easeOutBack\"] = \"easeOutBack\";\n  EasingPreset[\"easeInOutBack\"] = \"easeInOutBack\";\n})(EasingPreset || (EasingPreset = {}));\n\n/**\r\n * Creates a unique id to distinguish parallax elements.\r\n */\nvar id = 0;\nfunction createId() {\n  ++id;\n  return id;\n}\n\nvar Rect = /*#__PURE__*/function () {\n  function Rect(options) {\n    var rect = options.el.getBoundingClientRect(); // rect is based on viewport -- must adjust for relative scroll container\n\n    if (options.view.scrollContainer) {\n      var scrollRect = options.view.scrollContainer.getBoundingClientRect();\n      rect = _extends({}, rect, {\n        top: rect.top - scrollRect.top,\n        right: rect.right - scrollRect.left,\n        bottom: rect.bottom - scrollRect.top,\n        left: rect.left - scrollRect.left\n      });\n    }\n\n    this.height = options.el.offsetHeight;\n    this.width = options.el.offsetWidth;\n    this.left = rect.left;\n    this.right = rect.right;\n    this.top = rect.top;\n    this.bottom = rect.bottom;\n\n    if (options.rootMargin) {\n      this._setRectWithRootMargin(options.rootMargin);\n    }\n  }\n  /**\r\n   * Apply root margin to all properties\r\n   */\n\n\n  var _proto = Rect.prototype;\n\n  _proto._setRectWithRootMargin = function _setRectWithRootMargin(rootMargin) {\n    var totalRootY = rootMargin.top + rootMargin.bottom;\n    var totalRootX = rootMargin.left + rootMargin.right;\n    this.top -= rootMargin.top;\n    this.right += rootMargin.right;\n    this.bottom += rootMargin.bottom;\n    this.left -= rootMargin.left;\n    this.height += totalRootY;\n    this.width += totalRootX;\n  };\n\n  return Rect;\n}();\n\nvar VALID_UNITS = [ScaleUnits[''], Units.px, Units['%'], Units['vh'], Units['vw'], RotationUnits.deg, RotationUnits.turn, RotationUnits.rad];\n/**\r\n * Determines the unit of a string and parses the value\r\n */\n\nfunction parseValueAndUnit(str, defaultUnit) {\n  if (defaultUnit === void 0) {\n    defaultUnit = Units['%'];\n  }\n\n  var out = {\n    value: 0,\n    unit: defaultUnit\n  };\n  if (typeof str === 'undefined') return out;\n  var isValid = typeof str === 'number' || typeof str === 'string';\n\n  if (!isValid) {\n    throw new Error('Invalid value provided. Must provide a value as a string or number');\n  }\n\n  str = String(str);\n  out.value = parseFloat(str); // @ts-ignore\n\n  out.unit = str.match(/[\\d.\\-+]*\\s*(.*)/)[1] || defaultUnit; // @ts-expect-error\n\n  var isValidUnit = VALID_UNITS.includes(out.unit);\n\n  if (!isValidUnit) {\n    throw new Error('Invalid unit provided.');\n  }\n\n  return out;\n}\n\nvar easingPresets = {\n  ease: [0.25, 0.1, 0.25, 1.0],\n  easeIn: [0.42, 0.0, 1.0, 1.0],\n  easeOut: [0.0, 0.0, 0.58, 1.0],\n  easeInOut: [0.42, 0.0, 0.58, 1.0],\n\n  /* Ease IN curves */\n  easeInQuad: [0.55, 0.085, 0.68, 0.53],\n  easeInCubic: [0.55, 0.055, 0.675, 0.19],\n  easeInQuart: [0.895, 0.03, 0.685, 0.22],\n  easeInQuint: [0.755, 0.05, 0.855, 0.06],\n  easeInSine: [0.47, 0.0, 0.745, 0.715],\n  easeInExpo: [0.95, 0.05, 0.795, 0.035],\n  easeInCirc: [0.6, 0.04, 0.98, 0.335],\n\n  /* Ease Out Curves */\n  easeOutQuad: [0.25, 0.46, 0.45, 0.94],\n  easeOutCubic: [0.215, 0.61, 0.355, 1.0],\n  easeOutQuart: [0.165, 0.84, 0.44, 1.0],\n  easeOutQuint: [0.23, 1.0, 0.32, 1.0],\n  easeOutSine: [0.39, 0.575, 0.565, 1.0],\n  easeOutExpo: [0.19, 1.0, 0.22, 1.0],\n  easeOutCirc: [0.075, 0.82, 0.165, 1.0],\n\n  /* Ease IN Out Curves */\n  easeInOutQuad: [0.455, 0.03, 0.515, 0.955],\n  easeInOutCubic: [0.645, 0.045, 0.355, 1.0],\n  easeInOutQuart: [0.77, 0.0, 0.175, 1.0],\n  easeInOutQuint: [0.86, 0.0, 0.07, 1.0],\n  easeInOutSine: [0.445, 0.05, 0.55, 0.95],\n  easeInOutExpo: [1.0, 0.0, 0.0, 1.0],\n  easeInOutCirc: [0.785, 0.135, 0.15, 0.86],\n\n  /* Ease Bounce Curves */\n  easeInBack: [0.6, -0.28, 0.735, 0.045],\n  easeOutBack: [0.175, 0.885, 0.32, 1.275],\n  easeInOutBack: [0.68, -0.55, 0.265, 1.55]\n};\n\nfunction createEasingFunction(easing) {\n  if (Array.isArray(easing)) {\n    return bezier_easing__WEBPACK_IMPORTED_MODULE_0___default()(easing[0], easing[1], easing[2], easing[3]);\n  }\n\n  if (typeof easing === 'string' && typeof easingPresets[easing] !== 'undefined') {\n    var params = easingPresets[easing];\n    return bezier_easing__WEBPACK_IMPORTED_MODULE_0___default()(params[0], params[1], params[2], params[3]);\n  }\n\n  return;\n}\n\nvar PARALLAX_EFFECTS = /*#__PURE__*/Object.values(ValidCSSEffects);\nvar MAP_EFFECT_TO_DEFAULT_UNIT = {\n  speed: 'px',\n  translateX: '%',\n  translateY: '%',\n  rotate: 'deg',\n  rotateX: 'deg',\n  rotateY: 'deg',\n  rotateZ: 'deg',\n  scale: '',\n  scaleX: '',\n  scaleY: '',\n  scaleZ: '',\n  opacity: ''\n};\n/**\r\n * Takes a parallax element effects and parses the properties to get the start and end values and units.\r\n */\n\nfunction parseElementTransitionEffects(props, scrollAxis) {\n  var parsedEffects = {};\n  PARALLAX_EFFECTS.forEach(function (key) {\n    var defaultValue = MAP_EFFECT_TO_DEFAULT_UNIT[key]; // If the provided type is a number, this must be the speed prop\n    // in which case we need to construct the proper translate config\n\n    if (typeof (props == null ? void 0 : props[key]) === 'number') {\n      var value = props == null ? void 0 : props[key];\n      var startSpeed = (value || 0) * 10 + \"px\";\n      var endSpeed = (value || 0) * -10 + \"px\";\n      var startParsed = parseValueAndUnit(startSpeed);\n      var endParsed = parseValueAndUnit(endSpeed);\n      var speedConfig = {\n        start: startParsed.value,\n        end: endParsed.value,\n        unit: startParsed.unit\n      }; // Manually set translate y value\n\n      if (scrollAxis === ScrollAxis.vertical) {\n        parsedEffects.translateY = speedConfig;\n      } // Manually set translate y value\n\n\n      if (scrollAxis === ScrollAxis.horizontal) {\n        parsedEffects.translateX = speedConfig;\n      }\n    } // The rest are standard effect being parsed\n\n\n    if (Array.isArray(props == null ? void 0 : props[key])) {\n      var _value = props == null ? void 0 : props[key];\n\n      if (typeof _value[0] !== 'undefined' && typeof _value[1] !== 'undefined') {\n        var _startParsed = parseValueAndUnit(_value == null ? void 0 : _value[0], defaultValue);\n\n        var _endParsed = parseValueAndUnit(_value == null ? void 0 : _value[1], defaultValue);\n\n        var easing = createEasingFunction(_value == null ? void 0 : _value[2]);\n        parsedEffects[key] = {\n          start: _startParsed.value,\n          end: _endParsed.value,\n          unit: _startParsed.unit,\n          easing: easing\n        };\n\n        if (_startParsed.unit !== _endParsed.unit) {\n          throw new Error('Must provide matching units for the min and max offset values of each axis.');\n        }\n      }\n    }\n  });\n  return parsedEffects;\n}\n\n/**\r\n * Returns the percent (0 - 100) moved based on position in the viewport\r\n */\nfunction getProgressAmount(\n/*\r\n * The start value from cache\r\n */\nstart,\n/*\r\n * total dist the element has to move to be 100% complete (view width/height + element width/height)\r\n */\ntotalDist,\n/*\r\n * Current scroll value\r\n */\ncurrentScroll,\n/*\r\n * an optional easing function to apply\r\n */\neasing) {\n  // adjust cached value\n  var startAdjustedScroll = currentScroll - start; // Amount the element has moved based on current and total distance to move\n\n  var amount = startAdjustedScroll / totalDist; // Apply bezier easing if provided\n\n  if (easing) {\n    amount = easing(amount);\n  }\n\n  return amount;\n}\n\n/**\r\n * Takes two values (start, end) and returns whether the current scroll is within range\r\n * @param {number} start - start of scroll (x/y)\r\n * @param {number} end - end of scroll (x/y)\r\n * @param {number} scroll - current scroll (x/y)\r\n * @return {boolean} isInView\r\n */\nfunction isElementInView(start, end, scroll) {\n  var isInView = scroll >= start && scroll <= end;\n  return isInView;\n}\n\n// Scale between AKA normalize\nfunction scaleBetween(value, newMin, newMax, oldMin, oldMax) {\n  return (newMax - newMin) * (value - oldMin) / (oldMax - oldMin) + newMin;\n}\n\n/**\r\n * Scales a start and end value of an effect based on percent moved and easing function\r\n */\n\nfunction scaleEffectByProgress(effect, progress) {\n  var value = scaleBetween(typeof effect.easing === 'function' ? effect.easing(progress) : progress, (effect == null ? void 0 : effect.start) || 0, (effect == null ? void 0 : effect.end) || 0, 0, 1);\n  return {\n    value: value,\n    unit: effect == null ? void 0 : effect.unit\n  };\n}\n\nvar TRANSFORM_EFFECTS = /*#__PURE__*/Object.values(ValidCSSEffects).filter(function (v) {\n  return v !== 'opacity';\n});\nfunction setWillChangeStyles(el, effects) {\n  var keys = Object.keys(effects);\n  var hasOpacity = keys.includes('opacity');\n  var willChange = \"transform\" + (hasOpacity ? ',opacity' : '');\n  el.style.willChange = willChange;\n}\nfunction setElementStyles(effects, progress, el) {\n  if (!el) return;\n  var transform = getTransformStyles(effects, progress);\n  var opacity = getOpacityStyles(effects, progress);\n  el.style.transform = transform;\n  el.style.opacity = opacity;\n}\nfunction getOpacityStyles(effects, progress) {\n  var scaledOpacity = effects['opacity'] && scaleEffectByProgress(effects['opacity'], progress);\n\n  if (typeof scaledOpacity === 'undefined' || typeof scaledOpacity.value === 'undefined' || typeof scaledOpacity.unit === 'undefined') {\n    return '';\n  }\n\n  var styleStr = \"\" + scaledOpacity.value;\n  return styleStr;\n}\nfunction getTransformStyles(effects, progress) {\n  var transform = TRANSFORM_EFFECTS.reduce(function (acc, key) {\n    var scaledEffect = // @ts-expect-error\n    effects[key] && scaleEffectByProgress(effects[key], progress);\n\n    if (typeof scaledEffect === 'undefined' || typeof scaledEffect.value === 'undefined' || typeof scaledEffect.unit === 'undefined') {\n      return acc;\n    }\n\n    var styleStr = key + \"(\" + scaledEffect.value + scaledEffect.unit + \")\";\n    return acc + styleStr;\n  }, '');\n  return transform;\n}\n/**\r\n * Takes a parallax element and removes parallax offset styles.\r\n * @param {object} element\r\n */\n\nfunction resetStyles(element) {\n  var el = element.el;\n  if (!el) return;\n  el.style.transform = '';\n  el.style.opacity = '';\n}\n\nfunction createLimitsForRelativeElements(rect, view, scroll, shouldAlwaysCompleteAnimation) {\n  var startY = rect.top - view.height;\n  var startX = rect.left - view.width;\n  var endY = rect.bottom;\n  var endX = rect.right; // add scroll\n\n  startX += scroll.x;\n  endX += scroll.x;\n  startY += scroll.y;\n  endY += scroll.y;\n\n  if (shouldAlwaysCompleteAnimation) {\n    if (scroll.y + rect.top < view.height) {\n      startY = 0;\n    }\n\n    if (scroll.x + rect.left < view.width) {\n      startX = 0;\n    }\n\n    if (endY > view.scrollHeight - view.height) {\n      endY = view.scrollHeight - view.height;\n    }\n\n    if (endX > view.scrollWidth - view.width) {\n      endX = view.scrollWidth - view.width;\n    }\n  }\n\n  var limits = new Limits({\n    startX: startX,\n    startY: startY,\n    endX: endX,\n    endY: endY\n  });\n  return limits;\n}\n\nfunction getTranslateScalar(startTranslatePx, endTranslatePx, totalDist) {\n  var slow = endTranslatePx > startTranslatePx; // calculating necessary scale to increase translations\n\n  var totalAbsOff = (Math.abs(startTranslatePx) + Math.abs(endTranslatePx)) * (slow ? -1 : 1);\n  var totalDistTrue = totalDist + totalAbsOff; // Determine multiple to scale by, only values greater than 1\n\n  var scale = Math.max(totalDist / totalDistTrue, 1);\n  return scale;\n}\n\n/**\r\n * Return the start and end pixel values for an elements translations\r\n */\nfunction getStartEndValueInPx(translate, elementSize) {\n  var start = translate.start,\n      end = translate.end,\n      unit = translate.unit;\n\n  if (unit === '%') {\n    var scale = elementSize / 100;\n    start = start * scale;\n    end = end * scale;\n  }\n\n  if (unit === 'vw') {\n    var startScale = start / 100;\n    var endScale = end / 100;\n    start = window.innerWidth * startScale;\n    end = window.innerWidth * endScale;\n  }\n\n  if (unit === 'vh') {\n    var _startScale = start / 100;\n\n    var _endScale = end / 100;\n\n    start = window.innerHeight * _startScale;\n    end = window.innerHeight * _endScale;\n  }\n\n  return {\n    start: start,\n    end: end\n  };\n}\n\nvar DEFAULT_VALUE = {\n  start: 0,\n  end: 0,\n  unit: ''\n};\nfunction createLimitsWithTranslationsForRelativeElements(rect, view, effects, scroll, scrollAxis, shouldAlwaysCompleteAnimation) {\n  // get start and end accounting for percent effects\n  var translateX = effects.translateX || DEFAULT_VALUE;\n  var translateY = effects.translateY || DEFAULT_VALUE;\n\n  var _getStartEndValueInPx = getStartEndValueInPx(translateX, rect.width),\n      startTranslateXPx = _getStartEndValueInPx.start,\n      endTranslateXPx = _getStartEndValueInPx.end;\n\n  var _getStartEndValueInPx2 = getStartEndValueInPx(translateY, rect.height),\n      startTranslateYPx = _getStartEndValueInPx2.start,\n      endTranslateYPx = _getStartEndValueInPx2.end; // default starting values\n\n\n  var startY = rect.top - view.height;\n  var startX = rect.left - view.width;\n  var endY = rect.bottom;\n  var endX = rect.right;\n  var startMultiplierY = 1;\n  var endMultiplierY = 1;\n\n  if (scrollAxis === ScrollAxis.vertical) {\n    startMultiplierY = getTranslateScalar(startTranslateYPx, endTranslateYPx, view.height + rect.height);\n    endMultiplierY = startMultiplierY;\n  }\n\n  var startMultiplierX = 1;\n  var endMultiplierX = 1;\n\n  if (scrollAxis === ScrollAxis.horizontal) {\n    startMultiplierX = getTranslateScalar(startTranslateXPx, endTranslateXPx, view.width + rect.width);\n    endMultiplierX = startMultiplierX;\n  } // Apply the scale to initial values\n\n\n  if (startTranslateYPx < 0) {\n    startY = startY + startTranslateYPx * startMultiplierY;\n  }\n\n  if (endTranslateYPx > 0) {\n    endY = endY + endTranslateYPx * endMultiplierY;\n  }\n\n  if (startTranslateXPx < 0) {\n    startX = startX + startTranslateXPx * startMultiplierX;\n  }\n\n  if (endTranslateXPx > 0) {\n    endX = endX + endTranslateXPx * endMultiplierX;\n  } // add scroll\n\n\n  startX += scroll.x;\n  endX += scroll.x;\n  startY += scroll.y;\n  endY += scroll.y; // NOTE: please refactor and isolate this :(\n\n  if (shouldAlwaysCompleteAnimation) {\n    var topBeginsInView = scroll.y + rect.top < view.height;\n    var leftBeginsInView = scroll.x + rect.left < view.width;\n    var bottomEndsInView = scroll.y + rect.bottom > view.scrollHeight - view.height;\n    var rightEndsInView = scroll.x + rect.right > view.scrollWidth - view.height;\n\n    if (topBeginsInView && bottomEndsInView) {\n      startMultiplierY = 1;\n      endMultiplierY = 1;\n      startY = 0;\n      endY = view.scrollHeight - view.height;\n    }\n\n    if (leftBeginsInView && rightEndsInView) {\n      startMultiplierX = 1;\n      endMultiplierX = 1;\n      startX = 0;\n      endX = view.scrollWidth - view.width;\n    }\n\n    if (!topBeginsInView && bottomEndsInView) {\n      startY = rect.top - view.height + scroll.y;\n      endY = view.scrollHeight - view.height;\n      var totalDist = endY - startY;\n      startMultiplierY = getTranslateScalar(startTranslateYPx, endTranslateYPx, totalDist);\n      endMultiplierY = 1;\n\n      if (startTranslateYPx < 0) {\n        startY = startY + startTranslateYPx * startMultiplierY;\n      }\n    }\n\n    if (!leftBeginsInView && rightEndsInView) {\n      startX = rect.left - view.width + scroll.x;\n      endX = view.scrollWidth - view.width;\n\n      var _totalDist = endX - startX;\n\n      startMultiplierX = getTranslateScalar(startTranslateXPx, endTranslateXPx, _totalDist);\n      endMultiplierX = 1;\n\n      if (startTranslateXPx < 0) {\n        startX = startX + startTranslateXPx * startMultiplierX;\n      }\n    }\n\n    if (topBeginsInView && !bottomEndsInView) {\n      startY = 0;\n      endY = rect.bottom + scroll.y;\n\n      var _totalDist2 = endY - startY;\n\n      startMultiplierY = 1;\n      endMultiplierY = getTranslateScalar(startTranslateYPx, endTranslateYPx, _totalDist2);\n\n      if (endTranslateYPx > 0) {\n        endY = endY + endTranslateYPx * endMultiplierY;\n      }\n    }\n\n    if (leftBeginsInView && !rightEndsInView) {\n      startX = 0;\n      endX = rect.right + scroll.x;\n\n      var _totalDist3 = endX - startX;\n\n      startMultiplierX = 1;\n      endMultiplierX = getTranslateScalar(startTranslateXPx, endTranslateXPx, _totalDist3);\n\n      if (endTranslateXPx > 0) {\n        endX = endX + endTranslateXPx * endMultiplierX;\n      }\n    }\n  }\n\n  var limits = new Limits({\n    startX: startX,\n    startY: startY,\n    endX: endX,\n    endY: endY,\n    startMultiplierX: startMultiplierX,\n    endMultiplierX: endMultiplierX,\n    startMultiplierY: startMultiplierY,\n    endMultiplierY: endMultiplierY\n  });\n  return limits;\n}\n\nfunction scaleTranslateEffectsForSlowerScroll(effects, limits) {\n  var effectsCopy = _extends({}, effects);\n\n  if (effectsCopy.translateX) {\n    effectsCopy.translateX = _extends({}, effects.translateX, {\n      start: effectsCopy.translateX.start * limits.startMultiplierX,\n      end: effectsCopy.translateX.end * limits.endMultiplierX\n    });\n  }\n\n  if (effectsCopy.translateY) {\n    effectsCopy.translateY = _extends({}, effects.translateY, {\n      start: effectsCopy.translateY.start * limits.startMultiplierY,\n      end: effectsCopy.translateY.end * limits.endMultiplierY\n    });\n  }\n\n  return effectsCopy;\n}\n\nfunction getShouldScaleTranslateEffects(props, effects, scrollAxis) {\n  if (props.rootMargin || props.targetElement || props.shouldDisableScalingTranslations) {\n    return false;\n  }\n\n  if (!!effects.translateX && scrollAxis === ScrollAxis.horizontal || !!effects.translateY && scrollAxis === ScrollAxis.vertical) {\n    return true;\n  }\n\n  return false;\n}\n\nvar clamp = function clamp(num, min, max) {\n  return Math.min(Math.max(num, min), max);\n};\n\nvar Element = /*#__PURE__*/function () {\n  function Element(options) {\n    this.el = options.el;\n    this.props = options.props;\n    this.scrollAxis = options.scrollAxis;\n    this.disabledParallaxController = options.disabledParallaxController || false;\n    this.id = createId();\n    this.effects = parseElementTransitionEffects(this.props, this.scrollAxis);\n    this.isInView = null;\n    this.progress = 0;\n\n    this._setElementEasing(options.props.easing);\n\n    setWillChangeStyles(options.el, this.effects);\n  }\n\n  var _proto = Element.prototype;\n\n  _proto.updateProps = function updateProps(nextProps) {\n    this.props = _extends({}, this.props, nextProps);\n    this.effects = parseElementTransitionEffects(nextProps, this.scrollAxis);\n\n    this._setElementEasing(nextProps.easing);\n\n    return this;\n  };\n\n  _proto.setCachedAttributes = function setCachedAttributes(view, scroll) {\n    // NOTE: Must reset styles before getting the rect, as it might impact the natural position\n    resetStyles(this);\n    this.rect = new Rect({\n      el: this.props.targetElement || this.el,\n      rootMargin: this.props.rootMargin,\n      view: view\n    });\n    var shouldScaleTranslateEffects = getShouldScaleTranslateEffects(this.props, this.effects, this.scrollAxis);\n\n    if (typeof this.props.startScroll === 'number' && typeof this.props.endScroll === 'number') {\n      this.limits = new Limits({\n        startX: this.props.startScroll,\n        startY: this.props.startScroll,\n        endX: this.props.endScroll,\n        endY: this.props.endScroll\n      }); // Undo the reset -- place it back at current position with styles\n\n      this._setElementStyles();\n\n      return this;\n    }\n\n    if (shouldScaleTranslateEffects) {\n      this.limits = createLimitsWithTranslationsForRelativeElements(this.rect, view, this.effects, scroll, this.scrollAxis, this.props.shouldAlwaysCompleteAnimation);\n      this.scaledEffects = scaleTranslateEffectsForSlowerScroll(this.effects, this.limits);\n    } else {\n      this.limits = createLimitsForRelativeElements(this.rect, view, scroll, this.props.shouldAlwaysCompleteAnimation);\n    } // Undo the reset -- place it back at current position with styles\n\n\n    this._setElementStyles();\n\n    return this;\n  };\n\n  _proto._updateElementIsInView = function _updateElementIsInView(nextIsInView) {\n    // NOTE: Check if this is the first change to make sure onExit isn't called\n    var isFirstChange = this.isInView === null;\n\n    if (nextIsInView !== this.isInView) {\n      if (nextIsInView) {\n        this.props.onEnter && this.props.onEnter(this);\n      } else if (!isFirstChange) {\n        this._setFinalProgress();\n\n        this._setElementStyles();\n\n        this.props.onExit && this.props.onExit(this);\n      }\n    }\n\n    this.isInView = nextIsInView;\n  };\n\n  _proto._setFinalProgress = function _setFinalProgress() {\n    var finalProgress = clamp(Math.round(this.progress), 0, 1);\n\n    this._updateElementProgress(finalProgress);\n  };\n\n  _proto._setElementStyles = function _setElementStyles() {\n    if (this.props.disabled || this.disabledParallaxController) return;\n    var effects = this.scaledEffects || this.effects;\n    setElementStyles(effects, this.progress, this.el);\n  };\n\n  _proto._updateElementProgress = function _updateElementProgress(nextProgress) {\n    this.progress = nextProgress;\n    this.props.onProgressChange && this.props.onProgressChange(this.progress);\n    this.props.onChange && this.props.onChange(this);\n  };\n\n  _proto._setElementEasing = function _setElementEasing(easing) {\n    this.easing = createEasingFunction(easing);\n  };\n\n  _proto.updateElementOptions = function updateElementOptions(options) {\n    this.scrollAxis = options.scrollAxis;\n    this.disabledParallaxController = options.disabledParallaxController || false;\n  };\n\n  _proto.updatePosition = function updatePosition(scroll) {\n    if (!this.limits) return this;\n    var isVertical = this.scrollAxis === ScrollAxis.vertical;\n    var isFirstChange = this.isInView === null; // based on scroll axis\n\n    var start = isVertical ? this.limits.startY : this.limits.startX;\n    var end = isVertical ? this.limits.endY : this.limits.endX;\n    var total = isVertical ? this.limits.totalY : this.limits.totalX;\n    var s = isVertical ? scroll.y : scroll.x; // check if in view\n\n    var nextIsInView = isElementInView(start, end, s);\n\n    this._updateElementIsInView(nextIsInView); // set the progress if in view or this is the first change\n\n\n    if (nextIsInView) {\n      var nextProgress = getProgressAmount(start, total, s, this.easing);\n\n      this._updateElementProgress(nextProgress);\n\n      this._setElementStyles();\n    } else if (isFirstChange) {\n      // NOTE: this._updateElementProgress -- dont use this because it will trigger onChange\n      this.progress = clamp(Math.round(getProgressAmount(start, total, s, this.easing)), 0, 1);\n\n      this._setElementStyles();\n    }\n\n    return this;\n  };\n\n  return Element;\n}();\n\nvar View = /*#__PURE__*/function () {\n  function View(config) {\n    this.scrollContainer = config.scrollContainer;\n    this.width = config.width;\n    this.height = config.height;\n    this.scrollHeight = config.scrollHeight;\n    this.scrollWidth = config.scrollWidth;\n  }\n\n  var _proto = View.prototype;\n\n  _proto.hasChanged = function hasChanged(params) {\n    if (params.width !== this.width || params.height !== this.height || params.scrollWidth !== this.scrollWidth || params.scrollHeight !== this.scrollHeight) {\n      return true;\n    }\n\n    return false;\n  };\n\n  _proto.setSize = function setSize(params) {\n    this.width = params.width;\n    this.height = params.height;\n    this.scrollHeight = params.scrollHeight;\n    this.scrollWidth = params.scrollWidth;\n    return this;\n  };\n\n  return View;\n}();\n\nvar Scroll = /*#__PURE__*/function () {\n  function Scroll(x, y) {\n    this.x = x;\n    this.y = y;\n    this.dx = 0;\n    this.dy = 0;\n  }\n\n  var _proto = Scroll.prototype;\n\n  _proto.setScroll = function setScroll(x, y) {\n    this.dx = x - this.x;\n    this.dy = y - this.y;\n    this.x = x;\n    this.y = y;\n    return this;\n  };\n\n  return Scroll;\n}();\n\nfunction testForPassiveScroll() {\n  var supportsPassiveOption = false;\n\n  try {\n    var opts = Object.defineProperty({}, 'passive', {\n      get: function get() {\n        supportsPassiveOption = true;\n        return true;\n      }\n    }); // @ts-expect-error\n\n    window.addEventListener('test', null, opts); // @ts-expect-error\n\n    window.removeEventListener('test', null, opts);\n  } catch (e) {}\n\n  return supportsPassiveOption;\n}\n\n/**\r\n * -------------------------------------------------------\r\n * Parallax Controller\r\n * -------------------------------------------------------\r\n *\r\n * The global controller for setting up and managing a scroll view of elements.\r\n *\r\n */\n\nvar ParallaxController = /*#__PURE__*/function () {\n  function ParallaxController(_ref) {\n    var _ref$scrollAxis = _ref.scrollAxis,\n        scrollAxis = _ref$scrollAxis === void 0 ? ScrollAxis.vertical : _ref$scrollAxis,\n        scrollContainer = _ref.scrollContainer,\n        _ref$disabled = _ref.disabled,\n        disabled = _ref$disabled === void 0 ? false : _ref$disabled;\n    this.disabled = disabled;\n    this.scrollAxis = scrollAxis; // All parallax elements to be updated\n\n    this.elements = [];\n    this._hasScrollContainer = !!scrollContainer;\n    this.viewEl = scrollContainer != null ? scrollContainer : window; // Scroll and View\n\n    var _this$_getScrollPosit = this._getScrollPosition(),\n        x = _this$_getScrollPosit[0],\n        y = _this$_getScrollPosit[1];\n\n    this.scroll = new Scroll(x, y);\n    this.view = new View({\n      width: 0,\n      height: 0,\n      scrollWidth: 0,\n      scrollHeight: 0,\n      scrollContainer: this._hasScrollContainer ? scrollContainer : undefined\n    }); // Ticking\n\n    this._ticking = false; // Passive support\n\n    this._supportsPassive = testForPassiveScroll(); // Bind methods to class\n\n    this._bindAllMethods(); // If this is initialized disabled, don't do anything below.\n\n\n    if (this.disabled) return;\n\n    this._addListeners(this.viewEl);\n\n    this._addResizeObserver();\n\n    this._setViewSize();\n  }\n  /**\r\n   * Static method to instantiate the ParallaxController.\r\n   * @returns {Class} ParallaxController\r\n   */\n\n\n  ParallaxController.init = function init(options) {\n    var hasWindow = typeof window !== 'undefined';\n\n    if (!hasWindow) {\n      throw new Error('Looks like ParallaxController.init() was called on the server. This method must be called on the client.');\n    }\n\n    return new ParallaxController(options);\n  };\n\n  var _proto = ParallaxController.prototype;\n\n  _proto._bindAllMethods = function _bindAllMethods() {\n    var _this = this;\n\n    ['_addListeners', '_removeListeners', '_getScrollPosition', '_handleScroll', '_handleUpdateCache', '_updateAllElements', '_updateElementPosition', '_setViewSize', '_addResizeObserver', '_checkIfViewHasChanged', '_getViewParams', 'getElements', 'createElement', 'removeElementById', 'resetElementStyles', 'updateElementPropsById', 'update', 'updateScrollContainer', 'destroy'].forEach(function (method) {\n      // @ts-expect-error\n      _this[method] = _this[method].bind(_this);\n    });\n  };\n\n  _proto._addListeners = function _addListeners(el) {\n    el.addEventListener('scroll', this._handleScroll, this._supportsPassive ? {\n      passive: true\n    } : false);\n    window.addEventListener('resize', this._handleUpdateCache, false);\n    window.addEventListener('blur', this._handleUpdateCache, false);\n    window.addEventListener('focus', this._handleUpdateCache, false);\n    window.addEventListener('load', this._handleUpdateCache, false);\n  };\n\n  _proto._removeListeners = function _removeListeners(el) {\n    var _this$_resizeObserver;\n\n    el.removeEventListener('scroll', this._handleScroll, false);\n    window.removeEventListener('resize', this._handleUpdateCache, false);\n    window.removeEventListener('blur', this._handleUpdateCache, false);\n    window.removeEventListener('focus', this._handleUpdateCache, false);\n    window.removeEventListener('load', this._handleUpdateCache, false);\n    (_this$_resizeObserver = this._resizeObserver) == null ? void 0 : _this$_resizeObserver.disconnect();\n  };\n\n  _proto._addResizeObserver = function _addResizeObserver() {\n    var _this2 = this;\n\n    try {\n      var observedEl = this._hasScrollContainer ? this.viewEl : document.documentElement;\n      this._resizeObserver = new ResizeObserver(function () {\n        return _this2.update();\n      });\n\n      this._resizeObserver.observe(observedEl);\n    } catch (e) {\n      console.warn('Failed to create the resize observer in the ParallaxContoller');\n    }\n  };\n\n  _proto._getScrollPosition = function _getScrollPosition() {\n    // Save current scroll\n    // Supports IE 9 and up.\n    var nx = this._hasScrollContainer ? // @ts-expect-error\n    this.viewEl.scrollLeft : window.pageXOffset;\n    var ny = this._hasScrollContainer ? // @ts-expect-error\n    this.viewEl.scrollTop : window.pageYOffset;\n    return [nx, ny];\n  }\n  /**\r\n   * Window scroll handler sets scroll position\r\n   * and then calls '_updateAllElements()'.\r\n   */\n  ;\n\n  _proto._handleScroll = function _handleScroll() {\n    var _this$elements;\n\n    var _this$_getScrollPosit2 = this._getScrollPosition(),\n        nx = _this$_getScrollPosit2[0],\n        ny = _this$_getScrollPosit2[1];\n\n    this.scroll.setScroll(nx, ny); // Only called if the last animation request has been\n    // completed and there are parallax elements to update\n\n    if (!this._ticking && ((_this$elements = this.elements) == null ? void 0 : _this$elements.length) > 0) {\n      this._ticking = true; // @ts-ignore\n\n      window.requestAnimationFrame(this._updateAllElements);\n    }\n  }\n  /**\r\n   * Window resize handler. Sets the new window inner height\r\n   * then updates parallax element attributes and positions.\r\n   */\n  ;\n\n  _proto._handleUpdateCache = function _handleUpdateCache() {\n    this._setViewSize();\n\n    this._updateAllElements({\n      updateCache: true\n    });\n  }\n  /**\r\n   * Update element positions.\r\n   * Determines if the element is in view based on the cached\r\n   * attributes, if so set the elements parallax styles.\r\n   */\n  ;\n\n  _proto._updateAllElements = function _updateAllElements(_temp) {\n    var _this3 = this;\n\n    var _ref2 = _temp === void 0 ? {} : _temp,\n        updateCache = _ref2.updateCache;\n\n    if (this.elements) {\n      this.elements.forEach(function (element) {\n        if (updateCache) {\n          element.setCachedAttributes(_this3.view, _this3.scroll);\n        }\n\n        _this3._updateElementPosition(element);\n      });\n    } // reset ticking so more animations can be called\n\n\n    this._ticking = false;\n  }\n  /**\r\n   * Update element positions.\r\n   * Determines if the element is in view based on the cached\r\n   * attributes, if so set the elements parallax styles.\r\n   */\n  ;\n\n  _proto._updateElementPosition = function _updateElementPosition(element) {\n    if (element.props.disabled || this.disabled) return;\n    element.updatePosition(this.scroll);\n  }\n  /**\r\n   * Gets the params to set in the View from the scroll container or the window\r\n   */\n  ;\n\n  _proto._getViewParams = function _getViewParams() {\n    if (this._hasScrollContainer) {\n      // @ts-expect-error\n      var _width = this.viewEl.offsetWidth; // @ts-expect-error\n\n      var _height = this.viewEl.offsetHeight; // @ts-expect-error\n\n      var _scrollHeight = this.viewEl.scrollHeight; // @ts-expect-error\n\n      var _scrollWidth = this.viewEl.scrollWidth;\n      return this.view.setSize({\n        width: _width,\n        height: _height,\n        scrollHeight: _scrollHeight,\n        scrollWidth: _scrollWidth\n      });\n    }\n\n    var html = document.documentElement;\n    var width = window.innerWidth || html.clientWidth;\n    var height = window.innerHeight || html.clientHeight;\n    var scrollHeight = html.scrollHeight;\n    var scrollWidth = html.scrollWidth;\n    return {\n      width: width,\n      height: height,\n      scrollHeight: scrollHeight,\n      scrollWidth: scrollWidth\n    };\n  }\n  /**\r\n   * Cache the view attributes\r\n   */\n  ;\n\n  _proto._setViewSize = function _setViewSize() {\n    return this.view.setSize(this._getViewParams());\n  }\n  /**\r\n   * Checks if any of the cached attributes of the view have changed.\r\n   * @returns boolean\r\n   */\n  ;\n\n  _proto._checkIfViewHasChanged = function _checkIfViewHasChanged() {\n    return this.view.hasChanged(this._getViewParams());\n  }\n  /**\r\n   * -------------------------------------------------------\r\n   * Public methods\r\n   * -------------------------------------------------------\r\n   */\n\n  /**\r\n   * Returns all the parallax elements in the controller\r\n   */\n  ;\n\n  _proto.getElements = function getElements() {\n    return this.elements;\n  }\n  /**\r\n   * Creates and returns new parallax element with provided options to be managed by the controller.\r\n   */\n  ;\n\n  _proto.createElement = function createElement(options) {\n    var newElement = new Element(_extends({}, options, {\n      scrollAxis: this.scrollAxis,\n      disabledParallaxController: this.disabled\n    }));\n    newElement.setCachedAttributes(this.view, this.scroll);\n    this.elements = this.elements ? [].concat(this.elements, [newElement]) : [newElement];\n\n    this._updateElementPosition(newElement); // NOTE: This checks if the view has changed then update the controller and all elements if it has\n    // This shouldn't always be necessary with a resize observer watching the view element\n    // but there seems to be cases where the resize observer does not catch and update.\n\n\n    if (this._checkIfViewHasChanged()) {\n      this.update();\n    }\n\n    return newElement;\n  }\n  /**\r\n   * Remove an element by id\r\n   */\n  ;\n\n  _proto.removeElementById = function removeElementById(id) {\n    if (!this.elements) return;\n    this.elements = this.elements.filter(function (el) {\n      return el.id !== id;\n    });\n  }\n  /**\r\n   * Updates an existing parallax element object with new options.\r\n   */\n  ;\n\n  _proto.updateElementPropsById = function updateElementPropsById(id, props) {\n    if (this.elements) {\n      this.elements = this.elements.map(function (el) {\n        if (el.id === id) {\n          return el.updateProps(props);\n        }\n\n        return el;\n      });\n    }\n\n    this.update();\n  }\n  /**\r\n   * Remove a target elements parallax styles\r\n   */\n  ;\n\n  _proto.resetElementStyles = function resetElementStyles(element) {\n    resetStyles(element);\n  }\n  /**\r\n   * Updates all cached attributes on parallax elements.\r\n   */\n  ;\n\n  _proto.update = function update() {\n    // Save the latest scroll position because window.scroll\n    // may be called and the handle scroll event may not be called.\n    var _this$_getScrollPosit3 = this._getScrollPosition(),\n        nx = _this$_getScrollPosit3[0],\n        ny = _this$_getScrollPosit3[1];\n\n    this.scroll.setScroll(nx, ny);\n\n    this._setViewSize();\n\n    this._updateAllElements({\n      updateCache: true\n    });\n  }\n  /**\r\n   * Updates the scroll container of the parallax controller\r\n   */\n  ;\n\n  _proto.updateScrollContainer = function updateScrollContainer(el) {\n    // remove existing listeners with current el first\n    this._removeListeners(this.viewEl);\n\n    this.viewEl = el;\n    this._hasScrollContainer = !!el;\n    this.view = new View({\n      width: 0,\n      height: 0,\n      scrollWidth: 0,\n      scrollHeight: 0,\n      scrollContainer: el\n    });\n\n    this._setViewSize();\n\n    this._addListeners(this.viewEl);\n\n    this._updateAllElements({\n      updateCache: true\n    });\n  };\n\n  _proto.disableParallaxController = function disableParallaxController() {\n    this.disabled = true; // remove listeners\n\n    this._removeListeners(this.viewEl); // reset all styles\n\n\n    if (this.elements) {\n      this.elements.forEach(function (element) {\n        return resetStyles(element);\n      });\n    }\n  };\n\n  _proto.enableParallaxController = function enableParallaxController() {\n    var _this4 = this;\n\n    this.disabled = false;\n\n    if (this.elements) {\n      this.elements.forEach(function (element) {\n        return element.updateElementOptions({\n          disabledParallaxController: false,\n          scrollAxis: _this4.scrollAxis\n        });\n      });\n    } // add back listeners\n\n\n    this._addListeners(this.viewEl);\n\n    this._addResizeObserver();\n\n    this._setViewSize();\n  }\n  /**\r\n   * Disable all parallax elements\r\n   */\n  ;\n\n  _proto.disableAllElements = function disableAllElements() {\n    console.warn('deprecated: use disableParallaxController() instead');\n\n    if (this.elements) {\n      this.elements = this.elements.map(function (el) {\n        return el.updateProps({\n          disabled: true\n        });\n      });\n    }\n\n    this.update();\n  }\n  /**\r\n   * Enable all parallax elements\r\n   */\n  ;\n\n  _proto.enableAllElements = function enableAllElements() {\n    console.warn('deprecated: use enableParallaxController() instead');\n\n    if (this.elements) {\n      this.elements = this.elements.map(function (el) {\n        return el.updateProps({\n          disabled: false\n        });\n      });\n    }\n\n    this.update();\n  }\n  /**\r\n   * Removes all listeners and resets all styles on managed elements.\r\n   */\n  ;\n\n  _proto.destroy = function destroy() {\n    this._removeListeners(this.viewEl);\n\n    if (this.elements) {\n      this.elements.forEach(function (element) {\n        return resetStyles(element);\n      });\n    } // @ts-expect-error\n\n\n    this.elements = undefined;\n  };\n\n  return ParallaxController;\n}();\n\n\n//# sourceMappingURL=parallax-controller.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcGFyYWxsYXgtY29udHJvbGxlci9kaXN0L3BhcmFsbGF4LWNvbnRyb2xsZXIuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFtQzs7QUFFbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0I7O0FBRS9CO0FBQ0EseUNBQXlDOztBQUV6QztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxvQkFBb0Isc0JBQXNCO0FBQzFDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDBDQUEwQzs7QUFFM0M7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsc0JBQXNCOztBQUV2Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsc0NBQXNDOztBQUV2Qzs7QUFFQTtBQUNBO0FBQ0EsQ0FBQyxnQ0FBZ0M7O0FBRWpDOztBQUVBO0FBQ0E7QUFDQTtBQUNBLENBQUMsZ0NBQWdDOztBQUVqQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQ0FBb0M7O0FBRXJDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLG1EQUFtRDs7QUFFbkQ7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsK0JBQStCOztBQUUvQiw4REFBOEQ7O0FBRTlEOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxvREFBTTtBQUNqQjs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxvREFBTTtBQUNqQjs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSx3REFBd0Q7QUFDeEQ7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTOztBQUVUO0FBQ0E7QUFDQSxRQUFROzs7QUFHUjtBQUNBO0FBQ0E7QUFDQSxNQUFNOzs7QUFHTjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1EQUFtRDs7QUFFbkQsZ0RBQWdEOztBQUVoRDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkIsWUFBWSxTQUFTO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7O0FBRXpCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0EsZ0RBQWdEOztBQUVoRDtBQUNBLCtDQUErQzs7QUFFL0M7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esb0RBQW9EOzs7QUFHcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsSUFBSTs7O0FBR0o7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLElBQUk7OztBQUdKO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQjs7QUFFcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0EsK0JBQStCOztBQUUvQjtBQUNBLHdDQUF3QztBQUN4QztBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0Esd0NBQXdDO0FBQ3hDO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLDRCQUE0QjtBQUM1Qjs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU8sR0FBRzs7QUFFVjs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07OztBQUdOOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0Q7O0FBRWhEO0FBQ0E7QUFDQTtBQUNBLDhDQUE4Qzs7QUFFOUM7O0FBRUEsK0NBQStDOzs7QUFHL0M7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLE1BQU07QUFDTjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLENBQUM7O0FBRUQ7QUFDQTs7QUFFQTtBQUNBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUssR0FBRzs7QUFFUixpREFBaUQ7O0FBRWpEO0FBQ0EsSUFBSTs7QUFFSjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQzs7QUFFbEM7QUFDQTtBQUNBLHNFQUFzRTs7QUFFdEU7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSyxHQUFHOztBQUVSLDJCQUEyQjs7QUFFM0Isb0RBQW9EOztBQUVwRCw0QkFBNEI7OztBQUc1Qjs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsT0FBTztBQUN0Qjs7O0FBR0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPOztBQUVQO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLG1DQUFtQztBQUNuQzs7QUFFQTtBQUNBLDRCQUE0Qjs7QUFFNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxzQ0FBc0M7QUFDdEM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLE9BQU87QUFDUCxNQUFNOzs7QUFHTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsNENBQTRDOztBQUU1Qyw4Q0FBOEM7O0FBRTlDLG9EQUFvRDs7QUFFcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLDRDQUE0QztBQUM1QztBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUEsNkNBQTZDO0FBQzdDO0FBQ0E7OztBQUdBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxPQUFPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMOztBQUVBOztBQUVBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQSwwQkFBMEI7O0FBRTFCLHdDQUF3Qzs7O0FBR3hDO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1AsTUFBTTs7O0FBR047O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsTUFBTTs7O0FBR047QUFDQTs7QUFFQTtBQUNBLENBQUM7O0FBRXlVO0FBQzFVIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4vbm9kZV9tb2R1bGVzL3BhcmFsbGF4LWNvbnRyb2xsZXIvZGlzdC9wYXJhbGxheC1jb250cm9sbGVyLmVzbS5qcz9lMWQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBiZXppZXIgZnJvbSAnYmV6aWVyLWVhc2luZyc7XG5cbnZhciBMaW1pdHMgPSBmdW5jdGlvbiBMaW1pdHMocHJvcGVydGllcykge1xuICB0aGlzLnN0YXJ0WCA9IHByb3BlcnRpZXMuc3RhcnRYO1xuICB0aGlzLnN0YXJ0WSA9IHByb3BlcnRpZXMuc3RhcnRZO1xuICB0aGlzLmVuZFggPSBwcm9wZXJ0aWVzLmVuZFg7XG4gIHRoaXMuZW5kWSA9IHByb3BlcnRpZXMuZW5kWTsgLy8gVXNlZCB0byBjYWxjdWxhdGUgdGhlIHByb2dyZXNzIG9mIHRoZSBlbGVtZW50XG5cbiAgdGhpcy50b3RhbFggPSB0aGlzLmVuZFggLSB0aGlzLnN0YXJ0WDtcbiAgdGhpcy50b3RhbFkgPSB0aGlzLmVuZFkgLSB0aGlzLnN0YXJ0WTsgLy8gVXNlZCB0byBzY2FsZSB0cmFuc2xhdGUgZWZmZWN0c1xuXG4gIHRoaXMuc3RhcnRNdWx0aXBsaWVyWCA9IHByb3BlcnRpZXMuc3RhcnRNdWx0aXBsaWVyWCB8fCAxO1xuICB0aGlzLmVuZE11bHRpcGxpZXJYID0gcHJvcGVydGllcy5lbmRNdWx0aXBsaWVyWCB8fCAxO1xuICB0aGlzLnN0YXJ0TXVsdGlwbGllclkgPSBwcm9wZXJ0aWVzLnN0YXJ0TXVsdGlwbGllclkgfHwgMTtcbiAgdGhpcy5lbmRNdWx0aXBsaWVyWSA9IHByb3BlcnRpZXMuZW5kTXVsdGlwbGllclkgfHwgMTtcbn07XG5cbmZ1bmN0aW9uIF9leHRlbmRzKCkge1xuICBfZXh0ZW5kcyA9IE9iamVjdC5hc3NpZ24gfHwgZnVuY3Rpb24gKHRhcmdldCkge1xuICAgIGZvciAodmFyIGkgPSAxOyBpIDwgYXJndW1lbnRzLmxlbmd0aDsgaSsrKSB7XG4gICAgICB2YXIgc291cmNlID0gYXJndW1lbnRzW2ldO1xuXG4gICAgICBmb3IgKHZhciBrZXkgaW4gc291cmNlKSB7XG4gICAgICAgIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoc291cmNlLCBrZXkpKSB7XG4gICAgICAgICAgdGFyZ2V0W2tleV0gPSBzb3VyY2Vba2V5XTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiB0YXJnZXQ7XG4gIH07XG5cbiAgcmV0dXJuIF9leHRlbmRzLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG59XG5cbnZhciBWYWxpZENTU0VmZmVjdHM7XG5cbihmdW5jdGlvbiAoVmFsaWRDU1NFZmZlY3RzKSB7XG4gIFZhbGlkQ1NTRWZmZWN0c1tcInNwZWVkXCJdID0gXCJzcGVlZFwiO1xuICBWYWxpZENTU0VmZmVjdHNbXCJ0cmFuc2xhdGVYXCJdID0gXCJ0cmFuc2xhdGVYXCI7XG4gIFZhbGlkQ1NTRWZmZWN0c1tcInRyYW5zbGF0ZVlcIl0gPSBcInRyYW5zbGF0ZVlcIjtcbiAgVmFsaWRDU1NFZmZlY3RzW1wicm90YXRlXCJdID0gXCJyb3RhdGVcIjtcbiAgVmFsaWRDU1NFZmZlY3RzW1wicm90YXRlWFwiXSA9IFwicm90YXRlWFwiO1xuICBWYWxpZENTU0VmZmVjdHNbXCJyb3RhdGVZXCJdID0gXCJyb3RhdGVZXCI7XG4gIFZhbGlkQ1NTRWZmZWN0c1tcInJvdGF0ZVpcIl0gPSBcInJvdGF0ZVpcIjtcbiAgVmFsaWRDU1NFZmZlY3RzW1wic2NhbGVcIl0gPSBcInNjYWxlXCI7XG4gIFZhbGlkQ1NTRWZmZWN0c1tcInNjYWxlWFwiXSA9IFwic2NhbGVYXCI7XG4gIFZhbGlkQ1NTRWZmZWN0c1tcInNjYWxlWVwiXSA9IFwic2NhbGVZXCI7XG4gIFZhbGlkQ1NTRWZmZWN0c1tcInNjYWxlWlwiXSA9IFwic2NhbGVaXCI7XG4gIFZhbGlkQ1NTRWZmZWN0c1tcIm9wYWNpdHlcIl0gPSBcIm9wYWNpdHlcIjtcbn0pKFZhbGlkQ1NTRWZmZWN0cyB8fCAoVmFsaWRDU1NFZmZlY3RzID0ge30pKTtcblxudmFyIFVuaXRzO1xuXG4oZnVuY3Rpb24gKFVuaXRzKSB7XG4gIFVuaXRzW1wicHhcIl0gPSBcInB4XCI7XG4gIFVuaXRzW1wiJVwiXSA9IFwiJVwiO1xuICBVbml0c1tcInZoXCJdID0gXCJ2aFwiO1xuICBVbml0c1tcInZ3XCJdID0gXCJ2d1wiO1xufSkoVW5pdHMgfHwgKFVuaXRzID0ge30pKTtcblxudmFyIFJvdGF0aW9uVW5pdHM7XG5cbihmdW5jdGlvbiAoUm90YXRpb25Vbml0cykge1xuICBSb3RhdGlvblVuaXRzW1wiZGVnXCJdID0gXCJkZWdcIjtcbiAgUm90YXRpb25Vbml0c1tcInR1cm5cIl0gPSBcInR1cm5cIjtcbiAgUm90YXRpb25Vbml0c1tcInJhZFwiXSA9IFwicmFkXCI7XG59KShSb3RhdGlvblVuaXRzIHx8IChSb3RhdGlvblVuaXRzID0ge30pKTtcblxudmFyIFNjYWxlVW5pdHM7XG5cbihmdW5jdGlvbiAoU2NhbGVVbml0cykge1xuICBTY2FsZVVuaXRzW1wiXCJdID0gXCJcIjtcbn0pKFNjYWxlVW5pdHMgfHwgKFNjYWxlVW5pdHMgPSB7fSkpO1xuXG52YXIgU2Nyb2xsQXhpcztcblxuKGZ1bmN0aW9uIChTY3JvbGxBeGlzKSB7XG4gIFNjcm9sbEF4aXNbXCJ2ZXJ0aWNhbFwiXSA9IFwidmVydGljYWxcIjtcbiAgU2Nyb2xsQXhpc1tcImhvcml6b250YWxcIl0gPSBcImhvcml6b250YWxcIjtcbn0pKFNjcm9sbEF4aXMgfHwgKFNjcm9sbEF4aXMgPSB7fSkpO1xuXG52YXIgRWFzaW5nUHJlc2V0O1xuXG4oZnVuY3Rpb24gKEVhc2luZ1ByZXNldCkge1xuICBFYXNpbmdQcmVzZXRbXCJlYXNlXCJdID0gXCJlYXNlXCI7XG4gIEVhc2luZ1ByZXNldFtcImVhc2VJblwiXSA9IFwiZWFzZUluXCI7XG4gIEVhc2luZ1ByZXNldFtcImVhc2VPdXRcIl0gPSBcImVhc2VPdXRcIjtcbiAgRWFzaW5nUHJlc2V0W1wiZWFzZUluT3V0XCJdID0gXCJlYXNlSW5PdXRcIjtcbiAgRWFzaW5nUHJlc2V0W1wiZWFzZUluUXVhZFwiXSA9IFwiZWFzZUluUXVhZFwiO1xuICBFYXNpbmdQcmVzZXRbXCJlYXNlSW5DdWJpY1wiXSA9IFwiZWFzZUluQ3ViaWNcIjtcbiAgRWFzaW5nUHJlc2V0W1wiZWFzZUluUXVhcnRcIl0gPSBcImVhc2VJblF1YXJ0XCI7XG4gIEVhc2luZ1ByZXNldFtcImVhc2VJblF1aW50XCJdID0gXCJlYXNlSW5RdWludFwiO1xuICBFYXNpbmdQcmVzZXRbXCJlYXNlSW5TaW5lXCJdID0gXCJlYXNlSW5TaW5lXCI7XG4gIEVhc2luZ1ByZXNldFtcImVhc2VJbkV4cG9cIl0gPSBcImVhc2VJbkV4cG9cIjtcbiAgRWFzaW5nUHJlc2V0W1wiZWFzZUluQ2lyY1wiXSA9IFwiZWFzZUluQ2lyY1wiO1xuICBFYXNpbmdQcmVzZXRbXCJlYXNlT3V0UXVhZFwiXSA9IFwiZWFzZU91dFF1YWRcIjtcbiAgRWFzaW5nUHJlc2V0W1wiZWFzZU91dEN1YmljXCJdID0gXCJlYXNlT3V0Q3ViaWNcIjtcbiAgRWFzaW5nUHJlc2V0W1wiZWFzZU91dFF1YXJ0XCJdID0gXCJlYXNlT3V0UXVhcnRcIjtcbiAgRWFzaW5nUHJlc2V0W1wiZWFzZU91dFF1aW50XCJdID0gXCJlYXNlT3V0UXVpbnRcIjtcbiAgRWFzaW5nUHJlc2V0W1wiZWFzZU91dFNpbmVcIl0gPSBcImVhc2VPdXRTaW5lXCI7XG4gIEVhc2luZ1ByZXNldFtcImVhc2VPdXRFeHBvXCJdID0gXCJlYXNlT3V0RXhwb1wiO1xuICBFYXNpbmdQcmVzZXRbXCJlYXNlT3V0Q2lyY1wiXSA9IFwiZWFzZU91dENpcmNcIjtcbiAgRWFzaW5nUHJlc2V0W1wiZWFzZUluT3V0UXVhZFwiXSA9IFwiZWFzZUluT3V0UXVhZFwiO1xuICBFYXNpbmdQcmVzZXRbXCJlYXNlSW5PdXRDdWJpY1wiXSA9IFwiZWFzZUluT3V0Q3ViaWNcIjtcbiAgRWFzaW5nUHJlc2V0W1wiZWFzZUluT3V0UXVhcnRcIl0gPSBcImVhc2VJbk91dFF1YXJ0XCI7XG4gIEVhc2luZ1ByZXNldFtcImVhc2VJbk91dFF1aW50XCJdID0gXCJlYXNlSW5PdXRRdWludFwiO1xuICBFYXNpbmdQcmVzZXRbXCJlYXNlSW5PdXRTaW5lXCJdID0gXCJlYXNlSW5PdXRTaW5lXCI7XG4gIEVhc2luZ1ByZXNldFtcImVhc2VJbk91dEV4cG9cIl0gPSBcImVhc2VJbk91dEV4cG9cIjtcbiAgRWFzaW5nUHJlc2V0W1wiZWFzZUluT3V0Q2lyY1wiXSA9IFwiZWFzZUluT3V0Q2lyY1wiO1xuICBFYXNpbmdQcmVzZXRbXCJlYXNlSW5CYWNrXCJdID0gXCJlYXNlSW5CYWNrXCI7XG4gIEVhc2luZ1ByZXNldFtcImVhc2VPdXRCYWNrXCJdID0gXCJlYXNlT3V0QmFja1wiO1xuICBFYXNpbmdQcmVzZXRbXCJlYXNlSW5PdXRCYWNrXCJdID0gXCJlYXNlSW5PdXRCYWNrXCI7XG59KShFYXNpbmdQcmVzZXQgfHwgKEVhc2luZ1ByZXNldCA9IHt9KSk7XG5cbi8qKlxyXG4gKiBDcmVhdGVzIGEgdW5pcXVlIGlkIHRvIGRpc3Rpbmd1aXNoIHBhcmFsbGF4IGVsZW1lbnRzLlxyXG4gKi9cbnZhciBpZCA9IDA7XG5mdW5jdGlvbiBjcmVhdGVJZCgpIHtcbiAgKytpZDtcbiAgcmV0dXJuIGlkO1xufVxuXG52YXIgUmVjdCA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoKSB7XG4gIGZ1bmN0aW9uIFJlY3Qob3B0aW9ucykge1xuICAgIHZhciByZWN0ID0gb3B0aW9ucy5lbC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTsgLy8gcmVjdCBpcyBiYXNlZCBvbiB2aWV3cG9ydCAtLSBtdXN0IGFkanVzdCBmb3IgcmVsYXRpdmUgc2Nyb2xsIGNvbnRhaW5lclxuXG4gICAgaWYgKG9wdGlvbnMudmlldy5zY3JvbGxDb250YWluZXIpIHtcbiAgICAgIHZhciBzY3JvbGxSZWN0ID0gb3B0aW9ucy52aWV3LnNjcm9sbENvbnRhaW5lci5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICAgIHJlY3QgPSBfZXh0ZW5kcyh7fSwgcmVjdCwge1xuICAgICAgICB0b3A6IHJlY3QudG9wIC0gc2Nyb2xsUmVjdC50b3AsXG4gICAgICAgIHJpZ2h0OiByZWN0LnJpZ2h0IC0gc2Nyb2xsUmVjdC5sZWZ0LFxuICAgICAgICBib3R0b206IHJlY3QuYm90dG9tIC0gc2Nyb2xsUmVjdC50b3AsXG4gICAgICAgIGxlZnQ6IHJlY3QubGVmdCAtIHNjcm9sbFJlY3QubGVmdFxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgdGhpcy5oZWlnaHQgPSBvcHRpb25zLmVsLm9mZnNldEhlaWdodDtcbiAgICB0aGlzLndpZHRoID0gb3B0aW9ucy5lbC5vZmZzZXRXaWR0aDtcbiAgICB0aGlzLmxlZnQgPSByZWN0LmxlZnQ7XG4gICAgdGhpcy5yaWdodCA9IHJlY3QucmlnaHQ7XG4gICAgdGhpcy50b3AgPSByZWN0LnRvcDtcbiAgICB0aGlzLmJvdHRvbSA9IHJlY3QuYm90dG9tO1xuXG4gICAgaWYgKG9wdGlvbnMucm9vdE1hcmdpbikge1xuICAgICAgdGhpcy5fc2V0UmVjdFdpdGhSb290TWFyZ2luKG9wdGlvbnMucm9vdE1hcmdpbik7XG4gICAgfVxuICB9XG4gIC8qKlxyXG4gICAqIEFwcGx5IHJvb3QgbWFyZ2luIHRvIGFsbCBwcm9wZXJ0aWVzXHJcbiAgICovXG5cblxuICB2YXIgX3Byb3RvID0gUmVjdC5wcm90b3R5cGU7XG5cbiAgX3Byb3RvLl9zZXRSZWN0V2l0aFJvb3RNYXJnaW4gPSBmdW5jdGlvbiBfc2V0UmVjdFdpdGhSb290TWFyZ2luKHJvb3RNYXJnaW4pIHtcbiAgICB2YXIgdG90YWxSb290WSA9IHJvb3RNYXJnaW4udG9wICsgcm9vdE1hcmdpbi5ib3R0b207XG4gICAgdmFyIHRvdGFsUm9vdFggPSByb290TWFyZ2luLmxlZnQgKyByb290TWFyZ2luLnJpZ2h0O1xuICAgIHRoaXMudG9wIC09IHJvb3RNYXJnaW4udG9wO1xuICAgIHRoaXMucmlnaHQgKz0gcm9vdE1hcmdpbi5yaWdodDtcbiAgICB0aGlzLmJvdHRvbSArPSByb290TWFyZ2luLmJvdHRvbTtcbiAgICB0aGlzLmxlZnQgLT0gcm9vdE1hcmdpbi5sZWZ0O1xuICAgIHRoaXMuaGVpZ2h0ICs9IHRvdGFsUm9vdFk7XG4gICAgdGhpcy53aWR0aCArPSB0b3RhbFJvb3RYO1xuICB9O1xuXG4gIHJldHVybiBSZWN0O1xufSgpO1xuXG52YXIgVkFMSURfVU5JVFMgPSBbU2NhbGVVbml0c1snJ10sIFVuaXRzLnB4LCBVbml0c1snJSddLCBVbml0c1sndmgnXSwgVW5pdHNbJ3Z3J10sIFJvdGF0aW9uVW5pdHMuZGVnLCBSb3RhdGlvblVuaXRzLnR1cm4sIFJvdGF0aW9uVW5pdHMucmFkXTtcbi8qKlxyXG4gKiBEZXRlcm1pbmVzIHRoZSB1bml0IG9mIGEgc3RyaW5nIGFuZCBwYXJzZXMgdGhlIHZhbHVlXHJcbiAqL1xuXG5mdW5jdGlvbiBwYXJzZVZhbHVlQW5kVW5pdChzdHIsIGRlZmF1bHRVbml0KSB7XG4gIGlmIChkZWZhdWx0VW5pdCA9PT0gdm9pZCAwKSB7XG4gICAgZGVmYXVsdFVuaXQgPSBVbml0c1snJSddO1xuICB9XG5cbiAgdmFyIG91dCA9IHtcbiAgICB2YWx1ZTogMCxcbiAgICB1bml0OiBkZWZhdWx0VW5pdFxuICB9O1xuICBpZiAodHlwZW9mIHN0ciA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBvdXQ7XG4gIHZhciBpc1ZhbGlkID0gdHlwZW9mIHN0ciA9PT0gJ251bWJlcicgfHwgdHlwZW9mIHN0ciA9PT0gJ3N0cmluZyc7XG5cbiAgaWYgKCFpc1ZhbGlkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIHZhbHVlIHByb3ZpZGVkLiBNdXN0IHByb3ZpZGUgYSB2YWx1ZSBhcyBhIHN0cmluZyBvciBudW1iZXInKTtcbiAgfVxuXG4gIHN0ciA9IFN0cmluZyhzdHIpO1xuICBvdXQudmFsdWUgPSBwYXJzZUZsb2F0KHN0cik7IC8vIEB0cy1pZ25vcmVcblxuICBvdXQudW5pdCA9IHN0ci5tYXRjaCgvW1xcZC5cXC0rXSpcXHMqKC4qKS8pWzFdIHx8IGRlZmF1bHRVbml0OyAvLyBAdHMtZXhwZWN0LWVycm9yXG5cbiAgdmFyIGlzVmFsaWRVbml0ID0gVkFMSURfVU5JVFMuaW5jbHVkZXMob3V0LnVuaXQpO1xuXG4gIGlmICghaXNWYWxpZFVuaXQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgdW5pdCBwcm92aWRlZC4nKTtcbiAgfVxuXG4gIHJldHVybiBvdXQ7XG59XG5cbnZhciBlYXNpbmdQcmVzZXRzID0ge1xuICBlYXNlOiBbMC4yNSwgMC4xLCAwLjI1LCAxLjBdLFxuICBlYXNlSW46IFswLjQyLCAwLjAsIDEuMCwgMS4wXSxcbiAgZWFzZU91dDogWzAuMCwgMC4wLCAwLjU4LCAxLjBdLFxuICBlYXNlSW5PdXQ6IFswLjQyLCAwLjAsIDAuNTgsIDEuMF0sXG5cbiAgLyogRWFzZSBJTiBjdXJ2ZXMgKi9cbiAgZWFzZUluUXVhZDogWzAuNTUsIDAuMDg1LCAwLjY4LCAwLjUzXSxcbiAgZWFzZUluQ3ViaWM6IFswLjU1LCAwLjA1NSwgMC42NzUsIDAuMTldLFxuICBlYXNlSW5RdWFydDogWzAuODk1LCAwLjAzLCAwLjY4NSwgMC4yMl0sXG4gIGVhc2VJblF1aW50OiBbMC43NTUsIDAuMDUsIDAuODU1LCAwLjA2XSxcbiAgZWFzZUluU2luZTogWzAuNDcsIDAuMCwgMC43NDUsIDAuNzE1XSxcbiAgZWFzZUluRXhwbzogWzAuOTUsIDAuMDUsIDAuNzk1LCAwLjAzNV0sXG4gIGVhc2VJbkNpcmM6IFswLjYsIDAuMDQsIDAuOTgsIDAuMzM1XSxcblxuICAvKiBFYXNlIE91dCBDdXJ2ZXMgKi9cbiAgZWFzZU91dFF1YWQ6IFswLjI1LCAwLjQ2LCAwLjQ1LCAwLjk0XSxcbiAgZWFzZU91dEN1YmljOiBbMC4yMTUsIDAuNjEsIDAuMzU1LCAxLjBdLFxuICBlYXNlT3V0UXVhcnQ6IFswLjE2NSwgMC44NCwgMC40NCwgMS4wXSxcbiAgZWFzZU91dFF1aW50OiBbMC4yMywgMS4wLCAwLjMyLCAxLjBdLFxuICBlYXNlT3V0U2luZTogWzAuMzksIDAuNTc1LCAwLjU2NSwgMS4wXSxcbiAgZWFzZU91dEV4cG86IFswLjE5LCAxLjAsIDAuMjIsIDEuMF0sXG4gIGVhc2VPdXRDaXJjOiBbMC4wNzUsIDAuODIsIDAuMTY1LCAxLjBdLFxuXG4gIC8qIEVhc2UgSU4gT3V0IEN1cnZlcyAqL1xuICBlYXNlSW5PdXRRdWFkOiBbMC40NTUsIDAuMDMsIDAuNTE1LCAwLjk1NV0sXG4gIGVhc2VJbk91dEN1YmljOiBbMC42NDUsIDAuMDQ1LCAwLjM1NSwgMS4wXSxcbiAgZWFzZUluT3V0UXVhcnQ6IFswLjc3LCAwLjAsIDAuMTc1LCAxLjBdLFxuICBlYXNlSW5PdXRRdWludDogWzAuODYsIDAuMCwgMC4wNywgMS4wXSxcbiAgZWFzZUluT3V0U2luZTogWzAuNDQ1LCAwLjA1LCAwLjU1LCAwLjk1XSxcbiAgZWFzZUluT3V0RXhwbzogWzEuMCwgMC4wLCAwLjAsIDEuMF0sXG4gIGVhc2VJbk91dENpcmM6IFswLjc4NSwgMC4xMzUsIDAuMTUsIDAuODZdLFxuXG4gIC8qIEVhc2UgQm91bmNlIEN1cnZlcyAqL1xuICBlYXNlSW5CYWNrOiBbMC42LCAtMC4yOCwgMC43MzUsIDAuMDQ1XSxcbiAgZWFzZU91dEJhY2s6IFswLjE3NSwgMC44ODUsIDAuMzIsIDEuMjc1XSxcbiAgZWFzZUluT3V0QmFjazogWzAuNjgsIC0wLjU1LCAwLjI2NSwgMS41NV1cbn07XG5cbmZ1bmN0aW9uIGNyZWF0ZUVhc2luZ0Z1bmN0aW9uKGVhc2luZykge1xuICBpZiAoQXJyYXkuaXNBcnJheShlYXNpbmcpKSB7XG4gICAgcmV0dXJuIGJlemllcihlYXNpbmdbMF0sIGVhc2luZ1sxXSwgZWFzaW5nWzJdLCBlYXNpbmdbM10pO1xuICB9XG5cbiAgaWYgKHR5cGVvZiBlYXNpbmcgPT09ICdzdHJpbmcnICYmIHR5cGVvZiBlYXNpbmdQcmVzZXRzW2Vhc2luZ10gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgdmFyIHBhcmFtcyA9IGVhc2luZ1ByZXNldHNbZWFzaW5nXTtcbiAgICByZXR1cm4gYmV6aWVyKHBhcmFtc1swXSwgcGFyYW1zWzFdLCBwYXJhbXNbMl0sIHBhcmFtc1szXSk7XG4gIH1cblxuICByZXR1cm47XG59XG5cbnZhciBQQVJBTExBWF9FRkZFQ1RTID0gLyojX19QVVJFX18qL09iamVjdC52YWx1ZXMoVmFsaWRDU1NFZmZlY3RzKTtcbnZhciBNQVBfRUZGRUNUX1RPX0RFRkFVTFRfVU5JVCA9IHtcbiAgc3BlZWQ6ICdweCcsXG4gIHRyYW5zbGF0ZVg6ICclJyxcbiAgdHJhbnNsYXRlWTogJyUnLFxuICByb3RhdGU6ICdkZWcnLFxuICByb3RhdGVYOiAnZGVnJyxcbiAgcm90YXRlWTogJ2RlZycsXG4gIHJvdGF0ZVo6ICdkZWcnLFxuICBzY2FsZTogJycsXG4gIHNjYWxlWDogJycsXG4gIHNjYWxlWTogJycsXG4gIHNjYWxlWjogJycsXG4gIG9wYWNpdHk6ICcnXG59O1xuLyoqXHJcbiAqIFRha2VzIGEgcGFyYWxsYXggZWxlbWVudCBlZmZlY3RzIGFuZCBwYXJzZXMgdGhlIHByb3BlcnRpZXMgdG8gZ2V0IHRoZSBzdGFydCBhbmQgZW5kIHZhbHVlcyBhbmQgdW5pdHMuXHJcbiAqL1xuXG5mdW5jdGlvbiBwYXJzZUVsZW1lbnRUcmFuc2l0aW9uRWZmZWN0cyhwcm9wcywgc2Nyb2xsQXhpcykge1xuICB2YXIgcGFyc2VkRWZmZWN0cyA9IHt9O1xuICBQQVJBTExBWF9FRkZFQ1RTLmZvckVhY2goZnVuY3Rpb24gKGtleSkge1xuICAgIHZhciBkZWZhdWx0VmFsdWUgPSBNQVBfRUZGRUNUX1RPX0RFRkFVTFRfVU5JVFtrZXldOyAvLyBJZiB0aGUgcHJvdmlkZWQgdHlwZSBpcyBhIG51bWJlciwgdGhpcyBtdXN0IGJlIHRoZSBzcGVlZCBwcm9wXG4gICAgLy8gaW4gd2hpY2ggY2FzZSB3ZSBuZWVkIHRvIGNvbnN0cnVjdCB0aGUgcHJvcGVyIHRyYW5zbGF0ZSBjb25maWdcblxuICAgIGlmICh0eXBlb2YgKHByb3BzID09IG51bGwgPyB2b2lkIDAgOiBwcm9wc1trZXldKSA9PT0gJ251bWJlcicpIHtcbiAgICAgIHZhciB2YWx1ZSA9IHByb3BzID09IG51bGwgPyB2b2lkIDAgOiBwcm9wc1trZXldO1xuICAgICAgdmFyIHN0YXJ0U3BlZWQgPSAodmFsdWUgfHwgMCkgKiAxMCArIFwicHhcIjtcbiAgICAgIHZhciBlbmRTcGVlZCA9ICh2YWx1ZSB8fCAwKSAqIC0xMCArIFwicHhcIjtcbiAgICAgIHZhciBzdGFydFBhcnNlZCA9IHBhcnNlVmFsdWVBbmRVbml0KHN0YXJ0U3BlZWQpO1xuICAgICAgdmFyIGVuZFBhcnNlZCA9IHBhcnNlVmFsdWVBbmRVbml0KGVuZFNwZWVkKTtcbiAgICAgIHZhciBzcGVlZENvbmZpZyA9IHtcbiAgICAgICAgc3RhcnQ6IHN0YXJ0UGFyc2VkLnZhbHVlLFxuICAgICAgICBlbmQ6IGVuZFBhcnNlZC52YWx1ZSxcbiAgICAgICAgdW5pdDogc3RhcnRQYXJzZWQudW5pdFxuICAgICAgfTsgLy8gTWFudWFsbHkgc2V0IHRyYW5zbGF0ZSB5IHZhbHVlXG5cbiAgICAgIGlmIChzY3JvbGxBeGlzID09PSBTY3JvbGxBeGlzLnZlcnRpY2FsKSB7XG4gICAgICAgIHBhcnNlZEVmZmVjdHMudHJhbnNsYXRlWSA9IHNwZWVkQ29uZmlnO1xuICAgICAgfSAvLyBNYW51YWxseSBzZXQgdHJhbnNsYXRlIHkgdmFsdWVcblxuXG4gICAgICBpZiAoc2Nyb2xsQXhpcyA9PT0gU2Nyb2xsQXhpcy5ob3Jpem9udGFsKSB7XG4gICAgICAgIHBhcnNlZEVmZmVjdHMudHJhbnNsYXRlWCA9IHNwZWVkQ29uZmlnO1xuICAgICAgfVxuICAgIH0gLy8gVGhlIHJlc3QgYXJlIHN0YW5kYXJkIGVmZmVjdCBiZWluZyBwYXJzZWRcblxuXG4gICAgaWYgKEFycmF5LmlzQXJyYXkocHJvcHMgPT0gbnVsbCA/IHZvaWQgMCA6IHByb3BzW2tleV0pKSB7XG4gICAgICB2YXIgX3ZhbHVlID0gcHJvcHMgPT0gbnVsbCA/IHZvaWQgMCA6IHByb3BzW2tleV07XG5cbiAgICAgIGlmICh0eXBlb2YgX3ZhbHVlWzBdICE9PSAndW5kZWZpbmVkJyAmJiB0eXBlb2YgX3ZhbHVlWzFdICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICB2YXIgX3N0YXJ0UGFyc2VkID0gcGFyc2VWYWx1ZUFuZFVuaXQoX3ZhbHVlID09IG51bGwgPyB2b2lkIDAgOiBfdmFsdWVbMF0sIGRlZmF1bHRWYWx1ZSk7XG5cbiAgICAgICAgdmFyIF9lbmRQYXJzZWQgPSBwYXJzZVZhbHVlQW5kVW5pdChfdmFsdWUgPT0gbnVsbCA/IHZvaWQgMCA6IF92YWx1ZVsxXSwgZGVmYXVsdFZhbHVlKTtcblxuICAgICAgICB2YXIgZWFzaW5nID0gY3JlYXRlRWFzaW5nRnVuY3Rpb24oX3ZhbHVlID09IG51bGwgPyB2b2lkIDAgOiBfdmFsdWVbMl0pO1xuICAgICAgICBwYXJzZWRFZmZlY3RzW2tleV0gPSB7XG4gICAgICAgICAgc3RhcnQ6IF9zdGFydFBhcnNlZC52YWx1ZSxcbiAgICAgICAgICBlbmQ6IF9lbmRQYXJzZWQudmFsdWUsXG4gICAgICAgICAgdW5pdDogX3N0YXJ0UGFyc2VkLnVuaXQsXG4gICAgICAgICAgZWFzaW5nOiBlYXNpbmdcbiAgICAgICAgfTtcblxuICAgICAgICBpZiAoX3N0YXJ0UGFyc2VkLnVuaXQgIT09IF9lbmRQYXJzZWQudW5pdCkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignTXVzdCBwcm92aWRlIG1hdGNoaW5nIHVuaXRzIGZvciB0aGUgbWluIGFuZCBtYXggb2Zmc2V0IHZhbHVlcyBvZiBlYWNoIGF4aXMuJyk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH0pO1xuICByZXR1cm4gcGFyc2VkRWZmZWN0cztcbn1cblxuLyoqXHJcbiAqIFJldHVybnMgdGhlIHBlcmNlbnQgKDAgLSAxMDApIG1vdmVkIGJhc2VkIG9uIHBvc2l0aW9uIGluIHRoZSB2aWV3cG9ydFxyXG4gKi9cbmZ1bmN0aW9uIGdldFByb2dyZXNzQW1vdW50KFxuLypcclxuICogVGhlIHN0YXJ0IHZhbHVlIGZyb20gY2FjaGVcclxuICovXG5zdGFydCxcbi8qXHJcbiAqIHRvdGFsIGRpc3QgdGhlIGVsZW1lbnQgaGFzIHRvIG1vdmUgdG8gYmUgMTAwJSBjb21wbGV0ZSAodmlldyB3aWR0aC9oZWlnaHQgKyBlbGVtZW50IHdpZHRoL2hlaWdodClcclxuICovXG50b3RhbERpc3QsXG4vKlxyXG4gKiBDdXJyZW50IHNjcm9sbCB2YWx1ZVxyXG4gKi9cbmN1cnJlbnRTY3JvbGwsXG4vKlxyXG4gKiBhbiBvcHRpb25hbCBlYXNpbmcgZnVuY3Rpb24gdG8gYXBwbHlcclxuICovXG5lYXNpbmcpIHtcbiAgLy8gYWRqdXN0IGNhY2hlZCB2YWx1ZVxuICB2YXIgc3RhcnRBZGp1c3RlZFNjcm9sbCA9IGN1cnJlbnRTY3JvbGwgLSBzdGFydDsgLy8gQW1vdW50IHRoZSBlbGVtZW50IGhhcyBtb3ZlZCBiYXNlZCBvbiBjdXJyZW50IGFuZCB0b3RhbCBkaXN0YW5jZSB0byBtb3ZlXG5cbiAgdmFyIGFtb3VudCA9IHN0YXJ0QWRqdXN0ZWRTY3JvbGwgLyB0b3RhbERpc3Q7IC8vIEFwcGx5IGJlemllciBlYXNpbmcgaWYgcHJvdmlkZWRcblxuICBpZiAoZWFzaW5nKSB7XG4gICAgYW1vdW50ID0gZWFzaW5nKGFtb3VudCk7XG4gIH1cblxuICByZXR1cm4gYW1vdW50O1xufVxuXG4vKipcclxuICogVGFrZXMgdHdvIHZhbHVlcyAoc3RhcnQsIGVuZCkgYW5kIHJldHVybnMgd2hldGhlciB0aGUgY3VycmVudCBzY3JvbGwgaXMgd2l0aGluIHJhbmdlXHJcbiAqIEBwYXJhbSB7bnVtYmVyfSBzdGFydCAtIHN0YXJ0IG9mIHNjcm9sbCAoeC95KVxyXG4gKiBAcGFyYW0ge251bWJlcn0gZW5kIC0gZW5kIG9mIHNjcm9sbCAoeC95KVxyXG4gKiBAcGFyYW0ge251bWJlcn0gc2Nyb2xsIC0gY3VycmVudCBzY3JvbGwgKHgveSlcclxuICogQHJldHVybiB7Ym9vbGVhbn0gaXNJblZpZXdcclxuICovXG5mdW5jdGlvbiBpc0VsZW1lbnRJblZpZXcoc3RhcnQsIGVuZCwgc2Nyb2xsKSB7XG4gIHZhciBpc0luVmlldyA9IHNjcm9sbCA+PSBzdGFydCAmJiBzY3JvbGwgPD0gZW5kO1xuICByZXR1cm4gaXNJblZpZXc7XG59XG5cbi8vIFNjYWxlIGJldHdlZW4gQUtBIG5vcm1hbGl6ZVxuZnVuY3Rpb24gc2NhbGVCZXR3ZWVuKHZhbHVlLCBuZXdNaW4sIG5ld01heCwgb2xkTWluLCBvbGRNYXgpIHtcbiAgcmV0dXJuIChuZXdNYXggLSBuZXdNaW4pICogKHZhbHVlIC0gb2xkTWluKSAvIChvbGRNYXggLSBvbGRNaW4pICsgbmV3TWluO1xufVxuXG4vKipcclxuICogU2NhbGVzIGEgc3RhcnQgYW5kIGVuZCB2YWx1ZSBvZiBhbiBlZmZlY3QgYmFzZWQgb24gcGVyY2VudCBtb3ZlZCBhbmQgZWFzaW5nIGZ1bmN0aW9uXHJcbiAqL1xuXG5mdW5jdGlvbiBzY2FsZUVmZmVjdEJ5UHJvZ3Jlc3MoZWZmZWN0LCBwcm9ncmVzcykge1xuICB2YXIgdmFsdWUgPSBzY2FsZUJldHdlZW4odHlwZW9mIGVmZmVjdC5lYXNpbmcgPT09ICdmdW5jdGlvbicgPyBlZmZlY3QuZWFzaW5nKHByb2dyZXNzKSA6IHByb2dyZXNzLCAoZWZmZWN0ID09IG51bGwgPyB2b2lkIDAgOiBlZmZlY3Quc3RhcnQpIHx8IDAsIChlZmZlY3QgPT0gbnVsbCA/IHZvaWQgMCA6IGVmZmVjdC5lbmQpIHx8IDAsIDAsIDEpO1xuICByZXR1cm4ge1xuICAgIHZhbHVlOiB2YWx1ZSxcbiAgICB1bml0OiBlZmZlY3QgPT0gbnVsbCA/IHZvaWQgMCA6IGVmZmVjdC51bml0XG4gIH07XG59XG5cbnZhciBUUkFOU0ZPUk1fRUZGRUNUUyA9IC8qI19fUFVSRV9fKi9PYmplY3QudmFsdWVzKFZhbGlkQ1NTRWZmZWN0cykuZmlsdGVyKGZ1bmN0aW9uICh2KSB7XG4gIHJldHVybiB2ICE9PSAnb3BhY2l0eSc7XG59KTtcbmZ1bmN0aW9uIHNldFdpbGxDaGFuZ2VTdHlsZXMoZWwsIGVmZmVjdHMpIHtcbiAgdmFyIGtleXMgPSBPYmplY3Qua2V5cyhlZmZlY3RzKTtcbiAgdmFyIGhhc09wYWNpdHkgPSBrZXlzLmluY2x1ZGVzKCdvcGFjaXR5Jyk7XG4gIHZhciB3aWxsQ2hhbmdlID0gXCJ0cmFuc2Zvcm1cIiArIChoYXNPcGFjaXR5ID8gJyxvcGFjaXR5JyA6ICcnKTtcbiAgZWwuc3R5bGUud2lsbENoYW5nZSA9IHdpbGxDaGFuZ2U7XG59XG5mdW5jdGlvbiBzZXRFbGVtZW50U3R5bGVzKGVmZmVjdHMsIHByb2dyZXNzLCBlbCkge1xuICBpZiAoIWVsKSByZXR1cm47XG4gIHZhciB0cmFuc2Zvcm0gPSBnZXRUcmFuc2Zvcm1TdHlsZXMoZWZmZWN0cywgcHJvZ3Jlc3MpO1xuICB2YXIgb3BhY2l0eSA9IGdldE9wYWNpdHlTdHlsZXMoZWZmZWN0cywgcHJvZ3Jlc3MpO1xuICBlbC5zdHlsZS50cmFuc2Zvcm0gPSB0cmFuc2Zvcm07XG4gIGVsLnN0eWxlLm9wYWNpdHkgPSBvcGFjaXR5O1xufVxuZnVuY3Rpb24gZ2V0T3BhY2l0eVN0eWxlcyhlZmZlY3RzLCBwcm9ncmVzcykge1xuICB2YXIgc2NhbGVkT3BhY2l0eSA9IGVmZmVjdHNbJ29wYWNpdHknXSAmJiBzY2FsZUVmZmVjdEJ5UHJvZ3Jlc3MoZWZmZWN0c1snb3BhY2l0eSddLCBwcm9ncmVzcyk7XG5cbiAgaWYgKHR5cGVvZiBzY2FsZWRPcGFjaXR5ID09PSAndW5kZWZpbmVkJyB8fCB0eXBlb2Ygc2NhbGVkT3BhY2l0eS52YWx1ZSA9PT0gJ3VuZGVmaW5lZCcgfHwgdHlwZW9mIHNjYWxlZE9wYWNpdHkudW5pdCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICByZXR1cm4gJyc7XG4gIH1cblxuICB2YXIgc3R5bGVTdHIgPSBcIlwiICsgc2NhbGVkT3BhY2l0eS52YWx1ZTtcbiAgcmV0dXJuIHN0eWxlU3RyO1xufVxuZnVuY3Rpb24gZ2V0VHJhbnNmb3JtU3R5bGVzKGVmZmVjdHMsIHByb2dyZXNzKSB7XG4gIHZhciB0cmFuc2Zvcm0gPSBUUkFOU0ZPUk1fRUZGRUNUUy5yZWR1Y2UoZnVuY3Rpb24gKGFjYywga2V5KSB7XG4gICAgdmFyIHNjYWxlZEVmZmVjdCA9IC8vIEB0cy1leHBlY3QtZXJyb3JcbiAgICBlZmZlY3RzW2tleV0gJiYgc2NhbGVFZmZlY3RCeVByb2dyZXNzKGVmZmVjdHNba2V5XSwgcHJvZ3Jlc3MpO1xuXG4gICAgaWYgKHR5cGVvZiBzY2FsZWRFZmZlY3QgPT09ICd1bmRlZmluZWQnIHx8IHR5cGVvZiBzY2FsZWRFZmZlY3QudmFsdWUgPT09ICd1bmRlZmluZWQnIHx8IHR5cGVvZiBzY2FsZWRFZmZlY3QudW5pdCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIHJldHVybiBhY2M7XG4gICAgfVxuXG4gICAgdmFyIHN0eWxlU3RyID0ga2V5ICsgXCIoXCIgKyBzY2FsZWRFZmZlY3QudmFsdWUgKyBzY2FsZWRFZmZlY3QudW5pdCArIFwiKVwiO1xuICAgIHJldHVybiBhY2MgKyBzdHlsZVN0cjtcbiAgfSwgJycpO1xuICByZXR1cm4gdHJhbnNmb3JtO1xufVxuLyoqXHJcbiAqIFRha2VzIGEgcGFyYWxsYXggZWxlbWVudCBhbmQgcmVtb3ZlcyBwYXJhbGxheCBvZmZzZXQgc3R5bGVzLlxyXG4gKiBAcGFyYW0ge29iamVjdH0gZWxlbWVudFxyXG4gKi9cblxuZnVuY3Rpb24gcmVzZXRTdHlsZXMoZWxlbWVudCkge1xuICB2YXIgZWwgPSBlbGVtZW50LmVsO1xuICBpZiAoIWVsKSByZXR1cm47XG4gIGVsLnN0eWxlLnRyYW5zZm9ybSA9ICcnO1xuICBlbC5zdHlsZS5vcGFjaXR5ID0gJyc7XG59XG5cbmZ1bmN0aW9uIGNyZWF0ZUxpbWl0c0ZvclJlbGF0aXZlRWxlbWVudHMocmVjdCwgdmlldywgc2Nyb2xsLCBzaG91bGRBbHdheXNDb21wbGV0ZUFuaW1hdGlvbikge1xuICB2YXIgc3RhcnRZID0gcmVjdC50b3AgLSB2aWV3LmhlaWdodDtcbiAgdmFyIHN0YXJ0WCA9IHJlY3QubGVmdCAtIHZpZXcud2lkdGg7XG4gIHZhciBlbmRZID0gcmVjdC5ib3R0b207XG4gIHZhciBlbmRYID0gcmVjdC5yaWdodDsgLy8gYWRkIHNjcm9sbFxuXG4gIHN0YXJ0WCArPSBzY3JvbGwueDtcbiAgZW5kWCArPSBzY3JvbGwueDtcbiAgc3RhcnRZICs9IHNjcm9sbC55O1xuICBlbmRZICs9IHNjcm9sbC55O1xuXG4gIGlmIChzaG91bGRBbHdheXNDb21wbGV0ZUFuaW1hdGlvbikge1xuICAgIGlmIChzY3JvbGwueSArIHJlY3QudG9wIDwgdmlldy5oZWlnaHQpIHtcbiAgICAgIHN0YXJ0WSA9IDA7XG4gICAgfVxuXG4gICAgaWYgKHNjcm9sbC54ICsgcmVjdC5sZWZ0IDwgdmlldy53aWR0aCkge1xuICAgICAgc3RhcnRYID0gMDtcbiAgICB9XG5cbiAgICBpZiAoZW5kWSA+IHZpZXcuc2Nyb2xsSGVpZ2h0IC0gdmlldy5oZWlnaHQpIHtcbiAgICAgIGVuZFkgPSB2aWV3LnNjcm9sbEhlaWdodCAtIHZpZXcuaGVpZ2h0O1xuICAgIH1cblxuICAgIGlmIChlbmRYID4gdmlldy5zY3JvbGxXaWR0aCAtIHZpZXcud2lkdGgpIHtcbiAgICAgIGVuZFggPSB2aWV3LnNjcm9sbFdpZHRoIC0gdmlldy53aWR0aDtcbiAgICB9XG4gIH1cblxuICB2YXIgbGltaXRzID0gbmV3IExpbWl0cyh7XG4gICAgc3RhcnRYOiBzdGFydFgsXG4gICAgc3RhcnRZOiBzdGFydFksXG4gICAgZW5kWDogZW5kWCxcbiAgICBlbmRZOiBlbmRZXG4gIH0pO1xuICByZXR1cm4gbGltaXRzO1xufVxuXG5mdW5jdGlvbiBnZXRUcmFuc2xhdGVTY2FsYXIoc3RhcnRUcmFuc2xhdGVQeCwgZW5kVHJhbnNsYXRlUHgsIHRvdGFsRGlzdCkge1xuICB2YXIgc2xvdyA9IGVuZFRyYW5zbGF0ZVB4ID4gc3RhcnRUcmFuc2xhdGVQeDsgLy8gY2FsY3VsYXRpbmcgbmVjZXNzYXJ5IHNjYWxlIHRvIGluY3JlYXNlIHRyYW5zbGF0aW9uc1xuXG4gIHZhciB0b3RhbEFic09mZiA9IChNYXRoLmFicyhzdGFydFRyYW5zbGF0ZVB4KSArIE1hdGguYWJzKGVuZFRyYW5zbGF0ZVB4KSkgKiAoc2xvdyA/IC0xIDogMSk7XG4gIHZhciB0b3RhbERpc3RUcnVlID0gdG90YWxEaXN0ICsgdG90YWxBYnNPZmY7IC8vIERldGVybWluZSBtdWx0aXBsZSB0byBzY2FsZSBieSwgb25seSB2YWx1ZXMgZ3JlYXRlciB0aGFuIDFcblxuICB2YXIgc2NhbGUgPSBNYXRoLm1heCh0b3RhbERpc3QgLyB0b3RhbERpc3RUcnVlLCAxKTtcbiAgcmV0dXJuIHNjYWxlO1xufVxuXG4vKipcclxuICogUmV0dXJuIHRoZSBzdGFydCBhbmQgZW5kIHBpeGVsIHZhbHVlcyBmb3IgYW4gZWxlbWVudHMgdHJhbnNsYXRpb25zXHJcbiAqL1xuZnVuY3Rpb24gZ2V0U3RhcnRFbmRWYWx1ZUluUHgodHJhbnNsYXRlLCBlbGVtZW50U2l6ZSkge1xuICB2YXIgc3RhcnQgPSB0cmFuc2xhdGUuc3RhcnQsXG4gICAgICBlbmQgPSB0cmFuc2xhdGUuZW5kLFxuICAgICAgdW5pdCA9IHRyYW5zbGF0ZS51bml0O1xuXG4gIGlmICh1bml0ID09PSAnJScpIHtcbiAgICB2YXIgc2NhbGUgPSBlbGVtZW50U2l6ZSAvIDEwMDtcbiAgICBzdGFydCA9IHN0YXJ0ICogc2NhbGU7XG4gICAgZW5kID0gZW5kICogc2NhbGU7XG4gIH1cblxuICBpZiAodW5pdCA9PT0gJ3Z3Jykge1xuICAgIHZhciBzdGFydFNjYWxlID0gc3RhcnQgLyAxMDA7XG4gICAgdmFyIGVuZFNjYWxlID0gZW5kIC8gMTAwO1xuICAgIHN0YXJ0ID0gd2luZG93LmlubmVyV2lkdGggKiBzdGFydFNjYWxlO1xuICAgIGVuZCA9IHdpbmRvdy5pbm5lcldpZHRoICogZW5kU2NhbGU7XG4gIH1cblxuICBpZiAodW5pdCA9PT0gJ3ZoJykge1xuICAgIHZhciBfc3RhcnRTY2FsZSA9IHN0YXJ0IC8gMTAwO1xuXG4gICAgdmFyIF9lbmRTY2FsZSA9IGVuZCAvIDEwMDtcblxuICAgIHN0YXJ0ID0gd2luZG93LmlubmVySGVpZ2h0ICogX3N0YXJ0U2NhbGU7XG4gICAgZW5kID0gd2luZG93LmlubmVySGVpZ2h0ICogX2VuZFNjYWxlO1xuICB9XG5cbiAgcmV0dXJuIHtcbiAgICBzdGFydDogc3RhcnQsXG4gICAgZW5kOiBlbmRcbiAgfTtcbn1cblxudmFyIERFRkFVTFRfVkFMVUUgPSB7XG4gIHN0YXJ0OiAwLFxuICBlbmQ6IDAsXG4gIHVuaXQ6ICcnXG59O1xuZnVuY3Rpb24gY3JlYXRlTGltaXRzV2l0aFRyYW5zbGF0aW9uc0ZvclJlbGF0aXZlRWxlbWVudHMocmVjdCwgdmlldywgZWZmZWN0cywgc2Nyb2xsLCBzY3JvbGxBeGlzLCBzaG91bGRBbHdheXNDb21wbGV0ZUFuaW1hdGlvbikge1xuICAvLyBnZXQgc3RhcnQgYW5kIGVuZCBhY2NvdW50aW5nIGZvciBwZXJjZW50IGVmZmVjdHNcbiAgdmFyIHRyYW5zbGF0ZVggPSBlZmZlY3RzLnRyYW5zbGF0ZVggfHwgREVGQVVMVF9WQUxVRTtcbiAgdmFyIHRyYW5zbGF0ZVkgPSBlZmZlY3RzLnRyYW5zbGF0ZVkgfHwgREVGQVVMVF9WQUxVRTtcblxuICB2YXIgX2dldFN0YXJ0RW5kVmFsdWVJblB4ID0gZ2V0U3RhcnRFbmRWYWx1ZUluUHgodHJhbnNsYXRlWCwgcmVjdC53aWR0aCksXG4gICAgICBzdGFydFRyYW5zbGF0ZVhQeCA9IF9nZXRTdGFydEVuZFZhbHVlSW5QeC5zdGFydCxcbiAgICAgIGVuZFRyYW5zbGF0ZVhQeCA9IF9nZXRTdGFydEVuZFZhbHVlSW5QeC5lbmQ7XG5cbiAgdmFyIF9nZXRTdGFydEVuZFZhbHVlSW5QeDIgPSBnZXRTdGFydEVuZFZhbHVlSW5QeCh0cmFuc2xhdGVZLCByZWN0LmhlaWdodCksXG4gICAgICBzdGFydFRyYW5zbGF0ZVlQeCA9IF9nZXRTdGFydEVuZFZhbHVlSW5QeDIuc3RhcnQsXG4gICAgICBlbmRUcmFuc2xhdGVZUHggPSBfZ2V0U3RhcnRFbmRWYWx1ZUluUHgyLmVuZDsgLy8gZGVmYXVsdCBzdGFydGluZyB2YWx1ZXNcblxuXG4gIHZhciBzdGFydFkgPSByZWN0LnRvcCAtIHZpZXcuaGVpZ2h0O1xuICB2YXIgc3RhcnRYID0gcmVjdC5sZWZ0IC0gdmlldy53aWR0aDtcbiAgdmFyIGVuZFkgPSByZWN0LmJvdHRvbTtcbiAgdmFyIGVuZFggPSByZWN0LnJpZ2h0O1xuICB2YXIgc3RhcnRNdWx0aXBsaWVyWSA9IDE7XG4gIHZhciBlbmRNdWx0aXBsaWVyWSA9IDE7XG5cbiAgaWYgKHNjcm9sbEF4aXMgPT09IFNjcm9sbEF4aXMudmVydGljYWwpIHtcbiAgICBzdGFydE11bHRpcGxpZXJZID0gZ2V0VHJhbnNsYXRlU2NhbGFyKHN0YXJ0VHJhbnNsYXRlWVB4LCBlbmRUcmFuc2xhdGVZUHgsIHZpZXcuaGVpZ2h0ICsgcmVjdC5oZWlnaHQpO1xuICAgIGVuZE11bHRpcGxpZXJZID0gc3RhcnRNdWx0aXBsaWVyWTtcbiAgfVxuXG4gIHZhciBzdGFydE11bHRpcGxpZXJYID0gMTtcbiAgdmFyIGVuZE11bHRpcGxpZXJYID0gMTtcblxuICBpZiAoc2Nyb2xsQXhpcyA9PT0gU2Nyb2xsQXhpcy5ob3Jpem9udGFsKSB7XG4gICAgc3RhcnRNdWx0aXBsaWVyWCA9IGdldFRyYW5zbGF0ZVNjYWxhcihzdGFydFRyYW5zbGF0ZVhQeCwgZW5kVHJhbnNsYXRlWFB4LCB2aWV3LndpZHRoICsgcmVjdC53aWR0aCk7XG4gICAgZW5kTXVsdGlwbGllclggPSBzdGFydE11bHRpcGxpZXJYO1xuICB9IC8vIEFwcGx5IHRoZSBzY2FsZSB0byBpbml0aWFsIHZhbHVlc1xuXG5cbiAgaWYgKHN0YXJ0VHJhbnNsYXRlWVB4IDwgMCkge1xuICAgIHN0YXJ0WSA9IHN0YXJ0WSArIHN0YXJ0VHJhbnNsYXRlWVB4ICogc3RhcnRNdWx0aXBsaWVyWTtcbiAgfVxuXG4gIGlmIChlbmRUcmFuc2xhdGVZUHggPiAwKSB7XG4gICAgZW5kWSA9IGVuZFkgKyBlbmRUcmFuc2xhdGVZUHggKiBlbmRNdWx0aXBsaWVyWTtcbiAgfVxuXG4gIGlmIChzdGFydFRyYW5zbGF0ZVhQeCA8IDApIHtcbiAgICBzdGFydFggPSBzdGFydFggKyBzdGFydFRyYW5zbGF0ZVhQeCAqIHN0YXJ0TXVsdGlwbGllclg7XG4gIH1cblxuICBpZiAoZW5kVHJhbnNsYXRlWFB4ID4gMCkge1xuICAgIGVuZFggPSBlbmRYICsgZW5kVHJhbnNsYXRlWFB4ICogZW5kTXVsdGlwbGllclg7XG4gIH0gLy8gYWRkIHNjcm9sbFxuXG5cbiAgc3RhcnRYICs9IHNjcm9sbC54O1xuICBlbmRYICs9IHNjcm9sbC54O1xuICBzdGFydFkgKz0gc2Nyb2xsLnk7XG4gIGVuZFkgKz0gc2Nyb2xsLnk7IC8vIE5PVEU6IHBsZWFzZSByZWZhY3RvciBhbmQgaXNvbGF0ZSB0aGlzIDooXG5cbiAgaWYgKHNob3VsZEFsd2F5c0NvbXBsZXRlQW5pbWF0aW9uKSB7XG4gICAgdmFyIHRvcEJlZ2luc0luVmlldyA9IHNjcm9sbC55ICsgcmVjdC50b3AgPCB2aWV3LmhlaWdodDtcbiAgICB2YXIgbGVmdEJlZ2luc0luVmlldyA9IHNjcm9sbC54ICsgcmVjdC5sZWZ0IDwgdmlldy53aWR0aDtcbiAgICB2YXIgYm90dG9tRW5kc0luVmlldyA9IHNjcm9sbC55ICsgcmVjdC5ib3R0b20gPiB2aWV3LnNjcm9sbEhlaWdodCAtIHZpZXcuaGVpZ2h0O1xuICAgIHZhciByaWdodEVuZHNJblZpZXcgPSBzY3JvbGwueCArIHJlY3QucmlnaHQgPiB2aWV3LnNjcm9sbFdpZHRoIC0gdmlldy5oZWlnaHQ7XG5cbiAgICBpZiAodG9wQmVnaW5zSW5WaWV3ICYmIGJvdHRvbUVuZHNJblZpZXcpIHtcbiAgICAgIHN0YXJ0TXVsdGlwbGllclkgPSAxO1xuICAgICAgZW5kTXVsdGlwbGllclkgPSAxO1xuICAgICAgc3RhcnRZID0gMDtcbiAgICAgIGVuZFkgPSB2aWV3LnNjcm9sbEhlaWdodCAtIHZpZXcuaGVpZ2h0O1xuICAgIH1cblxuICAgIGlmIChsZWZ0QmVnaW5zSW5WaWV3ICYmIHJpZ2h0RW5kc0luVmlldykge1xuICAgICAgc3RhcnRNdWx0aXBsaWVyWCA9IDE7XG4gICAgICBlbmRNdWx0aXBsaWVyWCA9IDE7XG4gICAgICBzdGFydFggPSAwO1xuICAgICAgZW5kWCA9IHZpZXcuc2Nyb2xsV2lkdGggLSB2aWV3LndpZHRoO1xuICAgIH1cblxuICAgIGlmICghdG9wQmVnaW5zSW5WaWV3ICYmIGJvdHRvbUVuZHNJblZpZXcpIHtcbiAgICAgIHN0YXJ0WSA9IHJlY3QudG9wIC0gdmlldy5oZWlnaHQgKyBzY3JvbGwueTtcbiAgICAgIGVuZFkgPSB2aWV3LnNjcm9sbEhlaWdodCAtIHZpZXcuaGVpZ2h0O1xuICAgICAgdmFyIHRvdGFsRGlzdCA9IGVuZFkgLSBzdGFydFk7XG4gICAgICBzdGFydE11bHRpcGxpZXJZID0gZ2V0VHJhbnNsYXRlU2NhbGFyKHN0YXJ0VHJhbnNsYXRlWVB4LCBlbmRUcmFuc2xhdGVZUHgsIHRvdGFsRGlzdCk7XG4gICAgICBlbmRNdWx0aXBsaWVyWSA9IDE7XG5cbiAgICAgIGlmIChzdGFydFRyYW5zbGF0ZVlQeCA8IDApIHtcbiAgICAgICAgc3RhcnRZID0gc3RhcnRZICsgc3RhcnRUcmFuc2xhdGVZUHggKiBzdGFydE11bHRpcGxpZXJZO1xuICAgICAgfVxuICAgIH1cblxuICAgIGlmICghbGVmdEJlZ2luc0luVmlldyAmJiByaWdodEVuZHNJblZpZXcpIHtcbiAgICAgIHN0YXJ0WCA9IHJlY3QubGVmdCAtIHZpZXcud2lkdGggKyBzY3JvbGwueDtcbiAgICAgIGVuZFggPSB2aWV3LnNjcm9sbFdpZHRoIC0gdmlldy53aWR0aDtcblxuICAgICAgdmFyIF90b3RhbERpc3QgPSBlbmRYIC0gc3RhcnRYO1xuXG4gICAgICBzdGFydE11bHRpcGxpZXJYID0gZ2V0VHJhbnNsYXRlU2NhbGFyKHN0YXJ0VHJhbnNsYXRlWFB4LCBlbmRUcmFuc2xhdGVYUHgsIF90b3RhbERpc3QpO1xuICAgICAgZW5kTXVsdGlwbGllclggPSAxO1xuXG4gICAgICBpZiAoc3RhcnRUcmFuc2xhdGVYUHggPCAwKSB7XG4gICAgICAgIHN0YXJ0WCA9IHN0YXJ0WCArIHN0YXJ0VHJhbnNsYXRlWFB4ICogc3RhcnRNdWx0aXBsaWVyWDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAodG9wQmVnaW5zSW5WaWV3ICYmICFib3R0b21FbmRzSW5WaWV3KSB7XG4gICAgICBzdGFydFkgPSAwO1xuICAgICAgZW5kWSA9IHJlY3QuYm90dG9tICsgc2Nyb2xsLnk7XG5cbiAgICAgIHZhciBfdG90YWxEaXN0MiA9IGVuZFkgLSBzdGFydFk7XG5cbiAgICAgIHN0YXJ0TXVsdGlwbGllclkgPSAxO1xuICAgICAgZW5kTXVsdGlwbGllclkgPSBnZXRUcmFuc2xhdGVTY2FsYXIoc3RhcnRUcmFuc2xhdGVZUHgsIGVuZFRyYW5zbGF0ZVlQeCwgX3RvdGFsRGlzdDIpO1xuXG4gICAgICBpZiAoZW5kVHJhbnNsYXRlWVB4ID4gMCkge1xuICAgICAgICBlbmRZID0gZW5kWSArIGVuZFRyYW5zbGF0ZVlQeCAqIGVuZE11bHRpcGxpZXJZO1xuICAgICAgfVxuICAgIH1cblxuICAgIGlmIChsZWZ0QmVnaW5zSW5WaWV3ICYmICFyaWdodEVuZHNJblZpZXcpIHtcbiAgICAgIHN0YXJ0WCA9IDA7XG4gICAgICBlbmRYID0gcmVjdC5yaWdodCArIHNjcm9sbC54O1xuXG4gICAgICB2YXIgX3RvdGFsRGlzdDMgPSBlbmRYIC0gc3RhcnRYO1xuXG4gICAgICBzdGFydE11bHRpcGxpZXJYID0gMTtcbiAgICAgIGVuZE11bHRpcGxpZXJYID0gZ2V0VHJhbnNsYXRlU2NhbGFyKHN0YXJ0VHJhbnNsYXRlWFB4LCBlbmRUcmFuc2xhdGVYUHgsIF90b3RhbERpc3QzKTtcblxuICAgICAgaWYgKGVuZFRyYW5zbGF0ZVhQeCA+IDApIHtcbiAgICAgICAgZW5kWCA9IGVuZFggKyBlbmRUcmFuc2xhdGVYUHggKiBlbmRNdWx0aXBsaWVyWDtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICB2YXIgbGltaXRzID0gbmV3IExpbWl0cyh7XG4gICAgc3RhcnRYOiBzdGFydFgsXG4gICAgc3RhcnRZOiBzdGFydFksXG4gICAgZW5kWDogZW5kWCxcbiAgICBlbmRZOiBlbmRZLFxuICAgIHN0YXJ0TXVsdGlwbGllclg6IHN0YXJ0TXVsdGlwbGllclgsXG4gICAgZW5kTXVsdGlwbGllclg6IGVuZE11bHRpcGxpZXJYLFxuICAgIHN0YXJ0TXVsdGlwbGllclk6IHN0YXJ0TXVsdGlwbGllclksXG4gICAgZW5kTXVsdGlwbGllclk6IGVuZE11bHRpcGxpZXJZXG4gIH0pO1xuICByZXR1cm4gbGltaXRzO1xufVxuXG5mdW5jdGlvbiBzY2FsZVRyYW5zbGF0ZUVmZmVjdHNGb3JTbG93ZXJTY3JvbGwoZWZmZWN0cywgbGltaXRzKSB7XG4gIHZhciBlZmZlY3RzQ29weSA9IF9leHRlbmRzKHt9LCBlZmZlY3RzKTtcblxuICBpZiAoZWZmZWN0c0NvcHkudHJhbnNsYXRlWCkge1xuICAgIGVmZmVjdHNDb3B5LnRyYW5zbGF0ZVggPSBfZXh0ZW5kcyh7fSwgZWZmZWN0cy50cmFuc2xhdGVYLCB7XG4gICAgICBzdGFydDogZWZmZWN0c0NvcHkudHJhbnNsYXRlWC5zdGFydCAqIGxpbWl0cy5zdGFydE11bHRpcGxpZXJYLFxuICAgICAgZW5kOiBlZmZlY3RzQ29weS50cmFuc2xhdGVYLmVuZCAqIGxpbWl0cy5lbmRNdWx0aXBsaWVyWFxuICAgIH0pO1xuICB9XG5cbiAgaWYgKGVmZmVjdHNDb3B5LnRyYW5zbGF0ZVkpIHtcbiAgICBlZmZlY3RzQ29weS50cmFuc2xhdGVZID0gX2V4dGVuZHMoe30sIGVmZmVjdHMudHJhbnNsYXRlWSwge1xuICAgICAgc3RhcnQ6IGVmZmVjdHNDb3B5LnRyYW5zbGF0ZVkuc3RhcnQgKiBsaW1pdHMuc3RhcnRNdWx0aXBsaWVyWSxcbiAgICAgIGVuZDogZWZmZWN0c0NvcHkudHJhbnNsYXRlWS5lbmQgKiBsaW1pdHMuZW5kTXVsdGlwbGllcllcbiAgICB9KTtcbiAgfVxuXG4gIHJldHVybiBlZmZlY3RzQ29weTtcbn1cblxuZnVuY3Rpb24gZ2V0U2hvdWxkU2NhbGVUcmFuc2xhdGVFZmZlY3RzKHByb3BzLCBlZmZlY3RzLCBzY3JvbGxBeGlzKSB7XG4gIGlmIChwcm9wcy5yb290TWFyZ2luIHx8IHByb3BzLnRhcmdldEVsZW1lbnQgfHwgcHJvcHMuc2hvdWxkRGlzYWJsZVNjYWxpbmdUcmFuc2xhdGlvbnMpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cblxuICBpZiAoISFlZmZlY3RzLnRyYW5zbGF0ZVggJiYgc2Nyb2xsQXhpcyA9PT0gU2Nyb2xsQXhpcy5ob3Jpem9udGFsIHx8ICEhZWZmZWN0cy50cmFuc2xhdGVZICYmIHNjcm9sbEF4aXMgPT09IFNjcm9sbEF4aXMudmVydGljYWwpIHtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfVxuXG4gIHJldHVybiBmYWxzZTtcbn1cblxudmFyIGNsYW1wID0gZnVuY3Rpb24gY2xhbXAobnVtLCBtaW4sIG1heCkge1xuICByZXR1cm4gTWF0aC5taW4oTWF0aC5tYXgobnVtLCBtaW4pLCBtYXgpO1xufTtcblxudmFyIEVsZW1lbnQgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkge1xuICBmdW5jdGlvbiBFbGVtZW50KG9wdGlvbnMpIHtcbiAgICB0aGlzLmVsID0gb3B0aW9ucy5lbDtcbiAgICB0aGlzLnByb3BzID0gb3B0aW9ucy5wcm9wcztcbiAgICB0aGlzLnNjcm9sbEF4aXMgPSBvcHRpb25zLnNjcm9sbEF4aXM7XG4gICAgdGhpcy5kaXNhYmxlZFBhcmFsbGF4Q29udHJvbGxlciA9IG9wdGlvbnMuZGlzYWJsZWRQYXJhbGxheENvbnRyb2xsZXIgfHwgZmFsc2U7XG4gICAgdGhpcy5pZCA9IGNyZWF0ZUlkKCk7XG4gICAgdGhpcy5lZmZlY3RzID0gcGFyc2VFbGVtZW50VHJhbnNpdGlvbkVmZmVjdHModGhpcy5wcm9wcywgdGhpcy5zY3JvbGxBeGlzKTtcbiAgICB0aGlzLmlzSW5WaWV3ID0gbnVsbDtcbiAgICB0aGlzLnByb2dyZXNzID0gMDtcblxuICAgIHRoaXMuX3NldEVsZW1lbnRFYXNpbmcob3B0aW9ucy5wcm9wcy5lYXNpbmcpO1xuXG4gICAgc2V0V2lsbENoYW5nZVN0eWxlcyhvcHRpb25zLmVsLCB0aGlzLmVmZmVjdHMpO1xuICB9XG5cbiAgdmFyIF9wcm90byA9IEVsZW1lbnQucHJvdG90eXBlO1xuXG4gIF9wcm90by51cGRhdGVQcm9wcyA9IGZ1bmN0aW9uIHVwZGF0ZVByb3BzKG5leHRQcm9wcykge1xuICAgIHRoaXMucHJvcHMgPSBfZXh0ZW5kcyh7fSwgdGhpcy5wcm9wcywgbmV4dFByb3BzKTtcbiAgICB0aGlzLmVmZmVjdHMgPSBwYXJzZUVsZW1lbnRUcmFuc2l0aW9uRWZmZWN0cyhuZXh0UHJvcHMsIHRoaXMuc2Nyb2xsQXhpcyk7XG5cbiAgICB0aGlzLl9zZXRFbGVtZW50RWFzaW5nKG5leHRQcm9wcy5lYXNpbmcpO1xuXG4gICAgcmV0dXJuIHRoaXM7XG4gIH07XG5cbiAgX3Byb3RvLnNldENhY2hlZEF0dHJpYnV0ZXMgPSBmdW5jdGlvbiBzZXRDYWNoZWRBdHRyaWJ1dGVzKHZpZXcsIHNjcm9sbCkge1xuICAgIC8vIE5PVEU6IE11c3QgcmVzZXQgc3R5bGVzIGJlZm9yZSBnZXR0aW5nIHRoZSByZWN0LCBhcyBpdCBtaWdodCBpbXBhY3QgdGhlIG5hdHVyYWwgcG9zaXRpb25cbiAgICByZXNldFN0eWxlcyh0aGlzKTtcbiAgICB0aGlzLnJlY3QgPSBuZXcgUmVjdCh7XG4gICAgICBlbDogdGhpcy5wcm9wcy50YXJnZXRFbGVtZW50IHx8IHRoaXMuZWwsXG4gICAgICByb290TWFyZ2luOiB0aGlzLnByb3BzLnJvb3RNYXJnaW4sXG4gICAgICB2aWV3OiB2aWV3XG4gICAgfSk7XG4gICAgdmFyIHNob3VsZFNjYWxlVHJhbnNsYXRlRWZmZWN0cyA9IGdldFNob3VsZFNjYWxlVHJhbnNsYXRlRWZmZWN0cyh0aGlzLnByb3BzLCB0aGlzLmVmZmVjdHMsIHRoaXMuc2Nyb2xsQXhpcyk7XG5cbiAgICBpZiAodHlwZW9mIHRoaXMucHJvcHMuc3RhcnRTY3JvbGwgPT09ICdudW1iZXInICYmIHR5cGVvZiB0aGlzLnByb3BzLmVuZFNjcm9sbCA9PT0gJ251bWJlcicpIHtcbiAgICAgIHRoaXMubGltaXRzID0gbmV3IExpbWl0cyh7XG4gICAgICAgIHN0YXJ0WDogdGhpcy5wcm9wcy5zdGFydFNjcm9sbCxcbiAgICAgICAgc3RhcnRZOiB0aGlzLnByb3BzLnN0YXJ0U2Nyb2xsLFxuICAgICAgICBlbmRYOiB0aGlzLnByb3BzLmVuZFNjcm9sbCxcbiAgICAgICAgZW5kWTogdGhpcy5wcm9wcy5lbmRTY3JvbGxcbiAgICAgIH0pOyAvLyBVbmRvIHRoZSByZXNldCAtLSBwbGFjZSBpdCBiYWNrIGF0IGN1cnJlbnQgcG9zaXRpb24gd2l0aCBzdHlsZXNcblxuICAgICAgdGhpcy5fc2V0RWxlbWVudFN0eWxlcygpO1xuXG4gICAgICByZXR1cm4gdGhpcztcbiAgICB9XG5cbiAgICBpZiAoc2hvdWxkU2NhbGVUcmFuc2xhdGVFZmZlY3RzKSB7XG4gICAgICB0aGlzLmxpbWl0cyA9IGNyZWF0ZUxpbWl0c1dpdGhUcmFuc2xhdGlvbnNGb3JSZWxhdGl2ZUVsZW1lbnRzKHRoaXMucmVjdCwgdmlldywgdGhpcy5lZmZlY3RzLCBzY3JvbGwsIHRoaXMuc2Nyb2xsQXhpcywgdGhpcy5wcm9wcy5zaG91bGRBbHdheXNDb21wbGV0ZUFuaW1hdGlvbik7XG4gICAgICB0aGlzLnNjYWxlZEVmZmVjdHMgPSBzY2FsZVRyYW5zbGF0ZUVmZmVjdHNGb3JTbG93ZXJTY3JvbGwodGhpcy5lZmZlY3RzLCB0aGlzLmxpbWl0cyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRoaXMubGltaXRzID0gY3JlYXRlTGltaXRzRm9yUmVsYXRpdmVFbGVtZW50cyh0aGlzLnJlY3QsIHZpZXcsIHNjcm9sbCwgdGhpcy5wcm9wcy5zaG91bGRBbHdheXNDb21wbGV0ZUFuaW1hdGlvbik7XG4gICAgfSAvLyBVbmRvIHRoZSByZXNldCAtLSBwbGFjZSBpdCBiYWNrIGF0IGN1cnJlbnQgcG9zaXRpb24gd2l0aCBzdHlsZXNcblxuXG4gICAgdGhpcy5fc2V0RWxlbWVudFN0eWxlcygpO1xuXG4gICAgcmV0dXJuIHRoaXM7XG4gIH07XG5cbiAgX3Byb3RvLl91cGRhdGVFbGVtZW50SXNJblZpZXcgPSBmdW5jdGlvbiBfdXBkYXRlRWxlbWVudElzSW5WaWV3KG5leHRJc0luVmlldykge1xuICAgIC8vIE5PVEU6IENoZWNrIGlmIHRoaXMgaXMgdGhlIGZpcnN0IGNoYW5nZSB0byBtYWtlIHN1cmUgb25FeGl0IGlzbid0IGNhbGxlZFxuICAgIHZhciBpc0ZpcnN0Q2hhbmdlID0gdGhpcy5pc0luVmlldyA9PT0gbnVsbDtcblxuICAgIGlmIChuZXh0SXNJblZpZXcgIT09IHRoaXMuaXNJblZpZXcpIHtcbiAgICAgIGlmIChuZXh0SXNJblZpZXcpIHtcbiAgICAgICAgdGhpcy5wcm9wcy5vbkVudGVyICYmIHRoaXMucHJvcHMub25FbnRlcih0aGlzKTtcbiAgICAgIH0gZWxzZSBpZiAoIWlzRmlyc3RDaGFuZ2UpIHtcbiAgICAgICAgdGhpcy5fc2V0RmluYWxQcm9ncmVzcygpO1xuXG4gICAgICAgIHRoaXMuX3NldEVsZW1lbnRTdHlsZXMoKTtcblxuICAgICAgICB0aGlzLnByb3BzLm9uRXhpdCAmJiB0aGlzLnByb3BzLm9uRXhpdCh0aGlzKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICB0aGlzLmlzSW5WaWV3ID0gbmV4dElzSW5WaWV3O1xuICB9O1xuXG4gIF9wcm90by5fc2V0RmluYWxQcm9ncmVzcyA9IGZ1bmN0aW9uIF9zZXRGaW5hbFByb2dyZXNzKCkge1xuICAgIHZhciBmaW5hbFByb2dyZXNzID0gY2xhbXAoTWF0aC5yb3VuZCh0aGlzLnByb2dyZXNzKSwgMCwgMSk7XG5cbiAgICB0aGlzLl91cGRhdGVFbGVtZW50UHJvZ3Jlc3MoZmluYWxQcm9ncmVzcyk7XG4gIH07XG5cbiAgX3Byb3RvLl9zZXRFbGVtZW50U3R5bGVzID0gZnVuY3Rpb24gX3NldEVsZW1lbnRTdHlsZXMoKSB7XG4gICAgaWYgKHRoaXMucHJvcHMuZGlzYWJsZWQgfHwgdGhpcy5kaXNhYmxlZFBhcmFsbGF4Q29udHJvbGxlcikgcmV0dXJuO1xuICAgIHZhciBlZmZlY3RzID0gdGhpcy5zY2FsZWRFZmZlY3RzIHx8IHRoaXMuZWZmZWN0cztcbiAgICBzZXRFbGVtZW50U3R5bGVzKGVmZmVjdHMsIHRoaXMucHJvZ3Jlc3MsIHRoaXMuZWwpO1xuICB9O1xuXG4gIF9wcm90by5fdXBkYXRlRWxlbWVudFByb2dyZXNzID0gZnVuY3Rpb24gX3VwZGF0ZUVsZW1lbnRQcm9ncmVzcyhuZXh0UHJvZ3Jlc3MpIHtcbiAgICB0aGlzLnByb2dyZXNzID0gbmV4dFByb2dyZXNzO1xuICAgIHRoaXMucHJvcHMub25Qcm9ncmVzc0NoYW5nZSAmJiB0aGlzLnByb3BzLm9uUHJvZ3Jlc3NDaGFuZ2UodGhpcy5wcm9ncmVzcyk7XG4gICAgdGhpcy5wcm9wcy5vbkNoYW5nZSAmJiB0aGlzLnByb3BzLm9uQ2hhbmdlKHRoaXMpO1xuICB9O1xuXG4gIF9wcm90by5fc2V0RWxlbWVudEVhc2luZyA9IGZ1bmN0aW9uIF9zZXRFbGVtZW50RWFzaW5nKGVhc2luZykge1xuICAgIHRoaXMuZWFzaW5nID0gY3JlYXRlRWFzaW5nRnVuY3Rpb24oZWFzaW5nKTtcbiAgfTtcblxuICBfcHJvdG8udXBkYXRlRWxlbWVudE9wdGlvbnMgPSBmdW5jdGlvbiB1cGRhdGVFbGVtZW50T3B0aW9ucyhvcHRpb25zKSB7XG4gICAgdGhpcy5zY3JvbGxBeGlzID0gb3B0aW9ucy5zY3JvbGxBeGlzO1xuICAgIHRoaXMuZGlzYWJsZWRQYXJhbGxheENvbnRyb2xsZXIgPSBvcHRpb25zLmRpc2FibGVkUGFyYWxsYXhDb250cm9sbGVyIHx8IGZhbHNlO1xuICB9O1xuXG4gIF9wcm90by51cGRhdGVQb3NpdGlvbiA9IGZ1bmN0aW9uIHVwZGF0ZVBvc2l0aW9uKHNjcm9sbCkge1xuICAgIGlmICghdGhpcy5saW1pdHMpIHJldHVybiB0aGlzO1xuICAgIHZhciBpc1ZlcnRpY2FsID0gdGhpcy5zY3JvbGxBeGlzID09PSBTY3JvbGxBeGlzLnZlcnRpY2FsO1xuICAgIHZhciBpc0ZpcnN0Q2hhbmdlID0gdGhpcy5pc0luVmlldyA9PT0gbnVsbDsgLy8gYmFzZWQgb24gc2Nyb2xsIGF4aXNcblxuICAgIHZhciBzdGFydCA9IGlzVmVydGljYWwgPyB0aGlzLmxpbWl0cy5zdGFydFkgOiB0aGlzLmxpbWl0cy5zdGFydFg7XG4gICAgdmFyIGVuZCA9IGlzVmVydGljYWwgPyB0aGlzLmxpbWl0cy5lbmRZIDogdGhpcy5saW1pdHMuZW5kWDtcbiAgICB2YXIgdG90YWwgPSBpc1ZlcnRpY2FsID8gdGhpcy5saW1pdHMudG90YWxZIDogdGhpcy5saW1pdHMudG90YWxYO1xuICAgIHZhciBzID0gaXNWZXJ0aWNhbCA/IHNjcm9sbC55IDogc2Nyb2xsLng7IC8vIGNoZWNrIGlmIGluIHZpZXdcblxuICAgIHZhciBuZXh0SXNJblZpZXcgPSBpc0VsZW1lbnRJblZpZXcoc3RhcnQsIGVuZCwgcyk7XG5cbiAgICB0aGlzLl91cGRhdGVFbGVtZW50SXNJblZpZXcobmV4dElzSW5WaWV3KTsgLy8gc2V0IHRoZSBwcm9ncmVzcyBpZiBpbiB2aWV3IG9yIHRoaXMgaXMgdGhlIGZpcnN0IGNoYW5nZVxuXG5cbiAgICBpZiAobmV4dElzSW5WaWV3KSB7XG4gICAgICB2YXIgbmV4dFByb2dyZXNzID0gZ2V0UHJvZ3Jlc3NBbW91bnQoc3RhcnQsIHRvdGFsLCBzLCB0aGlzLmVhc2luZyk7XG5cbiAgICAgIHRoaXMuX3VwZGF0ZUVsZW1lbnRQcm9ncmVzcyhuZXh0UHJvZ3Jlc3MpO1xuXG4gICAgICB0aGlzLl9zZXRFbGVtZW50U3R5bGVzKCk7XG4gICAgfSBlbHNlIGlmIChpc0ZpcnN0Q2hhbmdlKSB7XG4gICAgICAvLyBOT1RFOiB0aGlzLl91cGRhdGVFbGVtZW50UHJvZ3Jlc3MgLS0gZG9udCB1c2UgdGhpcyBiZWNhdXNlIGl0IHdpbGwgdHJpZ2dlciBvbkNoYW5nZVxuICAgICAgdGhpcy5wcm9ncmVzcyA9IGNsYW1wKE1hdGgucm91bmQoZ2V0UHJvZ3Jlc3NBbW91bnQoc3RhcnQsIHRvdGFsLCBzLCB0aGlzLmVhc2luZykpLCAwLCAxKTtcblxuICAgICAgdGhpcy5fc2V0RWxlbWVudFN0eWxlcygpO1xuICAgIH1cblxuICAgIHJldHVybiB0aGlzO1xuICB9O1xuXG4gIHJldHVybiBFbGVtZW50O1xufSgpO1xuXG52YXIgVmlldyA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoKSB7XG4gIGZ1bmN0aW9uIFZpZXcoY29uZmlnKSB7XG4gICAgdGhpcy5zY3JvbGxDb250YWluZXIgPSBjb25maWcuc2Nyb2xsQ29udGFpbmVyO1xuICAgIHRoaXMud2lkdGggPSBjb25maWcud2lkdGg7XG4gICAgdGhpcy5oZWlnaHQgPSBjb25maWcuaGVpZ2h0O1xuICAgIHRoaXMuc2Nyb2xsSGVpZ2h0ID0gY29uZmlnLnNjcm9sbEhlaWdodDtcbiAgICB0aGlzLnNjcm9sbFdpZHRoID0gY29uZmlnLnNjcm9sbFdpZHRoO1xuICB9XG5cbiAgdmFyIF9wcm90byA9IFZpZXcucHJvdG90eXBlO1xuXG4gIF9wcm90by5oYXNDaGFuZ2VkID0gZnVuY3Rpb24gaGFzQ2hhbmdlZChwYXJhbXMpIHtcbiAgICBpZiAocGFyYW1zLndpZHRoICE9PSB0aGlzLndpZHRoIHx8IHBhcmFtcy5oZWlnaHQgIT09IHRoaXMuaGVpZ2h0IHx8IHBhcmFtcy5zY3JvbGxXaWR0aCAhPT0gdGhpcy5zY3JvbGxXaWR0aCB8fCBwYXJhbXMuc2Nyb2xsSGVpZ2h0ICE9PSB0aGlzLnNjcm9sbEhlaWdodCkge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuXG4gICAgcmV0dXJuIGZhbHNlO1xuICB9O1xuXG4gIF9wcm90by5zZXRTaXplID0gZnVuY3Rpb24gc2V0U2l6ZShwYXJhbXMpIHtcbiAgICB0aGlzLndpZHRoID0gcGFyYW1zLndpZHRoO1xuICAgIHRoaXMuaGVpZ2h0ID0gcGFyYW1zLmhlaWdodDtcbiAgICB0aGlzLnNjcm9sbEhlaWdodCA9IHBhcmFtcy5zY3JvbGxIZWlnaHQ7XG4gICAgdGhpcy5zY3JvbGxXaWR0aCA9IHBhcmFtcy5zY3JvbGxXaWR0aDtcbiAgICByZXR1cm4gdGhpcztcbiAgfTtcblxuICByZXR1cm4gVmlldztcbn0oKTtcblxudmFyIFNjcm9sbCA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoKSB7XG4gIGZ1bmN0aW9uIFNjcm9sbCh4LCB5KSB7XG4gICAgdGhpcy54ID0geDtcbiAgICB0aGlzLnkgPSB5O1xuICAgIHRoaXMuZHggPSAwO1xuICAgIHRoaXMuZHkgPSAwO1xuICB9XG5cbiAgdmFyIF9wcm90byA9IFNjcm9sbC5wcm90b3R5cGU7XG5cbiAgX3Byb3RvLnNldFNjcm9sbCA9IGZ1bmN0aW9uIHNldFNjcm9sbCh4LCB5KSB7XG4gICAgdGhpcy5keCA9IHggLSB0aGlzLng7XG4gICAgdGhpcy5keSA9IHkgLSB0aGlzLnk7XG4gICAgdGhpcy54ID0geDtcbiAgICB0aGlzLnkgPSB5O1xuICAgIHJldHVybiB0aGlzO1xuICB9O1xuXG4gIHJldHVybiBTY3JvbGw7XG59KCk7XG5cbmZ1bmN0aW9uIHRlc3RGb3JQYXNzaXZlU2Nyb2xsKCkge1xuICB2YXIgc3VwcG9ydHNQYXNzaXZlT3B0aW9uID0gZmFsc2U7XG5cbiAgdHJ5IHtcbiAgICB2YXIgb3B0cyA9IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh7fSwgJ3Bhc3NpdmUnLCB7XG4gICAgICBnZXQ6IGZ1bmN0aW9uIGdldCgpIHtcbiAgICAgICAgc3VwcG9ydHNQYXNzaXZlT3B0aW9uID0gdHJ1ZTtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICB9XG4gICAgfSk7IC8vIEB0cy1leHBlY3QtZXJyb3JcblxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCd0ZXN0JywgbnVsbCwgb3B0cyk7IC8vIEB0cy1leHBlY3QtZXJyb3JcblxuICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCd0ZXN0JywgbnVsbCwgb3B0cyk7XG4gIH0gY2F0Y2ggKGUpIHt9XG5cbiAgcmV0dXJuIHN1cHBvcnRzUGFzc2l2ZU9wdGlvbjtcbn1cblxuLyoqXHJcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICogUGFyYWxsYXggQ29udHJvbGxlclxyXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXHJcbiAqXHJcbiAqIFRoZSBnbG9iYWwgY29udHJvbGxlciBmb3Igc2V0dGluZyB1cCBhbmQgbWFuYWdpbmcgYSBzY3JvbGwgdmlldyBvZiBlbGVtZW50cy5cclxuICpcclxuICovXG5cbnZhciBQYXJhbGxheENvbnRyb2xsZXIgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkge1xuICBmdW5jdGlvbiBQYXJhbGxheENvbnRyb2xsZXIoX3JlZikge1xuICAgIHZhciBfcmVmJHNjcm9sbEF4aXMgPSBfcmVmLnNjcm9sbEF4aXMsXG4gICAgICAgIHNjcm9sbEF4aXMgPSBfcmVmJHNjcm9sbEF4aXMgPT09IHZvaWQgMCA/IFNjcm9sbEF4aXMudmVydGljYWwgOiBfcmVmJHNjcm9sbEF4aXMsXG4gICAgICAgIHNjcm9sbENvbnRhaW5lciA9IF9yZWYuc2Nyb2xsQ29udGFpbmVyLFxuICAgICAgICBfcmVmJGRpc2FibGVkID0gX3JlZi5kaXNhYmxlZCxcbiAgICAgICAgZGlzYWJsZWQgPSBfcmVmJGRpc2FibGVkID09PSB2b2lkIDAgPyBmYWxzZSA6IF9yZWYkZGlzYWJsZWQ7XG4gICAgdGhpcy5kaXNhYmxlZCA9IGRpc2FibGVkO1xuICAgIHRoaXMuc2Nyb2xsQXhpcyA9IHNjcm9sbEF4aXM7IC8vIEFsbCBwYXJhbGxheCBlbGVtZW50cyB0byBiZSB1cGRhdGVkXG5cbiAgICB0aGlzLmVsZW1lbnRzID0gW107XG4gICAgdGhpcy5faGFzU2Nyb2xsQ29udGFpbmVyID0gISFzY3JvbGxDb250YWluZXI7XG4gICAgdGhpcy52aWV3RWwgPSBzY3JvbGxDb250YWluZXIgIT0gbnVsbCA/IHNjcm9sbENvbnRhaW5lciA6IHdpbmRvdzsgLy8gU2Nyb2xsIGFuZCBWaWV3XG5cbiAgICB2YXIgX3RoaXMkX2dldFNjcm9sbFBvc2l0ID0gdGhpcy5fZ2V0U2Nyb2xsUG9zaXRpb24oKSxcbiAgICAgICAgeCA9IF90aGlzJF9nZXRTY3JvbGxQb3NpdFswXSxcbiAgICAgICAgeSA9IF90aGlzJF9nZXRTY3JvbGxQb3NpdFsxXTtcblxuICAgIHRoaXMuc2Nyb2xsID0gbmV3IFNjcm9sbCh4LCB5KTtcbiAgICB0aGlzLnZpZXcgPSBuZXcgVmlldyh7XG4gICAgICB3aWR0aDogMCxcbiAgICAgIGhlaWdodDogMCxcbiAgICAgIHNjcm9sbFdpZHRoOiAwLFxuICAgICAgc2Nyb2xsSGVpZ2h0OiAwLFxuICAgICAgc2Nyb2xsQ29udGFpbmVyOiB0aGlzLl9oYXNTY3JvbGxDb250YWluZXIgPyBzY3JvbGxDb250YWluZXIgOiB1bmRlZmluZWRcbiAgICB9KTsgLy8gVGlja2luZ1xuXG4gICAgdGhpcy5fdGlja2luZyA9IGZhbHNlOyAvLyBQYXNzaXZlIHN1cHBvcnRcblxuICAgIHRoaXMuX3N1cHBvcnRzUGFzc2l2ZSA9IHRlc3RGb3JQYXNzaXZlU2Nyb2xsKCk7IC8vIEJpbmQgbWV0aG9kcyB0byBjbGFzc1xuXG4gICAgdGhpcy5fYmluZEFsbE1ldGhvZHMoKTsgLy8gSWYgdGhpcyBpcyBpbml0aWFsaXplZCBkaXNhYmxlZCwgZG9uJ3QgZG8gYW55dGhpbmcgYmVsb3cuXG5cblxuICAgIGlmICh0aGlzLmRpc2FibGVkKSByZXR1cm47XG5cbiAgICB0aGlzLl9hZGRMaXN0ZW5lcnModGhpcy52aWV3RWwpO1xuXG4gICAgdGhpcy5fYWRkUmVzaXplT2JzZXJ2ZXIoKTtcblxuICAgIHRoaXMuX3NldFZpZXdTaXplKCk7XG4gIH1cbiAgLyoqXHJcbiAgICogU3RhdGljIG1ldGhvZCB0byBpbnN0YW50aWF0ZSB0aGUgUGFyYWxsYXhDb250cm9sbGVyLlxyXG4gICAqIEByZXR1cm5zIHtDbGFzc30gUGFyYWxsYXhDb250cm9sbGVyXHJcbiAgICovXG5cblxuICBQYXJhbGxheENvbnRyb2xsZXIuaW5pdCA9IGZ1bmN0aW9uIGluaXQob3B0aW9ucykge1xuICAgIHZhciBoYXNXaW5kb3cgPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJztcblxuICAgIGlmICghaGFzV2luZG93KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0xvb2tzIGxpa2UgUGFyYWxsYXhDb250cm9sbGVyLmluaXQoKSB3YXMgY2FsbGVkIG9uIHRoZSBzZXJ2ZXIuIFRoaXMgbWV0aG9kIG11c3QgYmUgY2FsbGVkIG9uIHRoZSBjbGllbnQuJyk7XG4gICAgfVxuXG4gICAgcmV0dXJuIG5ldyBQYXJhbGxheENvbnRyb2xsZXIob3B0aW9ucyk7XG4gIH07XG5cbiAgdmFyIF9wcm90byA9IFBhcmFsbGF4Q29udHJvbGxlci5wcm90b3R5cGU7XG5cbiAgX3Byb3RvLl9iaW5kQWxsTWV0aG9kcyA9IGZ1bmN0aW9uIF9iaW5kQWxsTWV0aG9kcygpIHtcbiAgICB2YXIgX3RoaXMgPSB0aGlzO1xuXG4gICAgWydfYWRkTGlzdGVuZXJzJywgJ19yZW1vdmVMaXN0ZW5lcnMnLCAnX2dldFNjcm9sbFBvc2l0aW9uJywgJ19oYW5kbGVTY3JvbGwnLCAnX2hhbmRsZVVwZGF0ZUNhY2hlJywgJ191cGRhdGVBbGxFbGVtZW50cycsICdfdXBkYXRlRWxlbWVudFBvc2l0aW9uJywgJ19zZXRWaWV3U2l6ZScsICdfYWRkUmVzaXplT2JzZXJ2ZXInLCAnX2NoZWNrSWZWaWV3SGFzQ2hhbmdlZCcsICdfZ2V0Vmlld1BhcmFtcycsICdnZXRFbGVtZW50cycsICdjcmVhdGVFbGVtZW50JywgJ3JlbW92ZUVsZW1lbnRCeUlkJywgJ3Jlc2V0RWxlbWVudFN0eWxlcycsICd1cGRhdGVFbGVtZW50UHJvcHNCeUlkJywgJ3VwZGF0ZScsICd1cGRhdGVTY3JvbGxDb250YWluZXInLCAnZGVzdHJveSddLmZvckVhY2goZnVuY3Rpb24gKG1ldGhvZCkge1xuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvclxuICAgICAgX3RoaXNbbWV0aG9kXSA9IF90aGlzW21ldGhvZF0uYmluZChfdGhpcyk7XG4gICAgfSk7XG4gIH07XG5cbiAgX3Byb3RvLl9hZGRMaXN0ZW5lcnMgPSBmdW5jdGlvbiBfYWRkTGlzdGVuZXJzKGVsKSB7XG4gICAgZWwuYWRkRXZlbnRMaXN0ZW5lcignc2Nyb2xsJywgdGhpcy5faGFuZGxlU2Nyb2xsLCB0aGlzLl9zdXBwb3J0c1Bhc3NpdmUgPyB7XG4gICAgICBwYXNzaXZlOiB0cnVlXG4gICAgfSA6IGZhbHNlKTtcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5faGFuZGxlVXBkYXRlQ2FjaGUsIGZhbHNlKTtcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignYmx1cicsIHRoaXMuX2hhbmRsZVVwZGF0ZUNhY2hlLCBmYWxzZSk7XG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2ZvY3VzJywgdGhpcy5faGFuZGxlVXBkYXRlQ2FjaGUsIGZhbHNlKTtcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignbG9hZCcsIHRoaXMuX2hhbmRsZVVwZGF0ZUNhY2hlLCBmYWxzZSk7XG4gIH07XG5cbiAgX3Byb3RvLl9yZW1vdmVMaXN0ZW5lcnMgPSBmdW5jdGlvbiBfcmVtb3ZlTGlzdGVuZXJzKGVsKSB7XG4gICAgdmFyIF90aGlzJF9yZXNpemVPYnNlcnZlcjtcblxuICAgIGVsLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIHRoaXMuX2hhbmRsZVNjcm9sbCwgZmFsc2UpO1xuICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLl9oYW5kbGVVcGRhdGVDYWNoZSwgZmFsc2UpO1xuICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdibHVyJywgdGhpcy5faGFuZGxlVXBkYXRlQ2FjaGUsIGZhbHNlKTtcbiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignZm9jdXMnLCB0aGlzLl9oYW5kbGVVcGRhdGVDYWNoZSwgZmFsc2UpO1xuICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdsb2FkJywgdGhpcy5faGFuZGxlVXBkYXRlQ2FjaGUsIGZhbHNlKTtcbiAgICAoX3RoaXMkX3Jlc2l6ZU9ic2VydmVyID0gdGhpcy5fcmVzaXplT2JzZXJ2ZXIpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRfcmVzaXplT2JzZXJ2ZXIuZGlzY29ubmVjdCgpO1xuICB9O1xuXG4gIF9wcm90by5fYWRkUmVzaXplT2JzZXJ2ZXIgPSBmdW5jdGlvbiBfYWRkUmVzaXplT2JzZXJ2ZXIoKSB7XG4gICAgdmFyIF90aGlzMiA9IHRoaXM7XG5cbiAgICB0cnkge1xuICAgICAgdmFyIG9ic2VydmVkRWwgPSB0aGlzLl9oYXNTY3JvbGxDb250YWluZXIgPyB0aGlzLnZpZXdFbCA6IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudDtcbiAgICAgIHRoaXMuX3Jlc2l6ZU9ic2VydmVyID0gbmV3IFJlc2l6ZU9ic2VydmVyKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIF90aGlzMi51cGRhdGUoKTtcbiAgICAgIH0pO1xuXG4gICAgICB0aGlzLl9yZXNpemVPYnNlcnZlci5vYnNlcnZlKG9ic2VydmVkRWwpO1xuICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIGNyZWF0ZSB0aGUgcmVzaXplIG9ic2VydmVyIGluIHRoZSBQYXJhbGxheENvbnRvbGxlcicpO1xuICAgIH1cbiAgfTtcblxuICBfcHJvdG8uX2dldFNjcm9sbFBvc2l0aW9uID0gZnVuY3Rpb24gX2dldFNjcm9sbFBvc2l0aW9uKCkge1xuICAgIC8vIFNhdmUgY3VycmVudCBzY3JvbGxcbiAgICAvLyBTdXBwb3J0cyBJRSA5IGFuZCB1cC5cbiAgICB2YXIgbnggPSB0aGlzLl9oYXNTY3JvbGxDb250YWluZXIgPyAvLyBAdHMtZXhwZWN0LWVycm9yXG4gICAgdGhpcy52aWV3RWwuc2Nyb2xsTGVmdCA6IHdpbmRvdy5wYWdlWE9mZnNldDtcbiAgICB2YXIgbnkgPSB0aGlzLl9oYXNTY3JvbGxDb250YWluZXIgPyAvLyBAdHMtZXhwZWN0LWVycm9yXG4gICAgdGhpcy52aWV3RWwuc2Nyb2xsVG9wIDogd2luZG93LnBhZ2VZT2Zmc2V0O1xuICAgIHJldHVybiBbbngsIG55XTtcbiAgfVxuICAvKipcclxuICAgKiBXaW5kb3cgc2Nyb2xsIGhhbmRsZXIgc2V0cyBzY3JvbGwgcG9zaXRpb25cclxuICAgKiBhbmQgdGhlbiBjYWxscyAnX3VwZGF0ZUFsbEVsZW1lbnRzKCknLlxyXG4gICAqL1xuICA7XG5cbiAgX3Byb3RvLl9oYW5kbGVTY3JvbGwgPSBmdW5jdGlvbiBfaGFuZGxlU2Nyb2xsKCkge1xuICAgIHZhciBfdGhpcyRlbGVtZW50cztcblxuICAgIHZhciBfdGhpcyRfZ2V0U2Nyb2xsUG9zaXQyID0gdGhpcy5fZ2V0U2Nyb2xsUG9zaXRpb24oKSxcbiAgICAgICAgbnggPSBfdGhpcyRfZ2V0U2Nyb2xsUG9zaXQyWzBdLFxuICAgICAgICBueSA9IF90aGlzJF9nZXRTY3JvbGxQb3NpdDJbMV07XG5cbiAgICB0aGlzLnNjcm9sbC5zZXRTY3JvbGwobngsIG55KTsgLy8gT25seSBjYWxsZWQgaWYgdGhlIGxhc3QgYW5pbWF0aW9uIHJlcXVlc3QgaGFzIGJlZW5cbiAgICAvLyBjb21wbGV0ZWQgYW5kIHRoZXJlIGFyZSBwYXJhbGxheCBlbGVtZW50cyB0byB1cGRhdGVcblxuICAgIGlmICghdGhpcy5fdGlja2luZyAmJiAoKF90aGlzJGVsZW1lbnRzID0gdGhpcy5lbGVtZW50cykgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzJGVsZW1lbnRzLmxlbmd0aCkgPiAwKSB7XG4gICAgICB0aGlzLl90aWNraW5nID0gdHJ1ZTsgLy8gQHRzLWlnbm9yZVxuXG4gICAgICB3aW5kb3cucmVxdWVzdEFuaW1hdGlvbkZyYW1lKHRoaXMuX3VwZGF0ZUFsbEVsZW1lbnRzKTtcbiAgICB9XG4gIH1cbiAgLyoqXHJcbiAgICogV2luZG93IHJlc2l6ZSBoYW5kbGVyLiBTZXRzIHRoZSBuZXcgd2luZG93IGlubmVyIGhlaWdodFxyXG4gICAqIHRoZW4gdXBkYXRlcyBwYXJhbGxheCBlbGVtZW50IGF0dHJpYnV0ZXMgYW5kIHBvc2l0aW9ucy5cclxuICAgKi9cbiAgO1xuXG4gIF9wcm90by5faGFuZGxlVXBkYXRlQ2FjaGUgPSBmdW5jdGlvbiBfaGFuZGxlVXBkYXRlQ2FjaGUoKSB7XG4gICAgdGhpcy5fc2V0Vmlld1NpemUoKTtcblxuICAgIHRoaXMuX3VwZGF0ZUFsbEVsZW1lbnRzKHtcbiAgICAgIHVwZGF0ZUNhY2hlOiB0cnVlXG4gICAgfSk7XG4gIH1cbiAgLyoqXHJcbiAgICogVXBkYXRlIGVsZW1lbnQgcG9zaXRpb25zLlxyXG4gICAqIERldGVybWluZXMgaWYgdGhlIGVsZW1lbnQgaXMgaW4gdmlldyBiYXNlZCBvbiB0aGUgY2FjaGVkXHJcbiAgICogYXR0cmlidXRlcywgaWYgc28gc2V0IHRoZSBlbGVtZW50cyBwYXJhbGxheCBzdHlsZXMuXHJcbiAgICovXG4gIDtcblxuICBfcHJvdG8uX3VwZGF0ZUFsbEVsZW1lbnRzID0gZnVuY3Rpb24gX3VwZGF0ZUFsbEVsZW1lbnRzKF90ZW1wKSB7XG4gICAgdmFyIF90aGlzMyA9IHRoaXM7XG5cbiAgICB2YXIgX3JlZjIgPSBfdGVtcCA9PT0gdm9pZCAwID8ge30gOiBfdGVtcCxcbiAgICAgICAgdXBkYXRlQ2FjaGUgPSBfcmVmMi51cGRhdGVDYWNoZTtcblxuICAgIGlmICh0aGlzLmVsZW1lbnRzKSB7XG4gICAgICB0aGlzLmVsZW1lbnRzLmZvckVhY2goZnVuY3Rpb24gKGVsZW1lbnQpIHtcbiAgICAgICAgaWYgKHVwZGF0ZUNhY2hlKSB7XG4gICAgICAgICAgZWxlbWVudC5zZXRDYWNoZWRBdHRyaWJ1dGVzKF90aGlzMy52aWV3LCBfdGhpczMuc2Nyb2xsKTtcbiAgICAgICAgfVxuXG4gICAgICAgIF90aGlzMy5fdXBkYXRlRWxlbWVudFBvc2l0aW9uKGVsZW1lbnQpO1xuICAgICAgfSk7XG4gICAgfSAvLyByZXNldCB0aWNraW5nIHNvIG1vcmUgYW5pbWF0aW9ucyBjYW4gYmUgY2FsbGVkXG5cblxuICAgIHRoaXMuX3RpY2tpbmcgPSBmYWxzZTtcbiAgfVxuICAvKipcclxuICAgKiBVcGRhdGUgZWxlbWVudCBwb3NpdGlvbnMuXHJcbiAgICogRGV0ZXJtaW5lcyBpZiB0aGUgZWxlbWVudCBpcyBpbiB2aWV3IGJhc2VkIG9uIHRoZSBjYWNoZWRcclxuICAgKiBhdHRyaWJ1dGVzLCBpZiBzbyBzZXQgdGhlIGVsZW1lbnRzIHBhcmFsbGF4IHN0eWxlcy5cclxuICAgKi9cbiAgO1xuXG4gIF9wcm90by5fdXBkYXRlRWxlbWVudFBvc2l0aW9uID0gZnVuY3Rpb24gX3VwZGF0ZUVsZW1lbnRQb3NpdGlvbihlbGVtZW50KSB7XG4gICAgaWYgKGVsZW1lbnQucHJvcHMuZGlzYWJsZWQgfHwgdGhpcy5kaXNhYmxlZCkgcmV0dXJuO1xuICAgIGVsZW1lbnQudXBkYXRlUG9zaXRpb24odGhpcy5zY3JvbGwpO1xuICB9XG4gIC8qKlxyXG4gICAqIEdldHMgdGhlIHBhcmFtcyB0byBzZXQgaW4gdGhlIFZpZXcgZnJvbSB0aGUgc2Nyb2xsIGNvbnRhaW5lciBvciB0aGUgd2luZG93XHJcbiAgICovXG4gIDtcblxuICBfcHJvdG8uX2dldFZpZXdQYXJhbXMgPSBmdW5jdGlvbiBfZ2V0Vmlld1BhcmFtcygpIHtcbiAgICBpZiAodGhpcy5faGFzU2Nyb2xsQ29udGFpbmVyKSB7XG4gICAgICAvLyBAdHMtZXhwZWN0LWVycm9yXG4gICAgICB2YXIgX3dpZHRoID0gdGhpcy52aWV3RWwub2Zmc2V0V2lkdGg7IC8vIEB0cy1leHBlY3QtZXJyb3JcblxuICAgICAgdmFyIF9oZWlnaHQgPSB0aGlzLnZpZXdFbC5vZmZzZXRIZWlnaHQ7IC8vIEB0cy1leHBlY3QtZXJyb3JcblxuICAgICAgdmFyIF9zY3JvbGxIZWlnaHQgPSB0aGlzLnZpZXdFbC5zY3JvbGxIZWlnaHQ7IC8vIEB0cy1leHBlY3QtZXJyb3JcblxuICAgICAgdmFyIF9zY3JvbGxXaWR0aCA9IHRoaXMudmlld0VsLnNjcm9sbFdpZHRoO1xuICAgICAgcmV0dXJuIHRoaXMudmlldy5zZXRTaXplKHtcbiAgICAgICAgd2lkdGg6IF93aWR0aCxcbiAgICAgICAgaGVpZ2h0OiBfaGVpZ2h0LFxuICAgICAgICBzY3JvbGxIZWlnaHQ6IF9zY3JvbGxIZWlnaHQsXG4gICAgICAgIHNjcm9sbFdpZHRoOiBfc2Nyb2xsV2lkdGhcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIHZhciBodG1sID0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50O1xuICAgIHZhciB3aWR0aCA9IHdpbmRvdy5pbm5lcldpZHRoIHx8IGh0bWwuY2xpZW50V2lkdGg7XG4gICAgdmFyIGhlaWdodCA9IHdpbmRvdy5pbm5lckhlaWdodCB8fCBodG1sLmNsaWVudEhlaWdodDtcbiAgICB2YXIgc2Nyb2xsSGVpZ2h0ID0gaHRtbC5zY3JvbGxIZWlnaHQ7XG4gICAgdmFyIHNjcm9sbFdpZHRoID0gaHRtbC5zY3JvbGxXaWR0aDtcbiAgICByZXR1cm4ge1xuICAgICAgd2lkdGg6IHdpZHRoLFxuICAgICAgaGVpZ2h0OiBoZWlnaHQsXG4gICAgICBzY3JvbGxIZWlnaHQ6IHNjcm9sbEhlaWdodCxcbiAgICAgIHNjcm9sbFdpZHRoOiBzY3JvbGxXaWR0aFxuICAgIH07XG4gIH1cbiAgLyoqXHJcbiAgICogQ2FjaGUgdGhlIHZpZXcgYXR0cmlidXRlc1xyXG4gICAqL1xuICA7XG5cbiAgX3Byb3RvLl9zZXRWaWV3U2l6ZSA9IGZ1bmN0aW9uIF9zZXRWaWV3U2l6ZSgpIHtcbiAgICByZXR1cm4gdGhpcy52aWV3LnNldFNpemUodGhpcy5fZ2V0Vmlld1BhcmFtcygpKTtcbiAgfVxuICAvKipcclxuICAgKiBDaGVja3MgaWYgYW55IG9mIHRoZSBjYWNoZWQgYXR0cmlidXRlcyBvZiB0aGUgdmlldyBoYXZlIGNoYW5nZWQuXHJcbiAgICogQHJldHVybnMgYm9vbGVhblxyXG4gICAqL1xuICA7XG5cbiAgX3Byb3RvLl9jaGVja0lmVmlld0hhc0NoYW5nZWQgPSBmdW5jdGlvbiBfY2hlY2tJZlZpZXdIYXNDaGFuZ2VkKCkge1xuICAgIHJldHVybiB0aGlzLnZpZXcuaGFzQ2hhbmdlZCh0aGlzLl9nZXRWaWV3UGFyYW1zKCkpO1xuICB9XG4gIC8qKlxyXG4gICAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgKiBQdWJsaWMgbWV0aG9kc1xyXG4gICAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cclxuICAgKi9cblxuICAvKipcclxuICAgKiBSZXR1cm5zIGFsbCB0aGUgcGFyYWxsYXggZWxlbWVudHMgaW4gdGhlIGNvbnRyb2xsZXJcclxuICAgKi9cbiAgO1xuXG4gIF9wcm90by5nZXRFbGVtZW50cyA9IGZ1bmN0aW9uIGdldEVsZW1lbnRzKCkge1xuICAgIHJldHVybiB0aGlzLmVsZW1lbnRzO1xuICB9XG4gIC8qKlxyXG4gICAqIENyZWF0ZXMgYW5kIHJldHVybnMgbmV3IHBhcmFsbGF4IGVsZW1lbnQgd2l0aCBwcm92aWRlZCBvcHRpb25zIHRvIGJlIG1hbmFnZWQgYnkgdGhlIGNvbnRyb2xsZXIuXHJcbiAgICovXG4gIDtcblxuICBfcHJvdG8uY3JlYXRlRWxlbWVudCA9IGZ1bmN0aW9uIGNyZWF0ZUVsZW1lbnQob3B0aW9ucykge1xuICAgIHZhciBuZXdFbGVtZW50ID0gbmV3IEVsZW1lbnQoX2V4dGVuZHMoe30sIG9wdGlvbnMsIHtcbiAgICAgIHNjcm9sbEF4aXM6IHRoaXMuc2Nyb2xsQXhpcyxcbiAgICAgIGRpc2FibGVkUGFyYWxsYXhDb250cm9sbGVyOiB0aGlzLmRpc2FibGVkXG4gICAgfSkpO1xuICAgIG5ld0VsZW1lbnQuc2V0Q2FjaGVkQXR0cmlidXRlcyh0aGlzLnZpZXcsIHRoaXMuc2Nyb2xsKTtcbiAgICB0aGlzLmVsZW1lbnRzID0gdGhpcy5lbGVtZW50cyA/IFtdLmNvbmNhdCh0aGlzLmVsZW1lbnRzLCBbbmV3RWxlbWVudF0pIDogW25ld0VsZW1lbnRdO1xuXG4gICAgdGhpcy5fdXBkYXRlRWxlbWVudFBvc2l0aW9uKG5ld0VsZW1lbnQpOyAvLyBOT1RFOiBUaGlzIGNoZWNrcyBpZiB0aGUgdmlldyBoYXMgY2hhbmdlZCB0aGVuIHVwZGF0ZSB0aGUgY29udHJvbGxlciBhbmQgYWxsIGVsZW1lbnRzIGlmIGl0IGhhc1xuICAgIC8vIFRoaXMgc2hvdWxkbid0IGFsd2F5cyBiZSBuZWNlc3Nhcnkgd2l0aCBhIHJlc2l6ZSBvYnNlcnZlciB3YXRjaGluZyB0aGUgdmlldyBlbGVtZW50XG4gICAgLy8gYnV0IHRoZXJlIHNlZW1zIHRvIGJlIGNhc2VzIHdoZXJlIHRoZSByZXNpemUgb2JzZXJ2ZXIgZG9lcyBub3QgY2F0Y2ggYW5kIHVwZGF0ZS5cblxuXG4gICAgaWYgKHRoaXMuX2NoZWNrSWZWaWV3SGFzQ2hhbmdlZCgpKSB7XG4gICAgICB0aGlzLnVwZGF0ZSgpO1xuICAgIH1cblxuICAgIHJldHVybiBuZXdFbGVtZW50O1xuICB9XG4gIC8qKlxyXG4gICAqIFJlbW92ZSBhbiBlbGVtZW50IGJ5IGlkXHJcbiAgICovXG4gIDtcblxuICBfcHJvdG8ucmVtb3ZlRWxlbWVudEJ5SWQgPSBmdW5jdGlvbiByZW1vdmVFbGVtZW50QnlJZChpZCkge1xuICAgIGlmICghdGhpcy5lbGVtZW50cykgcmV0dXJuO1xuICAgIHRoaXMuZWxlbWVudHMgPSB0aGlzLmVsZW1lbnRzLmZpbHRlcihmdW5jdGlvbiAoZWwpIHtcbiAgICAgIHJldHVybiBlbC5pZCAhPT0gaWQ7XG4gICAgfSk7XG4gIH1cbiAgLyoqXHJcbiAgICogVXBkYXRlcyBhbiBleGlzdGluZyBwYXJhbGxheCBlbGVtZW50IG9iamVjdCB3aXRoIG5ldyBvcHRpb25zLlxyXG4gICAqL1xuICA7XG5cbiAgX3Byb3RvLnVwZGF0ZUVsZW1lbnRQcm9wc0J5SWQgPSBmdW5jdGlvbiB1cGRhdGVFbGVtZW50UHJvcHNCeUlkKGlkLCBwcm9wcykge1xuICAgIGlmICh0aGlzLmVsZW1lbnRzKSB7XG4gICAgICB0aGlzLmVsZW1lbnRzID0gdGhpcy5lbGVtZW50cy5tYXAoZnVuY3Rpb24gKGVsKSB7XG4gICAgICAgIGlmIChlbC5pZCA9PT0gaWQpIHtcbiAgICAgICAgICByZXR1cm4gZWwudXBkYXRlUHJvcHMocHJvcHMpO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGVsO1xuICAgICAgfSk7XG4gICAgfVxuXG4gICAgdGhpcy51cGRhdGUoKTtcbiAgfVxuICAvKipcclxuICAgKiBSZW1vdmUgYSB0YXJnZXQgZWxlbWVudHMgcGFyYWxsYXggc3R5bGVzXHJcbiAgICovXG4gIDtcblxuICBfcHJvdG8ucmVzZXRFbGVtZW50U3R5bGVzID0gZnVuY3Rpb24gcmVzZXRFbGVtZW50U3R5bGVzKGVsZW1lbnQpIHtcbiAgICByZXNldFN0eWxlcyhlbGVtZW50KTtcbiAgfVxuICAvKipcclxuICAgKiBVcGRhdGVzIGFsbCBjYWNoZWQgYXR0cmlidXRlcyBvbiBwYXJhbGxheCBlbGVtZW50cy5cclxuICAgKi9cbiAgO1xuXG4gIF9wcm90by51cGRhdGUgPSBmdW5jdGlvbiB1cGRhdGUoKSB7XG4gICAgLy8gU2F2ZSB0aGUgbGF0ZXN0IHNjcm9sbCBwb3NpdGlvbiBiZWNhdXNlIHdpbmRvdy5zY3JvbGxcbiAgICAvLyBtYXkgYmUgY2FsbGVkIGFuZCB0aGUgaGFuZGxlIHNjcm9sbCBldmVudCBtYXkgbm90IGJlIGNhbGxlZC5cbiAgICB2YXIgX3RoaXMkX2dldFNjcm9sbFBvc2l0MyA9IHRoaXMuX2dldFNjcm9sbFBvc2l0aW9uKCksXG4gICAgICAgIG54ID0gX3RoaXMkX2dldFNjcm9sbFBvc2l0M1swXSxcbiAgICAgICAgbnkgPSBfdGhpcyRfZ2V0U2Nyb2xsUG9zaXQzWzFdO1xuXG4gICAgdGhpcy5zY3JvbGwuc2V0U2Nyb2xsKG54LCBueSk7XG5cbiAgICB0aGlzLl9zZXRWaWV3U2l6ZSgpO1xuXG4gICAgdGhpcy5fdXBkYXRlQWxsRWxlbWVudHMoe1xuICAgICAgdXBkYXRlQ2FjaGU6IHRydWVcbiAgICB9KTtcbiAgfVxuICAvKipcclxuICAgKiBVcGRhdGVzIHRoZSBzY3JvbGwgY29udGFpbmVyIG9mIHRoZSBwYXJhbGxheCBjb250cm9sbGVyXHJcbiAgICovXG4gIDtcblxuICBfcHJvdG8udXBkYXRlU2Nyb2xsQ29udGFpbmVyID0gZnVuY3Rpb24gdXBkYXRlU2Nyb2xsQ29udGFpbmVyKGVsKSB7XG4gICAgLy8gcmVtb3ZlIGV4aXN0aW5nIGxpc3RlbmVycyB3aXRoIGN1cnJlbnQgZWwgZmlyc3RcbiAgICB0aGlzLl9yZW1vdmVMaXN0ZW5lcnModGhpcy52aWV3RWwpO1xuXG4gICAgdGhpcy52aWV3RWwgPSBlbDtcbiAgICB0aGlzLl9oYXNTY3JvbGxDb250YWluZXIgPSAhIWVsO1xuICAgIHRoaXMudmlldyA9IG5ldyBWaWV3KHtcbiAgICAgIHdpZHRoOiAwLFxuICAgICAgaGVpZ2h0OiAwLFxuICAgICAgc2Nyb2xsV2lkdGg6IDAsXG4gICAgICBzY3JvbGxIZWlnaHQ6IDAsXG4gICAgICBzY3JvbGxDb250YWluZXI6IGVsXG4gICAgfSk7XG5cbiAgICB0aGlzLl9zZXRWaWV3U2l6ZSgpO1xuXG4gICAgdGhpcy5fYWRkTGlzdGVuZXJzKHRoaXMudmlld0VsKTtcblxuICAgIHRoaXMuX3VwZGF0ZUFsbEVsZW1lbnRzKHtcbiAgICAgIHVwZGF0ZUNhY2hlOiB0cnVlXG4gICAgfSk7XG4gIH07XG5cbiAgX3Byb3RvLmRpc2FibGVQYXJhbGxheENvbnRyb2xsZXIgPSBmdW5jdGlvbiBkaXNhYmxlUGFyYWxsYXhDb250cm9sbGVyKCkge1xuICAgIHRoaXMuZGlzYWJsZWQgPSB0cnVlOyAvLyByZW1vdmUgbGlzdGVuZXJzXG5cbiAgICB0aGlzLl9yZW1vdmVMaXN0ZW5lcnModGhpcy52aWV3RWwpOyAvLyByZXNldCBhbGwgc3R5bGVzXG5cblxuICAgIGlmICh0aGlzLmVsZW1lbnRzKSB7XG4gICAgICB0aGlzLmVsZW1lbnRzLmZvckVhY2goZnVuY3Rpb24gKGVsZW1lbnQpIHtcbiAgICAgICAgcmV0dXJuIHJlc2V0U3R5bGVzKGVsZW1lbnQpO1xuICAgICAgfSk7XG4gICAgfVxuICB9O1xuXG4gIF9wcm90by5lbmFibGVQYXJhbGxheENvbnRyb2xsZXIgPSBmdW5jdGlvbiBlbmFibGVQYXJhbGxheENvbnRyb2xsZXIoKSB7XG4gICAgdmFyIF90aGlzNCA9IHRoaXM7XG5cbiAgICB0aGlzLmRpc2FibGVkID0gZmFsc2U7XG5cbiAgICBpZiAodGhpcy5lbGVtZW50cykge1xuICAgICAgdGhpcy5lbGVtZW50cy5mb3JFYWNoKGZ1bmN0aW9uIChlbGVtZW50KSB7XG4gICAgICAgIHJldHVybiBlbGVtZW50LnVwZGF0ZUVsZW1lbnRPcHRpb25zKHtcbiAgICAgICAgICBkaXNhYmxlZFBhcmFsbGF4Q29udHJvbGxlcjogZmFsc2UsXG4gICAgICAgICAgc2Nyb2xsQXhpczogX3RoaXM0LnNjcm9sbEF4aXNcbiAgICAgICAgfSk7XG4gICAgICB9KTtcbiAgICB9IC8vIGFkZCBiYWNrIGxpc3RlbmVyc1xuXG5cbiAgICB0aGlzLl9hZGRMaXN0ZW5lcnModGhpcy52aWV3RWwpO1xuXG4gICAgdGhpcy5fYWRkUmVzaXplT2JzZXJ2ZXIoKTtcblxuICAgIHRoaXMuX3NldFZpZXdTaXplKCk7XG4gIH1cbiAgLyoqXHJcbiAgICogRGlzYWJsZSBhbGwgcGFyYWxsYXggZWxlbWVudHNcclxuICAgKi9cbiAgO1xuXG4gIF9wcm90by5kaXNhYmxlQWxsRWxlbWVudHMgPSBmdW5jdGlvbiBkaXNhYmxlQWxsRWxlbWVudHMoKSB7XG4gICAgY29uc29sZS53YXJuKCdkZXByZWNhdGVkOiB1c2UgZGlzYWJsZVBhcmFsbGF4Q29udHJvbGxlcigpIGluc3RlYWQnKTtcblxuICAgIGlmICh0aGlzLmVsZW1lbnRzKSB7XG4gICAgICB0aGlzLmVsZW1lbnRzID0gdGhpcy5lbGVtZW50cy5tYXAoZnVuY3Rpb24gKGVsKSB7XG4gICAgICAgIHJldHVybiBlbC51cGRhdGVQcm9wcyh7XG4gICAgICAgICAgZGlzYWJsZWQ6IHRydWVcbiAgICAgICAgfSk7XG4gICAgICB9KTtcbiAgICB9XG5cbiAgICB0aGlzLnVwZGF0ZSgpO1xuICB9XG4gIC8qKlxyXG4gICAqIEVuYWJsZSBhbGwgcGFyYWxsYXggZWxlbWVudHNcclxuICAgKi9cbiAgO1xuXG4gIF9wcm90by5lbmFibGVBbGxFbGVtZW50cyA9IGZ1bmN0aW9uIGVuYWJsZUFsbEVsZW1lbnRzKCkge1xuICAgIGNvbnNvbGUud2FybignZGVwcmVjYXRlZDogdXNlIGVuYWJsZVBhcmFsbGF4Q29udHJvbGxlcigpIGluc3RlYWQnKTtcblxuICAgIGlmICh0aGlzLmVsZW1lbnRzKSB7XG4gICAgICB0aGlzLmVsZW1lbnRzID0gdGhpcy5lbGVtZW50cy5tYXAoZnVuY3Rpb24gKGVsKSB7XG4gICAgICAgIHJldHVybiBlbC51cGRhdGVQcm9wcyh7XG4gICAgICAgICAgZGlzYWJsZWQ6IGZhbHNlXG4gICAgICAgIH0pO1xuICAgICAgfSk7XG4gICAgfVxuXG4gICAgdGhpcy51cGRhdGUoKTtcbiAgfVxuICAvKipcclxuICAgKiBSZW1vdmVzIGFsbCBsaXN0ZW5lcnMgYW5kIHJlc2V0cyBhbGwgc3R5bGVzIG9uIG1hbmFnZWQgZWxlbWVudHMuXHJcbiAgICovXG4gIDtcblxuICBfcHJvdG8uZGVzdHJveSA9IGZ1bmN0aW9uIGRlc3Ryb3koKSB7XG4gICAgdGhpcy5fcmVtb3ZlTGlzdGVuZXJzKHRoaXMudmlld0VsKTtcblxuICAgIGlmICh0aGlzLmVsZW1lbnRzKSB7XG4gICAgICB0aGlzLmVsZW1lbnRzLmZvckVhY2goZnVuY3Rpb24gKGVsZW1lbnQpIHtcbiAgICAgICAgcmV0dXJuIHJlc2V0U3R5bGVzKGVsZW1lbnQpO1xuICAgICAgfSk7XG4gICAgfSAvLyBAdHMtZXhwZWN0LWVycm9yXG5cblxuICAgIHRoaXMuZWxlbWVudHMgPSB1bmRlZmluZWQ7XG4gIH07XG5cbiAgcmV0dXJuIFBhcmFsbGF4Q29udHJvbGxlcjtcbn0oKTtcblxuZXhwb3J0IHsgRWFzaW5nUHJlc2V0LCBFbGVtZW50LCBMaW1pdHMsIFBhcmFsbGF4Q29udHJvbGxlciwgUmVjdCwgUm90YXRpb25Vbml0cywgU2NhbGVVbml0cywgU2Nyb2xsLCBTY3JvbGxBeGlzLCBVbml0cywgVmFsaWRDU1NFZmZlY3RzLCBWaWV3LCBjcmVhdGVJZCwgZ2V0UHJvZ3Jlc3NBbW91bnQsIGlzRWxlbWVudEluVmlldywgcGFyc2VFbGVtZW50VHJhbnNpdGlvbkVmZmVjdHMsIHBhcnNlVmFsdWVBbmRVbml0LCByZXNldFN0eWxlcywgc2NhbGVCZXR3ZWVuLCBzY2FsZUVmZmVjdEJ5UHJvZ3Jlc3MsIHNldEVsZW1lbnRTdHlsZXMsIHRlc3RGb3JQYXNzaXZlU2Nyb2xsIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYXJhbGxheC1jb250cm9sbGVyLmVzbS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/parallax-controller/dist/parallax-controller.esm.js\n");

/***/ })

};
;