{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/assessment/[domain]", "regex": "^/assessment/([^/]+?)(?:/)?$", "routeKeys": {"nxtPdomain": "nxtPdomain"}, "namedRegex": "^/assessment/(?<nxtPdomain>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin/counselors", "regex": "^/admin/counselors(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/counselors(?:/)?$"}, {"page": "/admin/dashboard", "regex": "^/admin/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/dashboard(?:/)?$"}, {"page": "/admin/questions", "regex": "^/admin/questions(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/questions(?:/)?$"}, {"page": "/admin/register", "regex": "^/admin/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/register(?:/)?$"}, {"page": "/admin/users", "regex": "^/admin/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/users(?:/)?$"}, {"page": "/counselor/dashboard", "regex": "^/counselor/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/counselor/dashboard(?:/)?$"}, {"page": "/counselor/register", "regex": "^/counselor/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/counselor/register(?:/)?$"}, {"page": "/couple/dashboard", "regex": "^/couple/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/couple/dashboard(?:/)?$"}, {"page": "/couple/results", "regex": "^/couple/results(?:/)?$", "routeKeys": {}, "namedRegex": "^/couple/results(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}