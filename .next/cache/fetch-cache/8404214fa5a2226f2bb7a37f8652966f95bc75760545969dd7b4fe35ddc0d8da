{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94acb5d2bfb6de08-SIN", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json", "date": "Thu, 05 Jun 2025 03:51:13 GMT", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=tqF5qseXN7Hck.****************************************-*******-K0VHrXQatFRGWDZ6zC0ho4N69tzFis..Dhx1i4akjS8AaIpSFIHLEBTgixqp2zxSjRa3j5ok.qtf4UzazD_GyGf1SuDwDqVMStNZV8iIN74; path=/; expires=Thu, 05-Jun-25 04:21:13 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Origin, Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "5"}, "body": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/auth/v1/user"}, "revalidate": 31536000, "tags": []}