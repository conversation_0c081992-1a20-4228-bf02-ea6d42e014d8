{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94a4fb9a6ab15f69-SIN", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json", "date": "Wed, 04 Jun 2025 05:20:45 GMT", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=hmEVW5LkFRMLm2kcuiRcK.2hfIoF4j2zCcVGmpgs48w-1749014445-*******-wv9kzOqVXVAQkC23QIBmLOjxcVOxvmtzKv9QjMTP70bAHFLvj7fFoI0LbwkR7yWh0UsG6EuH3wYoYgQt_kqFBMymSqksl8KXeW2kkfFVGd4; path=/; expires=Wed, 04-Jun-25 05:50:45 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Origin, Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/auth/v1/user"}, "revalidate": 31536000, "tags": []}