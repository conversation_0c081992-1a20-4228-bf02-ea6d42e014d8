{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94a4fb9b3b785f69-SIN", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/profiles?id=eq.3dccb116-689f-4d35-86ee-096970fe9480&select=full_name%2Cemail", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Wed, 04 Jun 2025 05:20:45 GMT", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=QVc0MloEdabBFypDHTEECykrPNIENWoy0tgm8olizxU-1749014445-*******-K53OIVQNQUPEjvskAcE5vdBuZwBI4tWy3xtcx77azMPxZ1gLlmvEYcjUrPOv9E4WJyE._S10yxS9wZeGrSC8zpWY0lFCJbPTXmcKZFMP2HI; path=/; expires=Wed, 04-Jun-25 05:50:45 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "************************************************************************************", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/rest/v1/profiles?select=full_name%2Cemail&id=eq.3dccb116-689f-4d35-86ee-096970fe9480"}, "revalidate": 31536000, "tags": []}