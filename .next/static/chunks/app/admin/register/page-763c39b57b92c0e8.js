(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[680],{4200:function(e,r,t){Promise.resolve().then(t.bind(t,1442))},9322:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});let n=(0,t(9205).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},1817:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});let n=(0,t(9205).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},8728:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});let n=(0,t(9205).Z)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9376:function(e,r,t){"use strict";var n=t(5475);t.o(n,"useParams")&&t.d(r,{useParams:function(){return n.useParams}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}})},1442:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return x}});var n=t(7437),a=t(2265),s=t(9376),i=t(9820),l=t(2381),o=t(279),d=t(5060),c=t(8728),u=t(9322),f=t(1817),m=t(7648);function x(){let e=(0,s.useRouter)(),[r,t]=(0,a.useState)(""),[x,h]=(0,a.useState)(""),[p,v]=(0,a.useState)(""),[g,y]=(0,a.useState)(!1),[b,j]=(0,a.useState)(null),w=async t=>{t.preventDefault(),y(!0),j(null);try{if("MarriageMapAdmin"!==p)throw Error("Invalid admin code");let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r,password:x,role:"admin"})}),n=await t.json();if(!t.ok)throw Error(n.error||"Admin registration failed");e.push("/login?role=admin")}catch(e){console.error("Admin registration error:",e),j(e instanceof Error?e.message:"An error occurred during admin registration")}finally{y(!1)}};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,n.jsxs)("div",{className:"w-full max-w-md",children:[(0,n.jsx)("div",{className:"flex justify-center mb-8",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(c.Z,{className:"h-8 w-8 text-primary"}),(0,n.jsx)("span",{className:"text-2xl font-bold",children:"Admin Registration"})]})}),(0,n.jsxs)(i.Zb,{children:[(0,n.jsxs)(i.Ol,{children:[(0,n.jsx)(i.ll,{children:"Register as Admin"}),(0,n.jsx)(i.SZ,{children:"Create an administrator account (requires authorization code)"})]}),(0,n.jsxs)("form",{onSubmit:w,children:[(0,n.jsxs)(i.aY,{className:"space-y-4",children:[b&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(u.Z,{className:"h-4 w-4 mr-2"}),b]})}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(d._,{htmlFor:"email",children:"Email"}),(0,n.jsx)(o.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:r,onChange:e=>t(e.target.value),required:!0})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(d._,{htmlFor:"password",children:"Password"}),(0,n.jsx)(o.I,{id:"password",type:"password",placeholder:"••••••••",value:x,onChange:e=>h(e.target.value),required:!0})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(d._,{htmlFor:"admin-code",children:"Admin Authorization Code"}),(0,n.jsx)(o.I,{id:"admin-code",type:"password",placeholder:"Enter admin code",value:p,onChange:e=>v(e.target.value),required:!0})]})]}),(0,n.jsxs)(i.eW,{className:"flex flex-col space-y-4",children:[(0,n.jsx)(l.z,{type:"submit",className:"w-full",disabled:g,children:g?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(f.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating admin account..."]}):"Register as Admin"}),(0,n.jsx)("div",{className:"text-center w-full",children:(0,n.jsx)(m.default,{href:"/login",className:"text-sm text-primary hover:underline",children:"Back to Login"})})]})]})]})]})})}},2381:function(e,r,t){"use strict";t.d(r,{z:function(){return d}});var n=t(7437),a=t(2265),s=t(7495),i=t(535),l=t(3448);let o=(0,i.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,r)=>{let{className:t,variant:a,size:i,asChild:d=!1,...c}=e,u=d?s.g7:"button";return(0,n.jsx)(u,{className:(0,l.cn)(o({variant:a,size:i,className:t})),ref:r,...c})});d.displayName="Button"},9820:function(e,r,t){"use strict";t.d(r,{Ol:function(){return l},SZ:function(){return d},Zb:function(){return i},aY:function(){return c},eW:function(){return u},ll:function(){return o}});var n=t(7437),a=t(2265),s=t(3448);let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...a})});i.displayName="Card";let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",t),...a})});l.displayName="CardHeader";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("h3",{ref:r,className:(0,s.cn)("font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent";let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,s.cn)(" flex items-center p-6 pt-0",t),...a})});u.displayName="CardFooter"},279:function(e,r,t){"use strict";t.d(r,{I:function(){return i}});var n=t(7437),a=t(2265),s=t(3448);let i=a.forwardRef((e,r)=>{let{className:t,type:a,...i}=e;return(0,n.jsx)("input",{type:a,className:(0,s.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...i})});i.displayName="Input"},5060:function(e,r,t){"use strict";t.d(r,{_:function(){return d}});var n=t(7437),a=t(2265),s=t(6394),i=t(535),l=t(3448);let o=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)(s.f,{ref:r,className:(0,l.cn)(o(),t),...a})});d.displayName=s.f.displayName},3448:function(e,r,t){"use strict";t.d(r,{cn:function(){return s}});var n=t(1994),a=t(3335);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.m6)((0,n.W)(r))}},6394:function(e,r,t){"use strict";t.d(r,{f:function(){return l}});var n=t(2265),a=t(6840),s=t(7437),i=n.forwardRef((e,r)=>(0,s.jsx)(a.WV.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null===(t=e.onMouseDown)||void 0===t||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var l=i},6840:function(e,r,t){"use strict";t.d(r,{WV:function(){return l},jH:function(){return o}});var n=t(2265),a=t(4887),s=t(7495),i=t(7437),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=n.forwardRef((e,t)=>{let{asChild:n,...a}=e,l=n?s.g7:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...a,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{});function o(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}}},function(e){e.O(0,[569,216,971,117,744],function(){return e(e.s=4200)}),_N_E=e.O()}]);