(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[743],{8482:function(e,t,i){Promise.resolve().then(i.bind(i,5505))},5505:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return q}});var s=i(7437),r=i(2265),n=i(9376),a=i(2381),o=i(279),d=i(3675),l=i(653),c=i(7864),u=i(3448);let f=c.fC;c.ZA;let m=c.B4,p=r.forwardRef((e,t)=>{let{className:i,children:r,...n}=e;return(0,s.jsxs)(c.xz,{ref:t,className:(0,u.cn)("flex h-9 w-full items-center justify-between rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50",i),...n,children:[r,(0,s.jsx)(c.JO,{asChild:!0,children:(0,s.jsx)(l.jnn,{className:"h-4 w-4 opacity-50"})})]})});p.displayName=c.xz.displayName;let h=r.forwardRef((e,t)=>{let{className:i,children:r,position:n="popper",...a}=e;return(0,s.jsx)(c.h_,{children:(0,s.jsx)(c.VY,{ref:t,className:(0,u.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",i),position:n,...a,children:(0,s.jsx)(c.l_,{className:(0,u.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r})})})});h.displayName=c.VY.displayName,r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)(c.__,{ref:t,className:(0,u.cn)("px-2 py-1.5 text-sm font-semibold",i),...r})}).displayName=c.__.displayName;let x=r.forwardRef((e,t)=>{let{className:i,children:r,...n}=e;return(0,s.jsxs)(c.ck,{ref:t,className:(0,u.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",i),...n,children:[(0,s.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(c.wU,{children:(0,s.jsx)(l.nQG,{className:"h-4 w-4"})})}),(0,s.jsx)(c.eT,{children:r})]})});x.displayName=c.ck.displayName,r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)(c.Z0,{ref:t,className:(0,u.cn)("-mx-1 my-1 h-px bg-muted",i),...r})}).displayName=c.Z0.displayName;var g=i(9270);let y=r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)(g.fC,{ref:t,className:(0,u.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",i),...r,children:(0,s.jsx)(g.z$,{className:(0,u.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(l.nQG,{className:"h-4 w-4"})})})});y.displayName=g.fC.displayName;var j=i(5060),v=i(9820),b=i(7168),w=i(933),N=i(8933);function q(){let e=(0,n.useRouter)(),[t,i]=(0,r.useState)({}),[l,c]=(0,r.useState)(!0),[u,g]=(0,r.useState)(null),[q,C]=(0,r.useState)("finances"),[k,I]=(0,r.useState)(!1),[E,S]=(0,r.useState)(null),[z,R]=(0,r.useState)({id:"",domain:"finances",type:"multiple-choice",text:"",options:["","","",""],required:!0});(0,r.useEffect)(()=>{(async()=>{try{let t=(0,N.eI)(),{data:{user:i}}=await t.auth.getUser();if(!i){e.push("/");return}let{data:s,error:r}=await t.from("admin_users").select("*").eq("user_id",i.id).single();r||!s?(I(!1),e.push("/dashboard")):(I(!0),_())}catch(t){console.error("Error checking admin status:",t),e.push("/dashboard")}})()},[e]);let _=async()=>{try{c(!0),g(null);let e=await (0,w.xz)();i(e)}catch(e){console.error("Error loading questions:",e),g("Failed to load questions")}finally{c(!1)}},V=(e,t)=>{R({...z,[e]:t})},Z=(e,t)=>{let i=[...z.options];i[e]=t,R({...z,options:i})},O=e=>{let t=z.options.filter((t,i)=>i!==e);R({...z,options:t})},F=e=>{S(e),R({id:e.id,domain:e.domain,type:e.type,text:e.text,options:e.options||["","","",""],required:e.required||!1})},Q=async()=>{try{if(!z.id||!z.text||!z.domain){g("Please fill in all required fields");return}if(["multiple-choice","scenario","ranking"].includes(z.type)&&(!z.options||z.options.filter(e=>e.trim()).length<2)){g("Please provide at least 2 options");return}let e={id:z.id,domain:z.domain,type:z.type,text:z.text,options:["multiple-choice","scenario","ranking"].includes(z.type)?z.options.filter(e=>e.trim()):void 0,required:z.required};E?await (0,w.Ds)(e):await (0,w.xL)(e),Y(),await _()}catch(e){console.error("Error saving question:",e),g("Failed to save question")}},A=async e=>{if(confirm("Are you sure you want to delete this question?"))try{await (0,w.Km)(e),await _()}catch(e){console.error("Error deleting question:",e),g("Failed to delete question")}},Y=()=>{S(null),R({id:"",domain:q,type:"multiple-choice",text:"",options:["","","",""],required:!0}),g(null)};return k?(0,s.jsxs)("div",{className:"container py-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Assessment Questions Management"}),u&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:u}),(0,s.jsxs)(b.mQ,{defaultValue:"view",className:"w-full",children:[(0,s.jsxs)(b.dr,{className:"mb-4",children:[(0,s.jsx)(b.SP,{value:"view",children:"View Questions"}),(0,s.jsx)(b.SP,{value:"add",children:"Add/Edit Question"})]}),(0,s.jsx)(b.nU,{value:"view",children:(0,s.jsxs)(v.Zb,{children:[(0,s.jsxs)(v.Ol,{children:[(0,s.jsx)(v.ll,{children:"Assessment Questions"}),(0,s.jsx)(v.SZ,{children:"Select a domain to view its questions"}),(0,s.jsxs)(f,{value:q,onValueChange:C,children:[(0,s.jsx)(p,{className:"w-[180px]",children:(0,s.jsx)(m,{placeholder:"Select domain"})}),(0,s.jsx)(h,{children:Object.keys(t).map(e=>(0,s.jsx)(x,{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))})]})]}),(0,s.jsx)(v.aY,{children:l?(0,s.jsx)("p",{children:"Loading questions..."}):t[q]&&t[q].length>0?(0,s.jsx)("div",{className:"space-y-4",children:t[q].map(e=>(0,s.jsxs)(v.Zb,{className:"border border-gray-200",children:[(0,s.jsxs)(v.Ol,{className:"pb-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsx)(v.ll,{className:"text-lg",children:e.text}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(a.z,{variant:"outline",size:"sm",onClick:()=>F(e),children:"Edit"}),(0,s.jsx)(a.z,{variant:"destructive",size:"sm",onClick:()=>A(e.id),children:"Delete"})]})]}),(0,s.jsxs)(v.SZ,{children:["ID: ",e.id," | Type: ",e.type," | Required:"," ",e.required?"Yes":"No"]})]}),(0,s.jsx)(v.aY,{children:e.options&&e.options.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium mb-1",children:"Options:"}),(0,s.jsx)("ul",{className:"list-disc pl-5",children:e.options.map((e,t)=>(0,s.jsx)("li",{children:e},t))})]})})]},e.id))}):(0,s.jsx)("p",{children:"No questions found for this domain."})})]})}),(0,s.jsx)(b.nU,{value:"add",children:(0,s.jsxs)(v.Zb,{children:[(0,s.jsxs)(v.Ol,{children:[(0,s.jsx)(v.ll,{children:E?"Edit Question":"Add New Question"}),(0,s.jsx)(v.SZ,{children:E?"Editing question ID: ".concat(E.id):"Create a new assessment question"})]}),(0,s.jsx)(v.aY,{children:(0,s.jsxs)("form",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(j._,{htmlFor:"id",children:"Question ID"}),(0,s.jsx)(o.I,{id:"id",placeholder:"e.g., finances_q5",value:z.id,onChange:e=>V("id",e.target.value),disabled:!!E})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(j._,{htmlFor:"domain",children:"Domain"}),(0,s.jsxs)(f,{value:z.domain,onValueChange:e=>V("domain",e),children:[(0,s.jsx)(p,{id:"domain",children:(0,s.jsx)(m,{placeholder:"Select domain"})}),(0,s.jsxs)(h,{children:[(0,s.jsx)(x,{value:"finances",children:"Finances"}),(0,s.jsx)(x,{value:"vision",children:"Vision"}),(0,s.jsx)(x,{value:"parenting",children:"Parenting"}),(0,s.jsx)(x,{value:"communication",children:"Communication"}),(0,s.jsx)(x,{value:"roles",children:"Roles"}),(0,s.jsx)(x,{value:"sexuality",children:"Sexuality"}),(0,s.jsx)(x,{value:"spirituality",children:"Spirituality"}),(0,s.jsx)(x,{value:"darkside",children:"Dark Side"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(j._,{htmlFor:"type",children:"Question Type"}),(0,s.jsxs)(f,{value:z.type,onValueChange:e=>V("type",e),children:[(0,s.jsx)(p,{id:"type",children:(0,s.jsx)(m,{placeholder:"Select type"})}),(0,s.jsxs)(h,{children:[(0,s.jsx)(x,{value:"multiple-choice",children:"Multiple Choice"}),(0,s.jsx)(x,{value:"scenario",children:"Scenario"}),(0,s.jsx)(x,{value:"ranking",children:"Ranking"}),(0,s.jsx)(x,{value:"open-ended",children:"Open Ended"}),(0,s.jsx)(x,{value:"scale",children:"Scale"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(j._,{htmlFor:"text",children:"Question Text"}),(0,s.jsx)(d.g,{id:"text",placeholder:"Enter the question text",value:z.text,onChange:e=>V("text",e.target.value),rows:3})]}),["multiple-choice","scenario","ranking"].includes(z.type)&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(j._,{children:"Options"}),(0,s.jsx)(a.z,{type:"button",variant:"outline",size:"sm",onClick:()=>{R({...z,options:[...z.options,""]})},children:"Add Option"})]}),z.options.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(o.I,{placeholder:"Option ".concat(t+1),value:e,onChange:e=>Z(t,e.target.value)}),(0,s.jsx)(a.z,{type:"button",variant:"ghost",size:"sm",onClick:()=>O(t),disabled:z.options.length<=2,children:"Remove"})]},t))]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(y,{id:"required",checked:z.required,onCheckedChange:e=>V("required",!0===e)}),(0,s.jsx)(j._,{htmlFor:"required",children:"Required question"})]})]})}),(0,s.jsxs)(v.eW,{className:"flex justify-between",children:[(0,s.jsx)(a.z,{variant:"outline",onClick:Y,children:E?"Cancel":"Clear"}),(0,s.jsx)(a.z,{onClick:Q,children:E?"Update Question":"Add Question"})]})]})})]})]}):(0,s.jsx)("div",{className:"container py-8",children:"Checking permissions..."})}},2381:function(e,t,i){"use strict";i.d(t,{z:function(){return l}});var s=i(7437),r=i(2265),n=i(7495),a=i(535),o=i(3448);let d=(0,a.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=r.forwardRef((e,t)=>{let{className:i,variant:r,size:a,asChild:l=!1,...c}=e,u=l?n.g7:"button";return(0,s.jsx)(u,{className:(0,o.cn)(d({variant:r,size:a,className:i})),ref:t,...c})});l.displayName="Button"},9820:function(e,t,i){"use strict";i.d(t,{Ol:function(){return o},SZ:function(){return l},Zb:function(){return a},aY:function(){return c},eW:function(){return u},ll:function(){return d}});var s=i(7437),r=i(2265),n=i(3448);let a=r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",i),...r})});a.displayName="Card";let o=r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",i),...r})});o.displayName="CardHeader";let d=r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",i),...r})});d.displayName="CardTitle";let l=r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",i),...r})});l.displayName="CardDescription";let c=r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",i),...r})});c.displayName="CardContent";let u=r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)(" flex items-center p-6 pt-0",i),...r})});u.displayName="CardFooter"},279:function(e,t,i){"use strict";i.d(t,{I:function(){return a}});var s=i(7437),r=i(2265),n=i(3448);let a=r.forwardRef((e,t)=>{let{className:i,type:r,...a}=e;return(0,s.jsx)("input",{type:r,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",i),ref:t,...a})});a.displayName="Input"},5060:function(e,t,i){"use strict";i.d(t,{_:function(){return l}});var s=i(7437),r=i(2265),n=i(6394),a=i(535),o=i(3448);let d=(0,a.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)(n.f,{ref:t,className:(0,o.cn)(d(),i),...r})});l.displayName=n.f.displayName},7168:function(e,t,i){"use strict";i.d(t,{SP:function(){return l},dr:function(){return d},mQ:function(){return o},nU:function(){return c}});var s=i(7437),r=i(2265),n=i(271),a=i(3448);let o=n.fC,d=r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)(n.aV,{ref:t,className:(0,a.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",i),...r})});d.displayName=n.aV.displayName;let l=r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)(n.xz,{ref:t,className:(0,a.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",i),...r})});l.displayName=n.xz.displayName;let c=r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)(n.VY,{ref:t,className:(0,a.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",i),...r})});c.displayName=n.VY.displayName},3675:function(e,t,i){"use strict";i.d(t,{g:function(){return a}});var s=i(7437),r=i(2265),n=i(3448);let a=r.forwardRef((e,t)=>{let{className:i,...r}=e;return(0,s.jsx)("textarea",{className:(0,n.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",i),ref:t,...r})});a.displayName="Textarea"},933:function(e,t,i){"use strict";i.d(t,{Ds:function(){return o},Km:function(){return d},Z7:function(){return r},xL:function(){return a},xz:function(){return n}});var s=i(8933);async function r(e){let t=(0,s.eI)(),{data:i,error:r}=await t.from("assessment_questions").select("*").eq("domain",e.toLowerCase());if(r)throw console.error("Error fetching questions:",r),Error("Failed to fetch ".concat(e," questions"));return i.map(e=>({id:e.id,type:e.type,text:e.text,options:e.options,required:e.required,domain:e.domain,weight:e.weight,category:e.category}))}async function n(){let e=(0,s.eI)(),{data:t,error:i}=await e.from("assessment_questions").select("*");if(i)throw console.error("Error fetching all questions:",i),Error("Failed to fetch questions");let r={};return t.forEach(e=>{let t={id:e.id,type:e.type,text:e.text,options:e.options,required:e.required,domain:e.domain,weight:e.weight,category:e.category};r[e.domain]||(r[e.domain]=[]),r[e.domain].push(t)}),r}async function a(e){let t=(0,s.eI)(),{error:i}=await t.from("assessment_questions").insert([{id:e.id,domain:e.domain,type:e.type,text:e.text,options:e.options,required:e.required,weight:e.weight,category:e.category}]);if(i)throw console.error("Error adding question:",i),Error("Failed to add question")}async function o(e){let t=(0,s.eI)(),{error:i}=await t.from("assessment_questions").update({domain:e.domain,type:e.type,text:e.text,options:e.options,required:e.required,weight:e.weight,category:e.category}).eq("id",e.id);if(i)throw console.error("Error updating question:",i),Error("Failed to update question")}async function d(e){let t=(0,s.eI)(),{error:i}=await t.from("assessment_questions").delete().eq("id",e);if(i)throw console.error("Error deleting question:",i),Error("Failed to delete question")}},8933:function(e,t,i){"use strict";i.d(t,{eI:function(){return d},s3:function(){return l}});var s=i(3777),r=i(257);let n=null,a="https://eqghwtejdnzgopmcjlho.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY";if(r.env.SUPABASE_SERVICE_ROLE_KEY,!a||!o)throw Error("Missing Supabase environment variables");let d=()=>(n||(n=(0,s.eI)(a,o,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})),n),l=()=>d()},3448:function(e,t,i){"use strict";i.d(t,{cn:function(){return n}});var s=i(1994),r=i(3335);function n(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return(0,r.m6)((0,s.W)(t))}}},function(e){e.O(0,[310,569,466,513,499,971,117,744],function(){return e(e.s=8482)}),_N_E=e.O()}]);