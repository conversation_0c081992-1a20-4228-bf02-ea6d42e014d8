(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[674],{7859:function(e,t,r){Promise.resolve().then(r.bind(r,3058))},464:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(9205).Z)("chart-no-axes-column-increasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},9322:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(9205).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(9205).Z)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},8736:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(9205).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},1817:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(9205).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},3247:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(9205).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8728:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(9205).Z)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1884:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(9205).Z)("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7806:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(9205).Z)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},5805:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(9205).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9376:function(e,t,r){"use strict";var s=r(5475);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},3058:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return b}});var s=r(7437),a=r(2265),n=r(9376),l=r(9820),i=r(2381),d=r(279),c=r(8933),o=r(1817),u=r(8728),m=r(464),x=r(8736),f=r(5805),h=r(1884),p=r(91),y=r(7806),g=r(9322),v=r(3247),j=r(7648);function b(){let e=(0,n.useRouter)(),[t,r]=(0,a.useState)(!0),[b,N]=(0,a.useState)(null),[k,w]=(0,a.useState)([]),[Z,I]=(0,a.useState)(""),[M,S]=(0,a.useState)(!1);(0,a.useEffect)(()=>{(async()=>{try{let t=(0,c.s3)(),{data:{user:r}}=await t.auth.getUser();if(!r){e.push("/");return}let{data:s,error:a}=await t.from("admin_users").select("*").eq("user_id",r.id).single();a||!s?(S(!1),e.push("/dashboard")):(S(!0),C())}catch(t){console.error("Error checking admin status:",t),e.push("/dashboard")}finally{r(!1)}})()},[e]);let C=async()=>{try{let e=(0,c.s3)(),{data:t,error:r}=await e.from("profiles").select("*").order("created_at",{ascending:!1});if(r)throw r;let{data:s}=await e.from("admin_users").select("user_id"),{data:a}=await e.from("counselor_profiles").select("user_id"),n=new Set((null==s?void 0:s.map(e=>e.user_id))||[]),l=new Set((null==a?void 0:a.map(e=>e.user_id))||[]),i=null==t?void 0:t.map(e=>{let t="user";return n.has(e.id)?t="admin":l.has(e.id)&&(t="counselor"),{...e,role:t}});w(i||[])}catch(e){console.error("Error fetching users:",e),N("Failed to load users")}},z=k.filter(e=>{var t,r;return(null===(t=e.full_name)||void 0===t?void 0:t.toLowerCase().includes(Z.toLowerCase()))||(null===(r=e.email)||void 0===r?void 0:r.toLowerCase().includes(Z.toLowerCase()))});return t?(0,s.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,s.jsx)(o.Z,{className:"h-8 w-8 animate-spin text-primary"}),(0,s.jsx)("span",{className:"ml-2",children:"Loading users..."})]}):M?(0,s.jsx)("div",{className:"min-h-screen bg-background",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"hidden md:flex w-64 flex-col fixed inset-y-0 z-50 border-r bg-background",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-6 p-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 px-2 py-4",children:[(0,s.jsx)(u.Z,{className:"h-6 w-6 text-primary"}),(0,s.jsx)("span",{className:"text-xl font-bold",children:"Admin Panel"})]}),(0,s.jsxs)("nav",{className:"flex flex-col space-y-1",children:[(0,s.jsxs)(j.default,{href:"/admin/dashboard",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,s.jsx)(m.Z,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Dashboard"})]}),(0,s.jsxs)(j.default,{href:"/admin/questions",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,s.jsx)(x.Z,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Questions"})]}),(0,s.jsxs)(j.default,{href:"/admin/users",className:"flex items-center gap-3 rounded-lg bg-primary/10 px-3 py-2 text-primary transition-all hover:text-primary",children:[(0,s.jsx)(f.Z,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Users"})]}),(0,s.jsxs)(j.default,{href:"/admin/counselors",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,s.jsx)(h.Z,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Counselors"})]}),(0,s.jsxs)(j.default,{href:"/admin/database",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,s.jsx)(p.Z,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Database"})]})]})]})}),(0,s.jsx)("div",{className:"flex-1 md:pl-64",children:(0,s.jsxs)("div",{className:"container py-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"User Management"}),(0,s.jsx)(j.default,{href:"/admin/register",children:(0,s.jsxs)(i.z,{className:"flex items-center gap-2",children:[(0,s.jsx)(y.Z,{className:"h-4 w-4"}),"Add Admin User"]})})]}),b&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(g.Z,{className:"h-4 w-4 mr-2"}),b]})}),(0,s.jsx)(l.Zb,{className:"mb-6",children:(0,s.jsx)(l.aY,{className:"pt-6",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(v.Z,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(d.I,{type:"search",placeholder:"Search users by name or email...",className:"pl-8",value:Z,onChange:e=>I(e.target.value)})]})})}),(0,s.jsxs)(l.Zb,{children:[(0,s.jsxs)(l.Ol,{children:[(0,s.jsx)(l.ll,{children:"Users"}),(0,s.jsx)(l.SZ,{children:"Manage all users in the system"})]}),(0,s.jsx)(l.aY,{children:(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-border",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"bg-muted/50",children:[(0,s.jsx)("th",{className:"px-4 py-3.5 text-left text-sm font-semibold",children:"Name"}),(0,s.jsx)("th",{className:"px-4 py-3.5 text-left text-sm font-semibold",children:"Email"}),(0,s.jsx)("th",{className:"px-4 py-3.5 text-left text-sm font-semibold",children:"Role"}),(0,s.jsx)("th",{className:"px-4 py-3.5 text-right text-sm font-semibold",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"divide-y divide-border",children:z.length>0?z.map(e=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-4 py-4 text-sm",children:e.full_name}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm",children:e.email}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm",children:(0,s.jsx)("span",{className:"inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ".concat("admin"===e.role?"bg-purple-100 text-purple-800":"counselor"===e.role?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"),children:e.role})}),(0,s.jsx)("td",{className:"px-4 py-4 text-sm text-right",children:(0,s.jsx)(i.z,{variant:"ghost",size:"sm",onClick:()=>{},children:"View"})})]},e.id)):(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:4,className:"px-4 py-8 text-center text-muted-foreground",children:"No users found"})})})]})})})]})]})})]})}):(0,s.jsx)("div",{className:"container py-8",children:"Checking permissions..."})}},2381:function(e,t,r){"use strict";r.d(t,{z:function(){return c}});var s=r(7437),a=r(2265),n=r(7495),l=r(535),i=r(3448);let d=(0,l.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:c=!1,...o}=e,u=c?n.g7:"button";return(0,s.jsx)(u,{className:(0,i.cn)(d({variant:a,size:l,className:r})),ref:t,...o})});c.displayName="Button"},9820:function(e,t,r){"use strict";r.d(t,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return l},aY:function(){return o},eW:function(){return u},ll:function(){return d}});var s=r(7437),a=r(2265),n=r(3448);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...a})});l.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",r),...a})});d.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});o.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)(" flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},279:function(e,t,r){"use strict";r.d(t,{I:function(){return l}});var s=r(7437),a=r(2265),n=r(3448);let l=a.forwardRef((e,t)=>{let{className:r,type:a,...l}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...l})});l.displayName="Input"},8933:function(e,t,r){"use strict";r.d(t,{eI:function(){return d},s3:function(){return c}});var s=r(3777),a=r(257);let n=null,l="https://eqghwtejdnzgopmcjlho.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY";if(a.env.SUPABASE_SERVICE_ROLE_KEY,!l||!i)throw Error("Missing Supabase environment variables");let d=()=>(n||(n=(0,s.eI)(l,i,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})),n),c=()=>d()},3448:function(e,t,r){"use strict";r.d(t,{cn:function(){return n}});var s=r(1994),a=r(3335);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,s.W)(t))}}},function(e){e.O(0,[569,466,216,971,117,744],function(){return e(e.s=7859)}),_N_E=e.O()}]);