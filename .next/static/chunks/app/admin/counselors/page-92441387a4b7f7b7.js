(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[761],{6084:function(e,s,t){Promise.resolve().then(t.bind(t,3895))},464:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9205).Z)("chart-no-axes-column-increasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},9322:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9205).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9205).Z)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},8736:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9205).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},1817:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9205).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},3247:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9205).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8728:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9205).Z)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1884:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9205).Z)("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7806:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9205).Z)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},5805:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});let r=(0,t(9205).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9376:function(e,s,t){"use strict";var r=t(5475);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},3895:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return b}});var r=t(7437),n=t(2265),a=t(9376),l=t(9820),i=t(2381),c=t(279),d=t(8933),o=t(1817),u=t(8728),m=t(464),x=t(8736),f=t(5805),h=t(1884),p=t(91),y=t(7806),v=t(9322),g=t(3247),j=t(7648);function b(){let e=(0,a.useRouter)(),[s,t]=(0,n.useState)(!0),[b,N]=(0,n.useState)(null),[k,w]=(0,n.useState)([]),[Z,C]=(0,n.useState)(""),[I,M]=(0,n.useState)(!1);(0,n.useEffect)(()=>{(async()=>{try{let s=(0,d.s3)(),{data:{user:t}}=await s.auth.getUser();if(!t){e.push("/");return}let{data:r,error:n}=await s.from("admin_users").select("*").eq("user_id",t.id).single();n||!r?(M(!1),e.push("/dashboard")):(M(!0),S())}catch(s){console.error("Error checking admin status:",s),e.push("/dashboard")}finally{t(!1)}})()},[e]);let S=async()=>{try{let e=(0,d.s3)(),{data:s,error:t}=await e.from("counselor_profiles").select("*, profiles:user_id(*)").order("created_at",{ascending:!1});if(t)throw t;w(s||[])}catch(e){console.error("Error fetching counselors:",e),N("Failed to load counselors")}},z=k.filter(e=>{var s,t,r;return(null===(s=e.full_name)||void 0===s?void 0:s.toLowerCase().includes(Z.toLowerCase()))||(null===(r=e.profiles)||void 0===r?void 0:null===(t=r.email)||void 0===t?void 0:t.toLowerCase().includes(Z.toLowerCase()))});return s?(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,r.jsx)(o.Z,{className:"h-8 w-8 animate-spin text-primary"}),(0,r.jsx)("span",{className:"ml-2",children:"Loading counselors..."})]}):I?(0,r.jsx)("div",{className:"min-h-screen bg-background",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"hidden md:flex w-64 flex-col fixed inset-y-0 z-50 border-r bg-background",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-6 p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 px-2 py-4",children:[(0,r.jsx)(u.Z,{className:"h-6 w-6 text-primary"}),(0,r.jsx)("span",{className:"text-xl font-bold",children:"Admin Panel"})]}),(0,r.jsxs)("nav",{className:"flex flex-col space-y-1",children:[(0,r.jsxs)(j.default,{href:"/admin/dashboard",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,r.jsx)(m.Z,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Dashboard"})]}),(0,r.jsxs)(j.default,{href:"/admin/questions",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,r.jsx)(x.Z,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Questions"})]}),(0,r.jsxs)(j.default,{href:"/admin/users",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,r.jsx)(f.Z,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Users"})]}),(0,r.jsxs)(j.default,{href:"/admin/counselors",className:"flex items-center gap-3 rounded-lg bg-primary/10 px-3 py-2 text-primary transition-all hover:text-primary",children:[(0,r.jsx)(h.Z,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Counselors"})]}),(0,r.jsxs)(j.default,{href:"/admin/database",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,r.jsx)(p.Z,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Database"})]})]})]})}),(0,r.jsx)("div",{className:"flex-1 md:pl-64",children:(0,r.jsxs)("div",{className:"container py-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Counselor Management"}),(0,r.jsx)(j.default,{href:"/counselor/register",children:(0,r.jsxs)(i.z,{className:"flex items-center gap-2",children:[(0,r.jsx)(y.Z,{className:"h-4 w-4"}),"Add Counselor"]})})]}),b&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(v.Z,{className:"h-4 w-4 mr-2"}),b]})}),(0,r.jsx)(l.Zb,{className:"mb-6",children:(0,r.jsx)(l.aY,{className:"pt-6",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(g.Z,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(c.I,{type:"search",placeholder:"Search counselors by name or email...",className:"pl-8",value:Z,onChange:e=>C(e.target.value)})]})})}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[(0,r.jsx)(l.ll,{children:"Counselors"}),(0,r.jsx)(l.SZ,{children:"Manage all counselors in the system"})]}),(0,r.jsx)(l.aY,{children:(0,r.jsx)("div",{className:"rounded-md border",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-border",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"bg-muted/50",children:[(0,r.jsx)("th",{className:"px-4 py-3.5 text-left text-sm font-semibold",children:"Name"}),(0,r.jsx)("th",{className:"px-4 py-3.5 text-left text-sm font-semibold",children:"Email"}),(0,r.jsx)("th",{className:"px-4 py-3.5 text-left text-sm font-semibold",children:"Assigned Couples"}),(0,r.jsx)("th",{className:"px-4 py-3.5 text-right text-sm font-semibold",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"divide-y divide-border",children:z.length>0?z.map(e=>{var s;return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-4 py-4 text-sm",children:e.full_name}),(0,r.jsx)("td",{className:"px-4 py-4 text-sm",children:null===(s=e.profiles)||void 0===s?void 0:s.email}),(0,r.jsx)("td",{className:"px-4 py-4 text-sm",children:e.assigned_couples_count||0}),(0,r.jsx)("td",{className:"px-4 py-4 text-sm text-right",children:(0,r.jsx)(i.z,{variant:"ghost",size:"sm",onClick:()=>{},children:"View"})})]},e.id)}):(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:4,className:"px-4 py-8 text-center text-muted-foreground",children:"No counselors found"})})})]})})})]})]})})]})}):(0,r.jsx)("div",{className:"container py-8",children:"Checking permissions..."})}},2381:function(e,s,t){"use strict";t.d(s,{z:function(){return d}});var r=t(7437),n=t(2265),a=t(7495),l=t(535),i=t(3448);let c=(0,l.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,s)=>{let{className:t,variant:n,size:l,asChild:d=!1,...o}=e,u=d?a.g7:"button";return(0,r.jsx)(u,{className:(0,i.cn)(c({variant:n,size:l,className:t})),ref:s,...o})});d.displayName="Button"},9820:function(e,s,t){"use strict";t.d(s,{Ol:function(){return i},SZ:function(){return d},Zb:function(){return l},aY:function(){return o},eW:function(){return u},ll:function(){return c}});var r=t(7437),n=t(2265),a=t(3448);let l=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:s,className:(0,a.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...n})});l.displayName="Card";let i=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:s,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",t),...n})});i.displayName="CardHeader";let c=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("h3",{ref:s,className:(0,a.cn)("font-semibold leading-none tracking-tight",t),...n})});c.displayName="CardTitle";let d=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("p",{ref:s,className:(0,a.cn)("text-sm text-muted-foreground",t),...n})});d.displayName="CardDescription";let o=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:s,className:(0,a.cn)("p-6 pt-0",t),...n})});o.displayName="CardContent";let u=n.forwardRef((e,s)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:s,className:(0,a.cn)(" flex items-center p-6 pt-0",t),...n})});u.displayName="CardFooter"},279:function(e,s,t){"use strict";t.d(s,{I:function(){return l}});var r=t(7437),n=t(2265),a=t(3448);let l=n.forwardRef((e,s)=>{let{className:t,type:n,...l}=e;return(0,r.jsx)("input",{type:n,className:(0,a.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...l})});l.displayName="Input"},8933:function(e,s,t){"use strict";t.d(s,{eI:function(){return c},s3:function(){return d}});var r=t(3777),n=t(257);let a=null,l="https://eqghwtejdnzgopmcjlho.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY";if(n.env.SUPABASE_SERVICE_ROLE_KEY,!l||!i)throw Error("Missing Supabase environment variables");let c=()=>(a||(a=(0,r.eI)(l,i,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})),a),d=()=>c()},3448:function(e,s,t){"use strict";t.d(s,{cn:function(){return a}});var r=t(1994),n=t(3335);function a(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,r.W)(s))}}},function(e){e.O(0,[569,466,216,971,117,744],function(){return e(e.s=6084)}),_N_E=e.O()}]);