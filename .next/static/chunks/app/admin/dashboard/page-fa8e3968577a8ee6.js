(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[427],{2049:function(e,t,s){Promise.resolve().then(s.bind(s,6764))},464:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9205).Z)("chart-no-axes-column-increasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},9322:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9205).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9205).Z)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},8736:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9205).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},1817:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9205).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7692:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9205).Z)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},8728:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9205).Z)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1884:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9205).Z)("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},5805:function(e,t,s){"use strict";s.d(t,{Z:function(){return r}});let r=(0,s(9205).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9376:function(e,t,s){"use strict";var r=s(5475);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},6764:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return b}});var r=s(7437),a=s(2265),n=s(9376),i=s(9820),l=s(2381),o=s(7168),c=s(8933),d=s(1817),u=s(8728),m=s(464),h=s(8736),f=s(5805),x=s(1884),p=s(91),g=s(7692),y=s(9322),j=s(1399),v=s(7648);function b(){let e=(0,n.useRouter)(),[t,s]=(0,a.useState)(!0),[b,N]=(0,a.useState)(null),[k,w]=(0,a.useState)({totalUsers:0,totalCouples:0,totalCounselors:0,completedAssessments:0}),[Z,C]=(0,a.useState)(!1);(0,a.useEffect)(()=>{(async()=>{s(!0),N(null);try{let t=(0,c.s3)(),{data:{session:r},error:a}=await t.auth.getSession();if(a||!r){console.error("AdminDashboard: No active session or error fetching session.",a),e.push("/login?role=admin&error=session_expired");return}let n=await fetch("/api/auth",{headers:{Authorization:"Bearer ".concat(r.access_token)}});if(!n.ok){let e=await n.json().catch(()=>({error:"Failed to parse auth error"}));console.error("AdminDashboard: /api/auth call failed.",n.status,e.error),N("Authentication check failed: ".concat(e.error||n.statusText,".")),C(!1),s(!1);return}let i=await n.json();"admin"!==i.role?(console.log("AdminDashboard: User is not admin. Role:",i.role),C(!1),N("Access denied. You are not an administrator.")):(console.log("AdminDashboard: User is admin. Proceeding to fetch stats."),C(!0),await A(r.access_token))}catch(e){console.error("AdminDashboard: Error in checkAdminStatusAndLoadData:",e),N(e.message||"An unexpected error occurred while verifying admin status."),C(!1)}finally{s(!1)}})()},[e]);let A=async e=>{N(null);try{console.log("AdminDashboard: Calling /api/admin/stats with token.");let t=await fetch("/api/admin/stats",{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok){let e=await t.json().catch(()=>({error:"Failed to parse stats API error"}));throw console.error("AdminDashboard: Error fetching stats from /api/admin/stats:",t.status,e.error),Error(e.error||"Failed to fetch dashboard statistics (status: ".concat(t.status,")"))}let s=await t.json();console.log("AdminDashboard: Successfully fetched stats:",s),w(s)}catch(e){console.error("Error fetching dashboard stats:",e),N(e.message||"Failed to load dashboard statistics.")}finally{}};return t?(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,r.jsx)(d.Z,{className:"h-8 w-8 animate-spin text-primary"}),(0,r.jsx)("span",{className:"ml-2",children:"Loading admin dashboard..."})]}):Z?(0,r.jsx)("div",{className:"min-h-screen bg-background",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"hidden md:flex w-64 flex-col fixed inset-y-0 z-50 border-r bg-background",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-6 p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 px-2 py-4",children:[(0,r.jsx)(u.Z,{className:"h-6 w-6 text-primary"}),(0,r.jsx)("span",{className:"text-xl font-bold",children:"Admin Panel"})]}),(0,r.jsxs)("nav",{className:"flex flex-col space-y-1",children:[(0,r.jsxs)(v.default,{href:"/admin/dashboard",className:"flex items-center gap-3 rounded-lg bg-primary/10 px-3 py-2 text-primary transition-all hover:text-primary",children:[(0,r.jsx)(m.Z,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Dashboard"})]}),(0,r.jsxs)(v.default,{href:"/admin/questions",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,r.jsx)(h.Z,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Questions"})]}),(0,r.jsxs)(v.default,{href:"/admin/users",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,r.jsx)(f.Z,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Users"})]}),(0,r.jsxs)(v.default,{href:"/admin/counselors",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,r.jsx)(x.Z,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Counselors"})]}),(0,r.jsxs)(v.default,{href:"/admin/database",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,r.jsx)(p.Z,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Database"})]}),(0,r.jsxs)("button",{onClick:j.h,className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:bg-red-50 hover:text-red-600 w-full mt-4",children:[(0,r.jsx)(g.Z,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Logout"})]})]})]})}),(0,r.jsx)("div",{className:"flex-1 md:pl-64",children:(0,r.jsxs)("div",{className:"container py-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Admin Dashboard"}),b&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(y.Z,{className:"h-4 w-4 mr-2"}),b]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)(i.Zb,{children:[(0,r.jsx)(i.Ol,{className:"pb-2",children:(0,r.jsx)(i.ll,{className:"text-lg",children:"Total Users"})}),(0,r.jsx)(i.aY,{children:(0,r.jsx)("div",{className:"text-3xl font-bold",children:k.totalUsers})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsx)(i.Ol,{className:"pb-2",children:(0,r.jsx)(i.ll,{className:"text-lg",children:"Total Couples"})}),(0,r.jsx)(i.aY,{children:(0,r.jsx)("div",{className:"text-3xl font-bold",children:k.totalCouples})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsx)(i.Ol,{className:"pb-2",children:(0,r.jsx)(i.ll,{className:"text-lg",children:"Total Counselors"})}),(0,r.jsx)(i.aY,{children:(0,r.jsx)("div",{className:"text-3xl font-bold",children:k.totalCounselors})})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsx)(i.Ol,{className:"pb-2",children:(0,r.jsx)(i.ll,{className:"text-lg",children:"Completed Assessments"})}),(0,r.jsx)(i.aY,{children:(0,r.jsx)("div",{className:"text-3xl font-bold",children:k.completedAssessments})})]})]}),(0,r.jsxs)(i.Zb,{className:"mb-8",children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsx)(i.ll,{children:"Quick Actions"}),(0,r.jsx)(i.SZ,{children:"Common administrative tasks you can perform"})]}),(0,r.jsxs)(i.aY,{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)(l.z,{variant:"outline",className:"flex items-center justify-start gap-2",onClick:()=>e.push("/admin/questions"),children:[(0,r.jsx)(h.Z,{className:"h-4 w-4"}),"Manage Assessment Questions"]}),(0,r.jsxs)(l.z,{variant:"outline",className:"flex items-center justify-start gap-2",onClick:()=>e.push("/admin/users"),children:[(0,r.jsx)(f.Z,{className:"h-4 w-4"}),"Manage Users"]}),(0,r.jsxs)(l.z,{variant:"outline",className:"flex items-center justify-start gap-2",onClick:()=>e.push("/admin/counselors"),children:[(0,r.jsx)(x.Z,{className:"h-4 w-4"}),"Manage Counselors"]})]})]}),(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsx)(i.ll,{children:"Recent Activity"}),(0,r.jsx)(i.SZ,{children:"Latest actions and events in the system"})]}),(0,r.jsx)(i.aY,{children:(0,r.jsxs)(o.mQ,{defaultValue:"registrations",children:[(0,r.jsxs)(o.dr,{className:"mb-4",children:[(0,r.jsx)(o.SP,{value:"registrations",children:"New Registrations"}),(0,r.jsx)(o.SP,{value:"assessments",children:"Completed Assessments"}),(0,r.jsx)(o.SP,{value:"connections",children:"Couple Connections"})]}),(0,r.jsx)(o.nU,{value:"registrations",children:(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"Recent user registrations will appear here."})})}),(0,r.jsx)(o.nU,{value:"assessments",children:(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"Recently completed assessments will appear here."})})}),(0,r.jsx)(o.nU,{value:"connections",children:(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"Recent couple connections will appear here."})})})]})}),(0,r.jsx)(i.eW,{children:(0,r.jsx)(l.z,{variant:"outline",className:"w-full",children:"View All Activity"})})]})]})})]})}):(0,r.jsx)("div",{className:"container py-8",children:"Checking permissions..."})}},2381:function(e,t,s){"use strict";s.d(t,{z:function(){return c}});var r=s(7437),a=s(2265),n=s(7495),i=s(535),l=s(3448);let o=(0,i.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:s,variant:a,size:i,asChild:c=!1,...d}=e,u=c?n.g7:"button";return(0,r.jsx)(u,{className:(0,l.cn)(o({variant:a,size:i,className:s})),ref:t,...d})});c.displayName="Button"},9820:function(e,t,s){"use strict";s.d(t,{Ol:function(){return l},SZ:function(){return c},Zb:function(){return i},aY:function(){return d},eW:function(){return u},ll:function(){return o}});var r=s(7437),a=s(2265),n=s(3448);let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",s),...a})});i.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...a})});l.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",s),...a})});o.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...a})});d.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)(" flex items-center p-6 pt-0",s),...a})});u.displayName="CardFooter"},7168:function(e,t,s){"use strict";s.d(t,{SP:function(){return c},dr:function(){return o},mQ:function(){return l},nU:function(){return d}});var r=s(7437),a=s(2265),n=s(271),i=s(3448);let l=n.fC,o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.aV,{ref:t,className:(0,i.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",s),...a})});o.displayName=n.aV.displayName;let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.xz,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",s),...a})});c.displayName=n.xz.displayName;let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(n.VY,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...a})});d.displayName=n.VY.displayName},1399:function(e,t,s){"use strict";s.d(t,{h:function(){return a}});var r=s(8933);async function a(){try{let e=(0,r.s3)(),{error:t}=await e.auth.signOut();if(t)return console.error("Error signing out:",t),!1;return window.location.href="/login",!0}catch(e){return console.error("Unexpected error during logout:",e),!1}}},8933:function(e,t,s){"use strict";s.d(t,{eI:function(){return o},s3:function(){return c}});var r=s(3777),a=s(257);let n=null,i="https://eqghwtejdnzgopmcjlho.supabase.co",l="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY";if(a.env.SUPABASE_SERVICE_ROLE_KEY,!i||!l)throw Error("Missing Supabase environment variables");let o=()=>(n||(n=(0,r.eI)(i,l,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})),n),c=()=>o()},3448:function(e,t,s){"use strict";s.d(t,{cn:function(){return n}});var r=s(1994),a=s(3335);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.m6)((0,r.W)(t))}},271:function(e,t,s){"use strict";s.d(t,{VY:function(){return V},aV:function(){return R},fC:function(){return M},xz:function(){return z}});var r=s(2265),a=s(6741),n=s(3966),i=s(1353),l=s(1599),o=s(6840),c=s(9114),d=s(886),u=s(9255),m=s(7437),h="Tabs",[f,x]=(0,n.b)(h,[i.Pc]),p=(0,i.Pc)(),[g,y]=f(h),j=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:a,defaultValue:n,orientation:i="horizontal",dir:l,activationMode:h="automatic",...f}=e,x=(0,c.gm)(l),[p,y]=(0,d.T)({prop:r,onChange:a,defaultProp:n});return(0,m.jsx)(g,{scope:s,baseId:(0,u.M)(),value:p,onValueChange:y,orientation:i,dir:x,activationMode:h,children:(0,m.jsx)(o.WV.div,{dir:x,"data-orientation":i,...f,ref:t})})});j.displayName=h;var v="TabsList",b=r.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...a}=e,n=y(v,s),l=p(s);return(0,m.jsx)(i.fC,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:r,children:(0,m.jsx)(o.WV.div,{role:"tablist","aria-orientation":n.orientation,...a,ref:t})})});b.displayName=v;var N="TabsTrigger",k=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:n=!1,...l}=e,c=y(N,s),d=p(s),u=C(c.baseId,r),h=A(c.baseId,r),f=r===c.value;return(0,m.jsx)(i.ck,{asChild:!0,...d,focusable:!n,active:f,children:(0,m.jsx)(o.WV.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":h,"data-state":f?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...l,ref:t,onMouseDown:(0,a.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,a.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,a.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;f||n||!e||c.onValueChange(r)})})})});k.displayName=N;var w="TabsContent",Z=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,forceMount:n,children:i,...c}=e,d=y(w,s),u=C(d.baseId,a),h=A(d.baseId,a),f=a===d.value,x=r.useRef(f);return r.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(l.z,{present:n||f,children:s=>{let{present:r}=s;return(0,m.jsx)(o.WV.div,{"data-state":f?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:h,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:r&&i})}})});function C(e,t){return"".concat(e,"-trigger-").concat(t)}function A(e,t){return"".concat(e,"-content-").concat(t)}Z.displayName=w;var M=j,R=b,z=k,V=Z}},function(e){e.O(0,[569,466,216,513,971,117,744],function(){return e(e.s=2049)}),_N_E=e.O()}]);