(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[530],{1411:function(e,s,t){Promise.resolve().then(t.bind(t,973))},973:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return E}});var r=t(7437),i=t(2265),a=t(9376),n=t(9820),o=t(2381),l=t(279),d=t(3675),c=t(653),u=t(2325),m=t(3448);let h=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,r.jsx)(u.fC,{className:(0,m.cn)("grid gap-2",t),...i,ref:s})});h.displayName=u.fC.displayName;let f=i.forwardRef((e,s)=>{let{className:t,children:i,...a}=e;return(0,r.jsx)(u.ck,{ref:s,className:(0,m.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)(u.z$,{className:"flex items-center justify-center",children:(0,r.jsx)(c.nQG,{className:"h-3.5 w-3.5 fill-primary"})})})});f.displayName=u.ck.displayName;var x=t(5060),p=t(7204),g=t(2451),v=t(3229),y=t(407),w=t(9796),j=t(933),b=e=>{let{domain:s="keuangan",questions:t,onSubmit:a,onSave:c}=e,[u,m]=(0,i.useState)([]),[b,N]=(0,i.useState)(!1),[q,_]=(0,i.useState)(null);(0,i.useEffect)(()=>{if(t){m(t);return}(async()=>{try{N(!0),_(null);let e=await (0,j.Z7)(s);if(e&&e.length>0)m(e);else{let e=(0,w.gK)(s);e.length>0?m(e):m((0,w.gK)("keuangan"))}}catch(t){console.error("Error loading questions:",t),_("Failed to load questions. Using local fallback.");let e=(0,w.gK)(s);e.length>0?m(e):m((0,w.gK)("keuangan"))}finally{N(!1)}})()},[s,t]);let[E,C]=(0,i.useState)(0),[k,S]=(0,i.useState)({}),[I,F]=(0,i.useState)({}),L=u[E]||{id:"",type:"multiple-choice",text:"Loading..."},Z=u.length>0?(E+1)/u.length*100:0,z=()=>{let e=u.filter(e=>e.required).filter(e=>!k[e.id]);if(e.length>0){let s={};e.forEach(e=>{s[e.id]="This question requires an answer"}),F(s),C(u.findIndex(s=>s.id===e[0].id));return}a&&a(k)},R=(e,s)=>{if(S({...k,[e]:s}),I[e]){let s={...I};delete s[e],F(s)}};return(0,r.jsxs)(n.Zb,{className:"w-full max-w-4xl mx-auto bg-white",children:[(0,r.jsxs)(n.Ol,{children:[(0,r.jsxs)(n.ll,{className:"text-2xl",children:[s," Assessment"]}),(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)(p.E,{value:Z,className:"h-2"}),(0,r.jsxs)("div",{className:"flex justify-between mt-1 text-sm text-gray-500",children:[(0,r.jsxs)("span",{children:["Question ",E+1," of ",u.length]}),(0,r.jsxs)("span",{children:[Math.round(Z),"% Complete"]})]})]}),b&&(0,r.jsx)("p",{className:"text-sm text-blue-500 mt-2",children:"Loading questions..."}),q&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-2",children:q})]}),(0,r.jsx)(n.aY,{children:(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium",children:L.text}),I[L.id]&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:I[L.id]}),(()=>{var e,s,t,i;switch(L.type){case"multiple-choice":return(0,r.jsx)(h,{value:k[L.id]||"",onValueChange:e=>R(L.id,e),className:"space-y-3 mt-4",children:null===(e=L.options)||void 0===e?void 0:e.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(f,{value:e,id:"option-".concat(s)}),(0,r.jsx)(x._,{htmlFor:"option-".concat(s),children:e})]},s))});case"scale":return(0,r.jsx)(h,{value:k[L.id]||"",onValueChange:e=>R(L.id,e),className:"space-y-3 mt-4",children:null===(s=L.options)||void 0===s?void 0:s.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(f,{value:e,id:"scale-".concat(s)}),(0,r.jsx)(x._,{htmlFor:"scale-".concat(s),children:e})]},s))});case"scenario":return(0,r.jsx)(h,{value:k[L.id]||"",onValueChange:e=>R(L.id,e),className:"space-y-3 mt-4",children:null===(t=L.options)||void 0===t?void 0:t.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(f,{value:e,id:"scenario-".concat(s)}),(0,r.jsx)(x._,{htmlFor:"scenario-".concat(s),children:e})]},s))});case"ranking":return(0,r.jsx)("div",{className:"space-y-4 mt-4",children:null===(i=L.options)||void 0===i?void 0:i.map((e,s)=>{var t,i;return(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(l.I,{type:"number",min:"1",max:null===(t=L.options)||void 0===t?void 0:t.length,className:"w-16",value:(null===(i=k[L.id])||void 0===i?void 0:i[e])||"",onChange:s=>{let t=s.target.value,r=k[L.id]||{};R(L.id,{...r,[e]:t})}}),(0,r.jsx)(x._,{children:e})]})},s)})});case"open-ended":return(0,r.jsx)(d.g,{className:"mt-4",placeholder:"Type your answer here...",value:k[L.id]||"",onChange:e=>R(L.id,e.target.value),rows:5});default:return null}})()]})})}),(0,r.jsxs)(n.eW,{className:"flex justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsxs)(o.z,{variant:"outline",onClick:()=>{E>0&&C(E-1)},disabled:0===E,children:[(0,r.jsx)(g.Z,{className:"mr-2 h-4 w-4"}),"Previous"]})}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(o.z,{variant:"outline",onClick:()=>{c&&c(k)},children:[(0,r.jsx)(v.Z,{className:"mr-2 h-4 w-4"}),"Save Progress"]}),(0,r.jsx)(o.z,{onClick:()=>{if(u.length>0&&L.required&&!k[L.id]){F({...I,[L.id]:"This question requires an answer"});return}if(I[L.id]){let e={...I};delete e[L.id],F(e)}E<u.length-1?C(E+1):z()},children:E<u.length-1?(0,r.jsxs)(r.Fragment,{children:["Next",(0,r.jsx)(y.Z,{className:"ml-2 h-4 w-4"})]}):"Submit"})]})]})]})},N=t(2660),q=t(8933),_=t(9540);function E(){let e=(0,a.useParams)(),s=(0,a.useRouter)(),t=e.domain,[n,l]=(0,i.useState)(!1),[d,c]=(0,i.useState)(null),u=async e=>{try{l(!0);let r=(0,q.s3)(),{data:{user:i}}=await r.auth.getUser();if(!i)throw Error("User not authenticated");let a=Object.entries(e).map(e=>{let[s,r]=e;return{questionId:s,answer:r,domain:t}}),n=(0,_.yI)(t,a),o=n.score,{data:d}=await r.from("individual_results").select("*").eq("user_id",i.id).single();if(d){let s=[...d.domains],i=s.findIndex(e=>e.domain.toLowerCase()===t.toLowerCase());i>=0?s[i]={...s[i],score:o,responses:e,subcategories:n.subcategories}:s.push({domain:(0,_.bc)(t),score:o,responses:e,subcategories:n.subcategories});let a=Math.round(s.reduce((e,s)=>e+s.score,0)/s.length);await r.from("individual_results").update({domains:s,overall_score:a,updated_at:new Date().toISOString()}).eq("id",d.id)}else await r.from("individual_results").insert({user_id:i.id,domains:[{domain:(0,_.bc)(t),score:o,responses:e,subcategories:n.subcategories}],overall_score:o});s.push("/couple/dashboard")}catch(e){console.error("Error saving assessment:",e),c(e instanceof Error?e.message:"An error occurred while saving your assessment")}finally{l(!1)}},m=async e=>{alert("Progress saved!")};return(0,r.jsxs)("div",{className:"container py-8",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)(o.z,{variant:"ghost",onClick:()=>s.push("/dashboard"),className:"mb-4",children:[(0,r.jsx)(N.Z,{className:"mr-2 h-4 w-4"}),"Back to Dashboard"]}),(0,r.jsxs)("h1",{className:"text-3xl font-bold",children:[(0,_.bc)(t)," Assessment"]}),(0,r.jsxs)("p",{className:"text-muted-foreground",children:["Complete the following questions to assess your ",t.toLowerCase()," ","compatibility."]})]}),d&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:d}),(0,r.jsx)(b,{domain:t,onSubmit:u,onSave:m})]})}},279:function(e,s,t){"use strict";t.d(s,{I:function(){return n}});var r=t(7437),i=t(2265),a=t(3448);let n=i.forwardRef((e,s)=>{let{className:t,type:i,...n}=e;return(0,r.jsx)("input",{type:i,className:(0,a.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...n})});n.displayName="Input"},5060:function(e,s,t){"use strict";t.d(s,{_:function(){return d}});var r=t(7437),i=t(2265),a=t(6394),n=t(535),o=t(3448);let l=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,r.jsx)(a.f,{ref:s,className:(0,o.cn)(l(),t),...i})});d.displayName=a.f.displayName},3675:function(e,s,t){"use strict";t.d(s,{g:function(){return n}});var r=t(7437),i=t(2265),a=t(3448);let n=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,r.jsx)("textarea",{className:(0,a.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...i})});n.displayName="Textarea"},933:function(e,s,t){"use strict";t.d(s,{Ds:function(){return o},Km:function(){return l},Z7:function(){return i},xL:function(){return n},xz:function(){return a}});var r=t(8933);async function i(e){let s=(0,r.eI)(),{data:t,error:i}=await s.from("assessment_questions").select("*").eq("domain",e.toLowerCase());if(i)throw console.error("Error fetching questions:",i),Error("Failed to fetch ".concat(e," questions"));return t.map(e=>({id:e.id,type:e.type,text:e.text,options:e.options,required:e.required,domain:e.domain,weight:e.weight,category:e.category}))}async function a(){let e=(0,r.eI)(),{data:s,error:t}=await e.from("assessment_questions").select("*");if(t)throw console.error("Error fetching all questions:",t),Error("Failed to fetch questions");let i={};return s.forEach(e=>{let s={id:e.id,type:e.type,text:e.text,options:e.options,required:e.required,domain:e.domain,weight:e.weight,category:e.category};i[e.domain]||(i[e.domain]=[]),i[e.domain].push(s)}),i}async function n(e){let s=(0,r.eI)(),{error:t}=await s.from("assessment_questions").insert([{id:e.id,domain:e.domain,type:e.type,text:e.text,options:e.options,required:e.required,weight:e.weight,category:e.category}]);if(t)throw console.error("Error adding question:",t),Error("Failed to add question")}async function o(e){let s=(0,r.eI)(),{error:t}=await s.from("assessment_questions").update({domain:e.domain,type:e.type,text:e.text,options:e.options,required:e.required,weight:e.weight,category:e.category}).eq("id",e.id);if(t)throw console.error("Error updating question:",t),Error("Failed to update question")}async function l(e){let s=(0,r.eI)(),{error:t}=await s.from("assessment_questions").delete().eq("id",e);if(t)throw console.error("Error deleting question:",t),Error("Failed to delete question")}}},function(e){e.O(0,[310,569,466,513,194,303,971,117,744],function(){return e(e.s=1411)}),_N_E=e.O()}]);