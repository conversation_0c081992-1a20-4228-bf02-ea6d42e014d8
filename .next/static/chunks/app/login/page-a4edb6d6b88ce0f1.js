(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[626],{4374:function(e,r,t){Promise.resolve().then(t.bind(t,8991))},9322:function(e,r,t){"use strict";t.d(r,{Z:function(){return a}});let a=(0,t(9205).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},8997:function(e,r,t){"use strict";t.d(r,{Z:function(){return a}});let a=(0,t(9205).Z)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},1817:function(e,r,t){"use strict";t.d(r,{Z:function(){return a}});let a=(0,t(9205).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},8728:function(e,r,t){"use strict";t.d(r,{Z:function(){return a}});let a=(0,t(9205).Z)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1884:function(e,r,t){"use strict";t.d(r,{Z:function(){return a}});let a=(0,t(9205).Z)("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9376:function(e,r,t){"use strict";var a=t(5475);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},8991:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return y}});var a=t(7437),n=t(2265),s=t(9376),i=t(9820),o=t(2381),l=t(279),c=t(5060),d=t(7168),u=t(8933),f=t(1884),m=t(8728),h=t(8997),g=t(9322),x=t(1817),p=t(7648);function y(){let e=(0,s.useRouter)(),r=(0,s.useSearchParams)(),t=r.get("role"),[y,v]=(0,n.useState)(""),[b,j]=(0,n.useState)(""),[w,N]=(0,n.useState)(!1),[k,C]=(0,n.useState)(null),[I,R]=(0,n.useState)("register"===r.get("tab")?"register":"login"),[P,S]=(0,n.useState)("user");(0,n.useEffect)(()=>{"counselor"===t?S("counselor"):"admin"===t?S("admin"):S("user"),(async()=>{let e=(0,u.s3)(),{data:{session:r},error:t}=await e.auth.getSession();if(t){console.error("Error getting session:",t);return}if(r){N(!0);try{console.log("Login Page: Active session found, fetching user role from /api/auth");let e=await fetch("/api/auth",{headers:{Authorization:"Bearer ".concat(r.access_token)}});if(!e.ok){let r=await e.json().catch(()=>({error:"Failed to parse error from /api/auth"}));console.error("Login Page: /api/auth call failed for existing session:",e.status,r.error),N(!1);return}let t=await e.json();console.log("Login Page: User data from /api/auth (existing session):",t),"admin"===t.role?window.location.href="/admin/dashboard":"counselor"===t.role?window.location.href="/counselor/dashboard":window.location.href="/couple/dashboard"}catch(e){console.error("Login Page: Error during session check and redirect:",e),N(!1)}}})()},[t,e]);let Z=async e=>{e.preventDefault(),N(!0),C(null);try{let e=(0,u.s3)(),{data:r,error:t}=await e.auth.signInWithPassword({email:y,password:b});if(t)throw t;let{data:a,error:n}=await e.auth.getSession();if(n||!a.session){console.error("Login error: Could not get session after sign-in",n),C("Failed to retrieve session after login. Please try again."),N(!1);return}console.log("Login Page: Successfully signed in, fetching user role from /api/auth with token.");let s=await fetch("/api/auth",{headers:{Authorization:"Bearer ".concat(a.session.access_token)}});if(!s.ok){let e=await s.json().catch(()=>({error:"Failed to parse error from /api/auth"}));console.error("Login Page: /api/auth call failed after login:",s.status,e.error),C("Role check failed: ".concat(e.error||s.statusText,". Please try again.")),N(!1);return}let i=await s.json();console.log("Login Page: User data from /api/auth (after login):",i),"admin"===i.role?window.location.href="/admin/dashboard":"counselor"===i.role?window.location.href="/counselor/dashboard":window.location.href="/couple/dashboard"}catch(e){console.error("Login error:",e),C(e instanceof Error?e.message:"An error occurred during login")}finally{N(!1)}},V=async e=>{e.preventDefault(),N(!0),C(null);try{let e=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:y,password:b,role:"user"})}),r=await e.json();if(!e.ok)throw Error(r.error||"Registration failed");let t=(0,u.s3)(),{error:a}=await t.auth.signInWithPassword({email:y,password:b});if(a)throw a;window.location.href="/couple/dashboard"}catch(e){console.error("Registration error:",e),C(e instanceof Error?e.message:"An error occurred during registration")}finally{N(!1)}},{icon:z,title:E}=(()=>{switch(P){case"counselor":return{icon:(0,a.jsx)(f.Z,{className:"h-8 w-8 text-primary"}),title:"Counselor Portal"};case"admin":return{icon:(0,a.jsx)(m.Z,{className:"h-8 w-8 text-primary"}),title:"Admin Portal"};default:return{icon:(0,a.jsx)(h.Z,{className:"h-8 w-8 text-primary"}),title:"Marriage Assessment"}}})();return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsx)("div",{className:"flex justify-center mb-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[z,(0,a.jsx)("span",{className:"text-2xl font-bold",children:E})]})}),(0,a.jsxs)(d.mQ,{defaultValue:"login",value:I,onValueChange:R,className:"w-full",children:[(0,a.jsxs)(d.dr,{className:"grid w-full grid-cols-2",children:[(0,a.jsx)(d.SP,{value:"login",children:"Login"}),"user"===P&&(0,a.jsx)(d.SP,{value:"register",children:"Register"})]}),(0,a.jsx)(d.nU,{value:"login",children:(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsx)(i.ll,{children:"user"===P?"Login":"counselor"===P?"Counselor Login":"Admin Login"}),(0,a.jsx)(i.SZ,{children:"Enter your credentials to access your account"})]}),(0,a.jsxs)("form",{onSubmit:Z,children:[(0,a.jsxs)(i.aY,{className:"space-y-4",children:[k&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.Z,{className:"h-4 w-4 mr-2"}),k]})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c._,{htmlFor:"email",children:"Email"}),(0,a.jsx)(l.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:y,onChange:e=>v(e.target.value),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(c._,{htmlFor:"password",children:"Password"}),(0,a.jsx)(p.default,{href:"/forgot-password",className:"text-sm text-primary hover:underline",children:"Forgot password?"})]}),(0,a.jsx)(l.I,{id:"password",type:"password",placeholder:"••••••••",value:b,onChange:e=>j(e.target.value),required:!0})]})]}),(0,a.jsxs)(i.eW,{className:"flex-col space-y-4",children:[(0,a.jsx)(o.z,{type:"submit",className:"w-full",disabled:w,children:w?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Logging in..."]}):"Login"}),"user"!==P&&(0,a.jsx)("div",{className:"text-center w-full",children:(0,a.jsx)(p.default,{href:"/login",className:"text-sm text-primary hover:underline",children:"Login as regular user"})}),"user"===P&&(0,a.jsx)("div",{className:"text-center w-full",children:(0,a.jsx)(p.default,{href:"/login?tab=register",className:"text-sm text-primary hover:underline",children:"Don't have an account? Register"})})]})]})]})}),"user"===P&&(0,a.jsx)(d.nU,{value:"register",children:(0,a.jsxs)(i.Zb,{children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsx)(i.ll,{children:"Create an Account"}),(0,a.jsx)(i.SZ,{children:"Register to start your marriage assessment journey"})]}),(0,a.jsxs)("form",{onSubmit:V,children:[(0,a.jsxs)(i.aY,{className:"space-y-4",children:[k&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.Z,{className:"h-4 w-4 mr-2"}),k]})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c._,{htmlFor:"register-email",children:"Email"}),(0,a.jsx)(l.I,{id:"register-email",type:"email",placeholder:"<EMAIL>",value:y,onChange:e=>v(e.target.value),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c._,{htmlFor:"register-password",children:"Password"}),(0,a.jsx)(l.I,{id:"register-password",type:"password",placeholder:"••••••••",value:b,onChange:e=>j(e.target.value),required:!0})]})]}),(0,a.jsxs)(i.eW,{className:"flex-col space-y-3",children:[(0,a.jsx)(o.z,{type:"submit",className:"w-full",disabled:w,children:w?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating account..."]}):"Create Account"}),(0,a.jsxs)("div",{className:"flex justify-between w-full text-xs text-muted-foreground pt-2",children:[(0,a.jsx)(p.default,{href:"/counselor/register",className:"hover:text-primary",children:"Register as counselor"}),(0,a.jsx)(p.default,{href:"/admin/register",className:"hover:text-primary",children:"Register as admin"})]})]})]})]})})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsx)(p.default,{href:"/",className:"text-sm text-muted-foreground hover:text-primary",children:"← Back to Home"})})]})})}},2381:function(e,r,t){"use strict";t.d(r,{z:function(){return c}});var a=t(7437),n=t(2265),s=t(7495),i=t(535),o=t(3448);let l=(0,i.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,r)=>{let{className:t,variant:n,size:i,asChild:c=!1,...d}=e,u=c?s.g7:"button";return(0,a.jsx)(u,{className:(0,o.cn)(l({variant:n,size:i,className:t})),ref:r,...d})});c.displayName="Button"},9820:function(e,r,t){"use strict";t.d(r,{Ol:function(){return o},SZ:function(){return c},Zb:function(){return i},aY:function(){return d},eW:function(){return u},ll:function(){return l}});var a=t(7437),n=t(2265),s=t(3448);let i=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...n})});i.displayName="Card";let o=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",t),...n})});o.displayName="CardHeader";let l=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("h3",{ref:r,className:(0,s.cn)("font-semibold leading-none tracking-tight",t),...n})});l.displayName="CardTitle";let c=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",t),...n})});c.displayName="CardDescription";let d=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",t),...n})});d.displayName="CardContent";let u=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)(" flex items-center p-6 pt-0",t),...n})});u.displayName="CardFooter"},279:function(e,r,t){"use strict";t.d(r,{I:function(){return i}});var a=t(7437),n=t(2265),s=t(3448);let i=n.forwardRef((e,r)=>{let{className:t,type:n,...i}=e;return(0,a.jsx)("input",{type:n,className:(0,s.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...i})});i.displayName="Input"},5060:function(e,r,t){"use strict";t.d(r,{_:function(){return c}});var a=t(7437),n=t(2265),s=t(6394),i=t(535),o=t(3448);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)(s.f,{ref:r,className:(0,o.cn)(l(),t),...n})});c.displayName=s.f.displayName},7168:function(e,r,t){"use strict";t.d(r,{SP:function(){return c},dr:function(){return l},mQ:function(){return o},nU:function(){return d}});var a=t(7437),n=t(2265),s=t(271),i=t(3448);let o=s.fC,l=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)(s.aV,{ref:r,className:(0,i.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",t),...n})});l.displayName=s.aV.displayName;let c=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)(s.xz,{ref:r,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",t),...n})});c.displayName=s.xz.displayName;let d=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)(s.VY,{ref:r,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...n})});d.displayName=s.VY.displayName},8933:function(e,r,t){"use strict";t.d(r,{eI:function(){return l},s3:function(){return c}});var a=t(3777),n=t(257);let s=null,i="https://eqghwtejdnzgopmcjlho.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY";if(n.env.SUPABASE_SERVICE_ROLE_KEY,!i||!o)throw Error("Missing Supabase environment variables");let l=()=>(s||(s=(0,a.eI)(i,o,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})),s),c=()=>l()},3448:function(e,r,t){"use strict";t.d(r,{cn:function(){return s}});var a=t(1994),n=t(3335);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.m6)((0,a.W)(r))}},6394:function(e,r,t){"use strict";t.d(r,{f:function(){return o}});var a=t(2265),n=t(6840),s=t(7437),i=a.forwardRef((e,r)=>(0,s.jsx)(n.WV.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null===(t=e.onMouseDown)||void 0===t||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var o=i},271:function(e,r,t){"use strict";t.d(r,{VY:function(){return V},aV:function(){return S},fC:function(){return P},xz:function(){return Z}});var a=t(2265),n=t(6741),s=t(3966),i=t(1353),o=t(1599),l=t(6840),c=t(9114),d=t(886),u=t(9255),f=t(7437),m="Tabs",[h,g]=(0,s.b)(m,[i.Pc]),x=(0,i.Pc)(),[p,y]=h(m),v=a.forwardRef((e,r)=>{let{__scopeTabs:t,value:a,onValueChange:n,defaultValue:s,orientation:i="horizontal",dir:o,activationMode:m="automatic",...h}=e,g=(0,c.gm)(o),[x,y]=(0,d.T)({prop:a,onChange:n,defaultProp:s});return(0,f.jsx)(p,{scope:t,baseId:(0,u.M)(),value:x,onValueChange:y,orientation:i,dir:g,activationMode:m,children:(0,f.jsx)(l.WV.div,{dir:g,"data-orientation":i,...h,ref:r})})});v.displayName=m;var b="TabsList",j=a.forwardRef((e,r)=>{let{__scopeTabs:t,loop:a=!0,...n}=e,s=y(b,t),o=x(t);return(0,f.jsx)(i.fC,{asChild:!0,...o,orientation:s.orientation,dir:s.dir,loop:a,children:(0,f.jsx)(l.WV.div,{role:"tablist","aria-orientation":s.orientation,...n,ref:r})})});j.displayName=b;var w="TabsTrigger",N=a.forwardRef((e,r)=>{let{__scopeTabs:t,value:a,disabled:s=!1,...o}=e,c=y(w,t),d=x(t),u=I(c.baseId,a),m=R(c.baseId,a),h=a===c.value;return(0,f.jsx)(i.ck,{asChild:!0,...d,focusable:!s,active:h,children:(0,f.jsx)(l.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":m,"data-state":h?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:u,...o,ref:r,onMouseDown:(0,n.M)(e.onMouseDown,e=>{s||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,n.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,n.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;h||s||!e||c.onValueChange(a)})})})});N.displayName=w;var k="TabsContent",C=a.forwardRef((e,r)=>{let{__scopeTabs:t,value:n,forceMount:s,children:i,...c}=e,d=y(k,t),u=I(d.baseId,n),m=R(d.baseId,n),h=n===d.value,g=a.useRef(h);return a.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(o.z,{present:s||h,children:t=>{let{present:a}=t;return(0,f.jsx)(l.WV.div,{"data-state":h?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!a,id:m,tabIndex:0,...c,ref:r,style:{...e.style,animationDuration:g.current?"0s":void 0},children:a&&i})}})});function I(e,r){return"".concat(e,"-trigger-").concat(r)}function R(e,r){return"".concat(e,"-content-").concat(r)}C.displayName=k;var P=v,S=j,Z=N,V=C}},function(e){e.O(0,[569,466,216,513,971,117,744],function(){return e(e.s=4374)}),_N_E=e.O()}]);