(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[702],{1948:function(e,t,r){Promise.resolve().then(r.bind(r,7340))},9322:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(9205).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},1671:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(9205).Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1723:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(9205).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},1817:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(9205).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7340:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return C}});var a=r(7437),n=r(2265),i=r(7648),s=r(9820),o=r(2381),l=r(7204),c=r(7168),d=r(279),u=r(1671),m=r(9322),f=r(9205);let p=(0,f.Z)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),h=(0,f.Z)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var g=r(5937),x=r(9174),v=r(1723);let b=(0,f.Z)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var y=r(3448),w=e=>{let{title:t,description:r,icon:n,status:o,domainId:l,className:c}=e;return(0,a.jsx)(i.default,{href:"/assessment/".concat(l),className:"block",children:(0,a.jsxs)(s.Zb,{className:(0,y.cn)("h-full transition-all duration-200 hover:shadow-md hover:scale-[1.02] cursor-pointer group","completed"===o&&"border-green-200 bg-green-50/50",c),children:[(0,a.jsx)(s.Ol,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"text-2xl",children:n}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(s.ll,{className:"text-base font-semibold group-hover:text-primary transition-colors",children:t})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(()=>{switch(o){case"completed":return(0,a.jsx)(u.Z,{className:"h-4 w-4 text-green-500"});case"in-progress":return(0,a.jsx)(v.Z,{className:"h-4 w-4 text-amber-500"});default:return(0,a.jsx)(v.Z,{className:"h-4 w-4 text-gray-400"})}})(),(0,a.jsx)(b,{className:"h-4 w-4 text-gray-400 group-hover:text-primary transition-colors"})]})]})}),(0,a.jsxs)(s.aY,{className:"pt-0",children:[(0,a.jsx)(s.SZ,{className:"text-sm text-gray-600 mb-3",children:r}),(0,a.jsx)("div",{className:"flex justify-between items-center",children:(()=>{switch(o){case"completed":return(0,a.jsx)(x.C,{variant:"secondary",className:"bg-green-100 text-green-700 border-green-200",children:"Completed"});case"in-progress":return(0,a.jsx)(x.C,{variant:"secondary",className:"bg-amber-100 text-amber-700 border-amber-200",children:"In Progress"});default:return(0,a.jsx)(x.C,{variant:"outline",className:"text-gray-600",children:"Not Started"})}})()})]})]})})},j=r(8933),N=e=>{let{userName:t="John",coupleCode:r="",partnerName:f="",isConnected:x=!1,completedDomains:v=[]}=e,[b,y]=(0,n.useState)("overview"),[N,k]=(0,n.useState)(r||""),[C,_]=(0,n.useState)(""),[I,D]=(0,n.useState)(x?"connected":"idle"),[S,E]=(0,n.useState)(!1),[Z,z]=(0,n.useState)(""),V=[{id:"visi-hidup",title:"Visi Hidup",description:"Aspirasi dan tujuan jangka panjang dalam pernikahan",icon:"\uD83D\uDD2D",completed:v.includes("visi-hidup"),status:v.includes("visi-hidup")?"completed":"not-started"},{id:"keuangan",title:"Keuangan",description:"Pengelolaan keuangan dan transparansi finansial",icon:"\uD83D\uDCB0",completed:v.includes("keuangan"),status:v.includes("keuangan")?"completed":"not-started"},{id:"pengasuhan",title:"Pengasuhan Anak",description:"Gaya dan filosofi dalam mendidik anak",icon:"\uD83D\uDC76",completed:v.includes("pengasuhan"),status:v.includes("pengasuhan")?"completed":"not-started"},{id:"komunikasi",title:"Komunikasi",description:"Cara berkomunikasi dan menyelesaikan konflik",icon:"\uD83D\uDCAC",completed:v.includes("komunikasi"),status:v.includes("komunikasi")?"completed":"not-started"},{id:"fungsi-dan-peran",title:"Fungsi dan Peran",description:"Peran suami-istri berdasarkan nilai-nilai Alkitab",icon:"\uD83D\uDD04",completed:v.includes("fungsi-dan-peran"),status:v.includes("fungsi-dan-peran")?"completed":"not-started"},{id:"seks",title:"Keintiman Seksual",description:"Pandangan dan ekspektasi tentang keintiman",icon:"❤️",completed:v.includes("seks"),status:v.includes("seks")?"completed":"not-started"},{id:"spiritualitas",title:"Spiritualitas",description:"Pertumbuhan iman dan praktik spiritual bersama",icon:"✝️",completed:v.includes("spiritualitas"),status:v.includes("spiritualitas")?"completed":"not-started"},{id:"sisi-gelap",title:"Sisi Gelap",description:"Pengelolaan emosi negatif dan potensi masalah",icon:"\uD83C\uDF11",completed:v.includes("sisi-gelap"),status:v.includes("sisi-gelap")?"completed":"not-started"}],R=Math.round(v.length/V.length*100),P=async()=>{E(!0),z("");try{let e;let t=(0,j.eI)(),{data:{user:r},error:a}=await t.auth.getUser();if(a||!r)throw Error("You must be logged in to generate a code");let{data:n}=await t.from("couples").select("couple_id").or("user_id_1.eq.".concat(r.id,",user_id_2.eq.").concat(r.id)).single();if(n)throw Error("You are already connected with a partner");await t.from("couple_invitation_codes").update({is_active:!1}).eq("creator_user_id",r.id).eq("is_active",!0);let i=!1,s=0;do{e=Math.random().toString(36).substring(2,8).toUpperCase();let{data:r}=await t.from("couple_invitation_codes").select("id").eq("code",e).eq("is_active",!0).single();i=!r,s++}while(!i&&s<10);if(!i)throw Error("Failed to generate unique code. Please try again.");let{data:o,error:l}=await t.from("couple_invitation_codes").insert({code:e,creator_user_id:r.id,expires_at:new Date(Date.now()+6048e5).toISOString()}).select().single();if(l)throw console.error("Error creating invitation code:",l),Error("Failed to create invitation code");k(o.code)}catch(e){console.error("Error generating code:",e),z(e instanceof Error?e.message:"Failed to generate code")}finally{E(!1)}},q=async()=>{if(6!==C.length){z("Please enter a valid 6-character code");return}E(!0),D("pending"),z("");try{let e=(0,j.eI)(),{data:{user:t},error:r}=await e.auth.getUser();if(r||!t)throw Error("You must be logged in to connect");let{data:a}=await e.from("couples").select("couple_id").or("user_id_1.eq.".concat(t.id,",user_id_2.eq.").concat(t.id)).single();if(a)throw Error("You are already connected with a partner");console.log("Searching for code:",C.toUpperCase());let{data:n,error:i}=await e.from("couple_invitation_codes").select("*").eq("code",C.toUpperCase()).eq("is_active",!0).gte("expires_at",new Date().toISOString()).single();if(console.log("Code search result:",{invitationCode:n,codeError:i}),i||!n){let{data:t}=await e.from("couple_invitation_codes").select("*").eq("code",C.toUpperCase()).single();if(console.log("Any code with this value:",t),t&&!t.is_active)throw Error("This invitation code has been used or deactivated");throw Error("Invalid or expired invitation code")}if(new Date(n.expires_at)<new Date)throw Error("Invitation code has expired");if(n.used_by_user_id)throw Error("Invitation code has already been used");if(n.creator_user_id===t.id)throw Error("You cannot use your own invitation code");let{data:s}=await e.from("couples").select("couple_id").or("user_id_1.eq.".concat(n.creator_user_id,",user_id_2.eq.").concat(n.creator_user_id)).single();if(s)throw Error("The code creator is already connected with someone else");let{data:o,error:l}=await e.from("couples").insert({user_id_1:n.creator_user_id,user_id_2:t.id}).select().single();if(l)throw console.error("Error creating couple:",l),Error("Failed to create couple connection");let{error:c}=await e.from("couple_invitation_codes").update({used_by_user_id:t.id,used_at:new Date().toISOString(),is_active:!1}).eq("id",n.id);c?console.error("Error updating invitation code:",c):console.log("Invitation code updated successfully"),D("connected"),_(""),window.location.reload()}catch(e){console.error("Error connecting with partner:",e),D("error"),z(e instanceof Error?e.message:"Failed to connect with partner")}finally{E(!1)}};return(0,a.jsxs)("div",{className:"bg-background w-full max-w-7xl mx-auto p-4 md:p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Assessment Dashboard"}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:["Welcome back, ",t,". Continue your marriage assessment journey."]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.z,{variant:"outline",onClick:()=>y("overview"),children:"Overview"}),(0,a.jsx)(o.z,{variant:"outline",onClick:()=>y("connect"),children:"Connect"}),R>0&&(0,a.jsx)(o.z,{variant:"outline",onClick:()=>y("results"),children:"Results"}),(0,a.jsx)(o.z,{variant:"default",asChild:!0,children:(0,a.jsx)(i.default,{href:"/couple/dashboard",children:"Couple Dashboard"})})]})]}),"overview"===b&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(s.Zb,{children:[(0,a.jsxs)(s.Ol,{className:"pb-2",children:[(0,a.jsx)(s.ll,{children:"Assessment Progress"}),(0,a.jsx)(s.SZ,{children:"Complete all 8 domains to get comprehensive insights"})]}),(0,a.jsx)(s.aY,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{children:[v.length," of ",V.length," completed"]}),(0,a.jsxs)("span",{children:[R,"%"]})]}),(0,a.jsx)(l.E,{value:R,className:"h-2"})]})})]}),"connected"===I?(0,a.jsxs)(g.bZ,{className:"bg-green-50 border-green-200",children:[(0,a.jsx)(u.Z,{className:"h-4 w-4 text-green-600"}),(0,a.jsxs)(g.X,{children:["You are connected with ",f||"your partner",". Complete your assessments to view compatibility results."]})]}):(0,a.jsxs)(g.bZ,{children:[(0,a.jsx)(m.Z,{className:"h-4 w-4"}),(0,a.jsx)(g.X,{children:"Connect with your partner to compare assessment results and get compatibility insights."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:V.map(e=>(0,a.jsx)(w,{title:e.title,description:e.description,icon:e.icon,status:e.status,domainId:e.id},e.id))})]}),"connect"===b&&(0,a.jsxs)(s.Zb,{className:"w-full",children:[(0,a.jsxs)(s.Ol,{children:[(0,a.jsx)(s.ll,{children:"Connect with Your Partner"}),(0,a.jsx)(s.SZ,{children:"Generate a code to share with your partner or enter the code they shared with you"})]}),(0,a.jsx)(s.aY,{children:(0,a.jsxs)(c.mQ,{defaultValue:"generate",className:"w-full",children:[(0,a.jsxs)(c.dr,{className:"grid w-full grid-cols-2",children:[(0,a.jsx)(c.SP,{value:"generate",children:"Generate Code"}),(0,a.jsx)(c.SP,{value:"enter",children:"Enter Code"})]}),(0,a.jsx)(c.nU,{value:"generate",className:"space-y-4",children:(0,a.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(d.I,{value:N,readOnly:!0,className:"font-mono text-center text-lg",placeholder:"No code generated"}),(0,a.jsx)(o.z,{variant:"outline",size:"icon",onClick:()=>{navigator.clipboard.writeText(N)},disabled:!N,children:(0,a.jsx)(p,{className:"h-4 w-4"})})]}),(0,a.jsxs)(o.z,{onClick:P,className:"w-full",disabled:S,children:[(0,a.jsx)(h,{className:"mr-2 h-4 w-4 ".concat(S?"animate-spin":"")}),S?"Generating...":"Generate New Code"]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground text-center",children:"Share this code with your partner so they can connect with you"}),Z&&(0,a.jsxs)(g.bZ,{variant:"destructive",children:[(0,a.jsx)(m.Z,{className:"h-4 w-4"}),(0,a.jsx)(g.X,{children:Z})]})]})}),(0,a.jsx)(c.nU,{value:"enter",className:"space-y-4",children:(0,a.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,a.jsx)(d.I,{value:C,onChange:e=>_(e.target.value),className:"font-mono text-center text-lg",placeholder:"Enter 6-digit code",maxLength:6}),(0,a.jsx)(o.z,{onClick:q,className:"w-full",disabled:S||"pending"===I||6!==C.length,children:"pending"===I||S?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h,{className:"mr-2 h-4 w-4 animate-spin"}),"Connecting..."]}):"Connect with Partner"}),("error"===I||Z)&&(0,a.jsxs)(g.bZ,{variant:"destructive",children:[(0,a.jsx)(m.Z,{className:"h-4 w-4"}),(0,a.jsx)(g.X,{children:Z||"Invalid code. Please check and try again."})]})]})})]})})]}),"results"===b&&(0,a.jsxs)(s.Zb,{children:[(0,a.jsxs)(s.Ol,{children:[(0,a.jsx)(s.ll,{children:"Assessment Results"}),(0,a.jsx)(s.SZ,{children:"connected"===I?"View your compatibility results and insights":"Connect with your partner to view compatibility results"})]}),(0,a.jsx)(s.aY,{children:"connected"===I?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:v.length===V.length?"All assessments completed! View your detailed results below.":"Complete all assessment domains to view detailed compatibility results."}),(0,a.jsx)(o.z,{disabled:v.length!==V.length,children:"View Detailed Results"})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"You need to connect with your partner first to view compatibility results."}),(0,a.jsx)(o.z,{onClick:()=>y("connect"),children:"Connect with Partner"})]})})]})]})},k=r(1817);function C(){let[e,t]=(0,n.useState)(!0),[r,i]=(0,n.useState)(null),[s,o]=(0,n.useState)({userName:"",coupleCode:"",partnerName:"",isConnected:!1,completedDomains:[]});return((0,n.useEffect)(()=>{(async()=>{try{var e,r;t(!0);let a=(0,j.eI)(),{data:{user:n}}=await a.auth.getUser();if(!n){window.location.href="/login";return}try{let e=await fetch("/api/auth"),t=await e.json();if("admin"===t.role){window.location.href="/admin/dashboard";return}if("counselor"===t.role){window.location.href="/counselor/dashboard";return}}catch(e){console.error("Error checking user role:",e)}let{data:i,error:s}=await a.from("profiles").select("*").eq("id",n.id).maybeSingle(),{data:l,error:c}=await a.from("individual_results").select("domains").eq("user_id",n.id).maybeSingle(),{data:{session:d}}=await a.auth.getSession(),u=null==d?void 0:d.access_token,m=null,f=null,p="",h="";if(u)try{let e=await fetch("/api/couples/status",{headers:{Authorization:"Bearer ".concat(u)}});e.ok&&((m=await e.json()).isConnected&&m.partner?(p=m.partner.displayName,h=m.couple.friendlyCode):m.activeInvitationCode&&(f=m.activeInvitationCode))}catch(e){}let g=(null==l?void 0:null===(e=l.domains)||void 0===e?void 0:e.map(e=>e.domain.toLowerCase()))||[];o({userName:(null==i?void 0:i.full_name)||(null===(r=n.email)||void 0===r?void 0:r.split("@")[0])||"User",coupleCode:h||(null==f?void 0:f.code)||"",partnerName:p,isConnected:(null==m?void 0:m.isConnected)||!1,completedDomains:g})}catch(e){console.error("Error fetching user data:",e),i(e instanceof Error?e.message:"An error occurred while loading your dashboard")}finally{t(!1)}})()},[]),e)?(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,a.jsx)(k.Z,{className:"h-8 w-8 animate-spin text-primary"}),(0,a.jsx)("span",{className:"ml-2",children:"Loading your dashboard..."})]}):r?(0,a.jsx)("div",{className:"container py-8",children:(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:r})}):(0,a.jsx)("div",{className:"min-h-screen bg-background",children:(0,a.jsx)(N,{userName:s.userName,coupleCode:s.coupleCode,partnerName:s.partnerName,isConnected:s.isConnected,completedDomains:s.completedDomains})})}},5937:function(e,t,r){"use strict";r.d(t,{X:function(){return c},bZ:function(){return l}});var a=r(7437),n=r(2265),i=r(535),s=r(3448);let o=(0,i.j)("relative w-full rounded-lg border px-4 py-3 text-sm [&:has(svg)]:pl-11 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=n.forwardRef((e,t)=>{let{className:r,variant:n,...i}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,s.cn)(o({variant:n}),r),...i})});l.displayName="Alert",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("h5",{ref:t,className:(0,s.cn)("mb-1 font-medium leading-none tracking-tight",r),...n})}).displayName="AlertTitle";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("text-sm [&_p]:leading-relaxed",r),...n})});c.displayName="AlertDescription"},9174:function(e,t,r){"use strict";r.d(t,{C:function(){return o}});var a=r(7437);r(2265);var n=r(535),i=r(3448);let s=(0,n.j)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,...n}=e;return(0,a.jsx)("div",{className:(0,i.cn)(s({variant:r}),t),...n})}},2381:function(e,t,r){"use strict";r.d(t,{z:function(){return c}});var a=r(7437),n=r(2265),i=r(7495),s=r(535),o=r(3448);let l=(0,s.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,t)=>{let{className:r,variant:n,size:s,asChild:c=!1,...d}=e,u=c?i.g7:"button";return(0,a.jsx)(u,{className:(0,o.cn)(l({variant:n,size:s,className:r})),ref:t,...d})});c.displayName="Button"},9820:function(e,t,r){"use strict";r.d(t,{Ol:function(){return o},SZ:function(){return c},Zb:function(){return s},aY:function(){return d},eW:function(){return u},ll:function(){return l}});var a=r(7437),n=r(2265),i=r(3448);let s=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...n})});s.displayName="Card";let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...n})});o.displayName="CardHeader";let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("h3",{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",r),...n})});l.displayName="CardTitle";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...n})});c.displayName="CardDescription";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",r),...n})});d.displayName="CardContent";let u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)(" flex items-center p-6 pt-0",r),...n})});u.displayName="CardFooter"},279:function(e,t,r){"use strict";r.d(t,{I:function(){return s}});var a=r(7437),n=r(2265),i=r(3448);let s=n.forwardRef((e,t)=>{let{className:r,type:n,...s}=e;return(0,a.jsx)("input",{type:n,className:(0,i.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...s})});s.displayName="Input"},7204:function(e,t,r){"use strict";r.d(t,{E:function(){return o}});var a=r(7437),n=r(2265),i=r(610),s=r(3448);let o=n.forwardRef((e,t)=>{let{className:r,value:n,...o}=e;return(0,a.jsx)(i.fC,{ref:t,className:(0,s.cn)("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",r),...o,children:(0,a.jsx)(i.z$,{className:"h-full w-full flex-1 bg-primary transition-all bg-white",style:{transform:"translateX(-".concat(100-(n||0),"%)")}})})});o.displayName=i.fC.displayName},7168:function(e,t,r){"use strict";r.d(t,{SP:function(){return c},dr:function(){return l},mQ:function(){return o},nU:function(){return d}});var a=r(7437),n=r(2265),i=r(271),s=r(3448);let o=i.fC,l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(i.aV,{ref:t,className:(0,s.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",r),...n})});l.displayName=i.aV.displayName;let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(i.xz,{ref:t,className:(0,s.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",r),...n})});c.displayName=i.xz.displayName;let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(i.VY,{ref:t,className:(0,s.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...n})});d.displayName=i.VY.displayName},8933:function(e,t,r){"use strict";r.d(t,{eI:function(){return l},s3:function(){return c}});var a=r(3777),n=r(257);let i=null,s="https://eqghwtejdnzgopmcjlho.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY";if(n.env.SUPABASE_SERVICE_ROLE_KEY,!s||!o)throw Error("Missing Supabase environment variables");let l=()=>(i||(i=(0,a.eI)(s,o,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})),i),c=()=>l()},3448:function(e,t,r){"use strict";r.d(t,{cn:function(){return i}});var a=r(1994),n=r(3335);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.m6)((0,a.W)(t))}},610:function(e,t,r){"use strict";r.d(t,{fC:function(){return y},z$:function(){return w}});var a=r(2265),n=r(3966),i=r(6840),s=r(7437),o="Progress",[l,c]=(0,n.b)(o),[d,u]=l(o),m=a.forwardRef((e,t)=>{var r,a,n,o;let{__scopeProgress:l,value:c=null,max:u,getValueLabel:m=h,...f}=e;(u||0===u)&&!v(u)&&console.error((r="".concat(u),a="Progress","Invalid prop `max` of value `".concat(r,"` supplied to `").concat(a,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let p=v(u)?u:100;null===c||b(c,p)||console.error((n="".concat(c),o="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(o,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let y=b(c,p)?c:null,w=x(y)?m(y,p):void 0;return(0,s.jsx)(d,{scope:l,value:y,max:p,children:(0,s.jsx)(i.WV.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":x(y)?y:void 0,"aria-valuetext":w,role:"progressbar","data-state":g(y,p),"data-value":null!=y?y:void 0,"data-max":p,...f,ref:t})})});m.displayName=o;var f="ProgressIndicator",p=a.forwardRef((e,t)=>{var r;let{__scopeProgress:a,...n}=e,o=u(f,a);return(0,s.jsx)(i.WV.div,{"data-state":g(o.value,o.max),"data-value":null!==(r=o.value)&&void 0!==r?r:void 0,"data-max":o.max,...n,ref:t})});function h(e,t){return"".concat(Math.round(e/t*100),"%")}function g(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function x(e){return"number"==typeof e}function v(e){return x(e)&&!isNaN(e)&&e>0}function b(e,t){return x(e)&&!isNaN(e)&&e<=t&&e>=0}p.displayName=f;var y=m,w=p},271:function(e,t,r){"use strict";r.d(t,{VY:function(){return Z},aV:function(){return S},fC:function(){return D},xz:function(){return E}});var a=r(2265),n=r(6741),i=r(3966),s=r(1353),o=r(1599),l=r(6840),c=r(9114),d=r(886),u=r(9255),m=r(7437),f="Tabs",[p,h]=(0,i.b)(f,[s.Pc]),g=(0,s.Pc)(),[x,v]=p(f),b=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:n,defaultValue:i,orientation:s="horizontal",dir:o,activationMode:f="automatic",...p}=e,h=(0,c.gm)(o),[g,v]=(0,d.T)({prop:a,onChange:n,defaultProp:i});return(0,m.jsx)(x,{scope:r,baseId:(0,u.M)(),value:g,onValueChange:v,orientation:s,dir:h,activationMode:f,children:(0,m.jsx)(l.WV.div,{dir:h,"data-orientation":s,...p,ref:t})})});b.displayName=f;var y="TabsList",w=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...n}=e,i=v(y,r),o=g(r);return(0,m.jsx)(s.fC,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:a,children:(0,m.jsx)(l.WV.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});w.displayName=y;var j="TabsTrigger",N=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:i=!1,...o}=e,c=v(j,r),d=g(r),u=_(c.baseId,a),f=I(c.baseId,a),p=a===c.value;return(0,m.jsx)(s.ck,{asChild:!0,...d,focusable:!i,active:p,children:(0,m.jsx)(l.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":f,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...o,ref:t,onMouseDown:(0,n.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,n.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,n.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||i||!e||c.onValueChange(a)})})})});N.displayName=j;var k="TabsContent",C=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:i,children:s,...c}=e,d=v(k,r),u=_(d.baseId,n),f=I(d.baseId,n),p=n===d.value,h=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(o.z,{present:i||p,children:r=>{let{present:a}=r;return(0,m.jsx)(l.WV.div,{"data-state":p?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!a,id:f,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:a&&s})}})});function _(e,t){return"".concat(e,"-trigger-").concat(t)}function I(e,t){return"".concat(e,"-content-").concat(t)}C.displayName=k;var D=b,S=w,E=N,Z=C}},function(e){e.O(0,[569,466,216,513,971,117,744],function(){return e(e.s=1948)}),_N_E=e.O()}]);