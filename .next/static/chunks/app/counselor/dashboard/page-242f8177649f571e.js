(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[667],{1689:function(e,s,r){Promise.resolve().then(r.bind(r,3300))},505:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});let n=(0,r(9205).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9322:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});let n=(0,r(9205).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},8736:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});let n=(0,r(9205).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},1817:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});let n=(0,r(9205).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7692:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});let n=(0,r(9205).Z)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},2718:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});let n=(0,r(9205).Z)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},3247:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});let n=(0,r(9205).Z)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},1884:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});let n=(0,r(9205).Z)("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},5805:function(e,s,r){"use strict";r.d(s,{Z:function(){return n}});let n=(0,r(9205).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9376:function(e,s,r){"use strict";var n=r(5475);r.o(n,"useParams")&&r.d(s,{useParams:function(){return n.useParams}}),r.o(n,"useRouter")&&r.d(s,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(s,{useSearchParams:function(){return n.useSearchParams}})},3300:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return N}});var n=r(7437),t=r(2265),a=r(9376),l=r(9820),i=r(2381),o=r(7168),c=r(8933),d=r(1817),u=r(9322),h=r(1884),f=r(5805),m=r(505),x=r(8736),p=r(7692),g=r(3247),j=r(2718),y=r(1399),b=r(279),v=r(7648);function N(){let e=(0,a.useRouter)(),[s,r]=(0,t.useState)(!0),[N,w]=(0,t.useState)(null),[k,C]=(0,t.useState)({assignedCouples:0,scheduledSessions:0,completedSessions:0}),[Z,S]=(0,t.useState)(!1),[M,I]=(0,t.useState)("");(0,t.useEffect)(()=>{(async()=>{r(!0);try{let s=(0,c.s3)();console.log("CounselorDashboard: checkCounselorStatus called");let{data:r,error:n}=await s.auth.getUser();if(n){console.error("CounselorDashboard: Error getting user:",n),e.push("/login");return}if(!r||!r.user){console.log("CounselorDashboard: No user found, redirecting to login."),e.push("/login");return}let t=r.user;console.log("CounselorDashboard: User found - ID:",t.id,"Email:",t.email),console.log("CounselorDashboard: Checking counselor_profiles for user_id: ".concat(t.id));let{data:a,error:l}=await s.from("counselor_profiles").select("user_id, id").eq("user_id",t.id).single();if(l){console.error("CounselorDashboard: Error fetching counselor profile:",l),w("Failed to load counselor profile: ".concat(l.message,". Please check Supabase RLS policies.")),S(!1);return}if(!a){console.log("CounselorDashboard: No counselor profile found for user_id: ".concat(t.id,".")),w("Counselor profile not found. You may not have counselor permissions."),S(!1);return}console.log("CounselorDashboard: Counselor profile found:",a),S(!0),R()}catch(e){console.error("CounselorDashboard: Catch block error in checkCounselorStatus:",e),w("An unexpected error occurred: ".concat(e.message||"Unknown error")),S(!1)}finally{r(!1)}})()},[e]);let R=async()=>{try{let e=(0,c.s3)(),{data:{user:s}}=await e.auth.getUser();if(!s)return;let{count:r}=await e.from("counselor_couple_assignments").select("*",{count:"exact",head:!0}).eq("counselor_id",s.id),{count:n}=await e.from("counseling_sessions").select("*",{count:"exact",head:!0}).eq("counselor_id",s.id).eq("status","scheduled"),{count:t}=await e.from("counseling_sessions").select("*",{count:"exact",head:!0}).eq("counselor_id",s.id).eq("status","completed");C({assignedCouples:r||0,scheduledSessions:n||0,completedSessions:t||0})}catch(e){console.error("Error fetching dashboard stats:",e),w("Failed to load dashboard statistics")}};return s?(0,n.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,n.jsx)(d.Z,{className:"h-8 w-8 animate-spin text-primary"}),(0,n.jsx)("span",{className:"ml-2",children:"Loading counselor dashboard..."})]}):N?(0,n.jsx)("div",{className:"container mx-auto p-4 min-h-screen flex flex-col items-center justify-center",children:(0,n.jsxs)(l.Zb,{className:"w-full max-w-md",children:[(0,n.jsx)(l.Ol,{children:(0,n.jsx)(l.ll,{className:"text-destructive",children:"Access Error"})}),(0,n.jsxs)(l.aY,{children:[(0,n.jsxs)("div",{className:"flex items-center text-destructive",children:[(0,n.jsx)(u.Z,{className:"h-5 w-5 mr-2"}),(0,n.jsx)("p",{children:N})]}),(0,n.jsx)("p",{className:"mt-4 text-sm text-muted-foreground",children:"There was an issue verifying your counselor status or loading necessary data. If this persists, please contact support. The Supabase RLS policy for 'admin_users' might be causing an infinite recursion."})]}),(0,n.jsx)(l.eW,{children:(0,n.jsx)(i.z,{onClick:()=>e.push("/login"),className:"w-full",children:"Go to Login"})})]})}):s?(0,n.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,n.jsx)(d.Z,{className:"h-8 w-8 animate-spin text-primary"}),(0,n.jsx)("span",{className:"ml-2",children:"Loading counselor dashboard..."})]}):Z?(0,n.jsx)("div",{className:"min-h-screen bg-background",children:(0,n.jsxs)("div",{className:"flex",children:[(0,n.jsx)("div",{className:"hidden md:flex w-64 flex-col fixed inset-y-0 z-50 border-r bg-background",children:(0,n.jsxs)("div",{className:"flex flex-col space-y-6 p-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 px-2 py-4",children:[(0,n.jsx)(h.Z,{className:"h-6 w-6 text-primary"}),(0,n.jsx)("span",{className:"text-xl font-bold",children:"Counselor Portal"})]}),(0,n.jsxs)("nav",{className:"flex flex-col space-y-1",children:[(0,n.jsxs)(v.default,{href:"/counselor/dashboard",className:"flex items-center gap-3 rounded-lg bg-primary/10 px-3 py-2 text-primary transition-all hover:text-primary",children:[(0,n.jsx)(f.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Dashboard"})]}),(0,n.jsxs)(v.default,{href:"/counselor/couples",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,n.jsx)(f.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"My Couples"})]}),(0,n.jsxs)(v.default,{href:"/counselor/sessions",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,n.jsx)(m.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Sessions"})]}),(0,n.jsxs)(v.default,{href:"/counselor/resources",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,n.jsx)(x.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Resources"})]}),(0,n.jsxs)(v.default,{href:"/counselor/profile",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,n.jsx)(h.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"My Profile"})]}),(0,n.jsxs)("button",{onClick:y.h,className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:bg-red-50 hover:text-red-600 w-full mt-4",children:[(0,n.jsx)(p.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Logout"})]})]})]})}),(0,n.jsx)("div",{className:"flex-1 md:pl-64",children:(0,n.jsxs)("div",{className:"container py-8",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Counselor Dashboard"}),N&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(u.Z,{className:"h-4 w-4 mr-2"}),N]})}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,n.jsxs)(l.Zb,{children:[(0,n.jsx)(l.Ol,{className:"pb-2",children:(0,n.jsx)(l.ll,{className:"text-lg",children:"Assigned Couples"})}),(0,n.jsx)(l.aY,{children:(0,n.jsx)("div",{className:"text-3xl font-bold",children:k.assignedCouples})}),(0,n.jsx)(l.eW,{children:(0,n.jsx)(i.z,{variant:"ghost",className:"w-full text-primary",onClick:()=>e.push("/counselor/couples"),children:"View All"})})]}),(0,n.jsxs)(l.Zb,{children:[(0,n.jsx)(l.Ol,{className:"pb-2",children:(0,n.jsx)(l.ll,{className:"text-lg",children:"Scheduled Sessions"})}),(0,n.jsx)(l.aY,{children:(0,n.jsx)("div",{className:"text-3xl font-bold",children:k.scheduledSessions})}),(0,n.jsx)(l.eW,{children:(0,n.jsx)(i.z,{variant:"ghost",className:"w-full text-primary",onClick:()=>e.push("/counselor/sessions?filter=scheduled"),children:"View Schedule"})})]}),(0,n.jsxs)(l.Zb,{children:[(0,n.jsx)(l.Ol,{className:"pb-2",children:(0,n.jsx)(l.ll,{className:"text-lg",children:"Completed Sessions"})}),(0,n.jsx)(l.aY,{children:(0,n.jsx)("div",{className:"text-3xl font-bold",children:k.completedSessions})}),(0,n.jsx)(l.eW,{children:(0,n.jsx)(i.z,{variant:"ghost",className:"w-full text-primary",onClick:()=>e.push("/counselor/sessions?filter=completed"),children:"View History"})})]})]}),(0,n.jsxs)(l.Zb,{className:"mb-8",children:[(0,n.jsxs)(l.Ol,{children:[(0,n.jsx)(l.ll,{children:"Find Couple"}),(0,n.jsx)(l.SZ,{children:"Search for couples by name or couple code"})]}),(0,n.jsx)(l.aY,{children:(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsxs)("div",{className:"relative flex-1",children:[(0,n.jsx)(g.Z,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,n.jsx)(b.I,{type:"search",placeholder:"Search by name or couple code...",className:"pl-8",value:M,onChange:e=>I(e.target.value)})]}),(0,n.jsx)(i.z,{children:"Search"})]})})]}),(0,n.jsxs)(l.Zb,{className:"mb-8",children:[(0,n.jsxs)(l.Ol,{children:[(0,n.jsx)(l.ll,{children:"Upcoming Sessions"}),(0,n.jsx)(l.SZ,{children:"Your scheduled counseling sessions"})]}),(0,n.jsx)(l.aY,{children:(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"No upcoming sessions scheduled. Sessions will appear here when couples book time with you."})})}),(0,n.jsx)(l.eW,{children:(0,n.jsx)(i.z,{variant:"outline",className:"w-full",onClick:()=>e.push("/counselor/sessions"),children:"View All Sessions"})})]}),(0,n.jsxs)(l.Zb,{children:[(0,n.jsxs)(l.Ol,{children:[(0,n.jsx)(l.ll,{children:"Recent Couple Activity"}),(0,n.jsx)(l.SZ,{children:"Latest assessment completions and updates"})]}),(0,n.jsx)(l.aY,{children:(0,n.jsxs)(o.mQ,{defaultValue:"assessments",children:[(0,n.jsxs)(o.dr,{className:"mb-4",children:[(0,n.jsx)(o.SP,{value:"assessments",children:"Assessments"}),(0,n.jsx)(o.SP,{value:"messages",children:"Messages"})]}),(0,n.jsx)(o.nU,{value:"assessments",children:(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"Recent assessment completions will appear here."})})}),(0,n.jsx)(o.nU,{value:"messages",children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("p",{className:"text-muted-foreground",children:"Recent messages from couples will appear here."}),(0,n.jsxs)(i.z,{className:"flex items-center gap-2",onClick:()=>e.push("/counselor/messages"),children:[(0,n.jsx)(j.Z,{className:"h-4 w-4"}),"Open Messages"]})]})})]})})]})]})})]})}):(0,n.jsx)("div",{className:"container mx-auto p-4 min-h-screen flex flex-col items-center justify-center",children:(0,n.jsxs)(l.Zb,{className:"w-full max-w-md",children:[(0,n.jsx)(l.Ol,{children:(0,n.jsx)(l.ll,{children:"Permission Issue"})}),(0,n.jsx)(l.aY,{children:(0,n.jsx)("p",{children:"Could not verify counselor permissions. You will be redirected to login."})}),(0,n.jsx)(l.eW,{children:(0,n.jsx)(i.z,{onClick:()=>e.push("/login"),className:"w-full",children:"Go to Login"})})]})})}},2381:function(e,s,r){"use strict";r.d(s,{z:function(){return c}});var n=r(7437),t=r(2265),a=r(7495),l=r(535),i=r(3448);let o=(0,l.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=t.forwardRef((e,s)=>{let{className:r,variant:t,size:l,asChild:c=!1,...d}=e,u=c?a.g7:"button";return(0,n.jsx)(u,{className:(0,i.cn)(o({variant:t,size:l,className:r})),ref:s,...d})});c.displayName="Button"},9820:function(e,s,r){"use strict";r.d(s,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return l},aY:function(){return d},eW:function(){return u},ll:function(){return o}});var n=r(7437),t=r(2265),a=r(3448);let l=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)("div",{ref:s,className:(0,a.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...t})});l.displayName="Card";let i=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)("div",{ref:s,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",r),...t})});i.displayName="CardHeader";let o=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)("h3",{ref:s,className:(0,a.cn)("font-semibold leading-none tracking-tight",r),...t})});o.displayName="CardTitle";let c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)("p",{ref:s,className:(0,a.cn)("text-sm text-muted-foreground",r),...t})});c.displayName="CardDescription";let d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)("div",{ref:s,className:(0,a.cn)("p-6 pt-0",r),...t})});d.displayName="CardContent";let u=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)("div",{ref:s,className:(0,a.cn)(" flex items-center p-6 pt-0",r),...t})});u.displayName="CardFooter"},279:function(e,s,r){"use strict";r.d(s,{I:function(){return l}});var n=r(7437),t=r(2265),a=r(3448);let l=t.forwardRef((e,s)=>{let{className:r,type:t,...l}=e;return(0,n.jsx)("input",{type:t,className:(0,a.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",r),ref:s,...l})});l.displayName="Input"},7168:function(e,s,r){"use strict";r.d(s,{SP:function(){return c},dr:function(){return o},mQ:function(){return i},nU:function(){return d}});var n=r(7437),t=r(2265),a=r(271),l=r(3448);let i=a.fC,o=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)(a.aV,{ref:s,className:(0,l.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",r),...t})});o.displayName=a.aV.displayName;let c=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)(a.xz,{ref:s,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",r),...t})});c.displayName=a.xz.displayName;let d=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,n.jsx)(a.VY,{ref:s,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...t})});d.displayName=a.VY.displayName},1399:function(e,s,r){"use strict";r.d(s,{h:function(){return t}});var n=r(8933);async function t(){try{let e=(0,n.s3)(),{error:s}=await e.auth.signOut();if(s)return console.error("Error signing out:",s),!1;return window.location.href="/login",!0}catch(e){return console.error("Unexpected error during logout:",e),!1}}},8933:function(e,s,r){"use strict";r.d(s,{eI:function(){return o},s3:function(){return c}});var n=r(3777),t=r(257);let a=null,l="https://eqghwtejdnzgopmcjlho.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY";if(t.env.SUPABASE_SERVICE_ROLE_KEY,!l||!i)throw Error("Missing Supabase environment variables");let o=()=>(a||(a=(0,n.eI)(l,i,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})),a),c=()=>o()},3448:function(e,s,r){"use strict";r.d(s,{cn:function(){return a}});var n=r(1994),t=r(3335);function a(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,t.m6)((0,n.W)(s))}},271:function(e,s,r){"use strict";r.d(s,{VY:function(){return V},aV:function(){return I},fC:function(){return M},xz:function(){return R}});var n=r(2265),t=r(6741),a=r(3966),l=r(1353),i=r(1599),o=r(6840),c=r(9114),d=r(886),u=r(9255),h=r(7437),f="Tabs",[m,x]=(0,a.b)(f,[l.Pc]),p=(0,l.Pc)(),[g,j]=m(f),y=n.forwardRef((e,s)=>{let{__scopeTabs:r,value:n,onValueChange:t,defaultValue:a,orientation:l="horizontal",dir:i,activationMode:f="automatic",...m}=e,x=(0,c.gm)(i),[p,j]=(0,d.T)({prop:n,onChange:t,defaultProp:a});return(0,h.jsx)(g,{scope:r,baseId:(0,u.M)(),value:p,onValueChange:j,orientation:l,dir:x,activationMode:f,children:(0,h.jsx)(o.WV.div,{dir:x,"data-orientation":l,...m,ref:s})})});y.displayName=f;var b="TabsList",v=n.forwardRef((e,s)=>{let{__scopeTabs:r,loop:n=!0,...t}=e,a=j(b,r),i=p(r);return(0,h.jsx)(l.fC,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:n,children:(0,h.jsx)(o.WV.div,{role:"tablist","aria-orientation":a.orientation,...t,ref:s})})});v.displayName=b;var N="TabsTrigger",w=n.forwardRef((e,s)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...i}=e,c=j(N,r),d=p(r),u=Z(c.baseId,n),f=S(c.baseId,n),m=n===c.value;return(0,h.jsx)(l.ck,{asChild:!0,...d,focusable:!a,active:m,children:(0,h.jsx)(o.WV.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":f,"data-state":m?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:u,...i,ref:s,onMouseDown:(0,t.M)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(n)}),onKeyDown:(0,t.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(n)}),onFocus:(0,t.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;m||a||!e||c.onValueChange(n)})})})});w.displayName=N;var k="TabsContent",C=n.forwardRef((e,s)=>{let{__scopeTabs:r,value:t,forceMount:a,children:l,...c}=e,d=j(k,r),u=Z(d.baseId,t),f=S(d.baseId,t),m=t===d.value,x=n.useRef(m);return n.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,h.jsx)(i.z,{present:a||m,children:r=>{let{present:n}=r;return(0,h.jsx)(o.WV.div,{"data-state":m?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!n,id:f,tabIndex:0,...c,ref:s,style:{...e.style,animationDuration:x.current?"0s":void 0},children:n&&l})}})});function Z(e,s){return"".concat(e,"-trigger-").concat(s)}function S(e,s){return"".concat(e,"-content-").concat(s)}C.displayName=k;var M=y,I=v,R=w,V=C}},function(e){e.O(0,[569,466,216,513,971,117,744],function(){return e(e.s=1689)}),_N_E=e.O()}]);