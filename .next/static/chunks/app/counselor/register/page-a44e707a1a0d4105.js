(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[219],{9991:function(e,r,t){Promise.resolve().then(t.bind(t,7190))},9322:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});let n=(0,t(9205).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},1817:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});let n=(0,t(9205).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1884:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});let n=(0,t(9205).Z)("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9376:function(e,r,t){"use strict";var n=t(5475);t.o(n,"useParams")&&t.d(r,{useParams:function(){return n.useParams}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}})},7190:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return p}});var n=t(7437),s=t(2265),a=t(9376),o=t(9820),i=t(2381),l=t(279),c=t(5060),d=t(1884),u=t(9322),f=t(1817),m=t(7648);function p(){let e=(0,a.useRouter)(),[r,t]=(0,s.useState)(""),[p,h]=(0,s.useState)(""),[x,y]=(0,s.useState)(""),[v,g]=(0,s.useState)(""),[j,b]=(0,s.useState)(!1),[w,N]=(0,s.useState)(null),k=async t=>{t.preventDefault(),b(!0),N(null);try{if("MarriageMapCounselor"!==v)throw Error("Invalid counselor authorization code");let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r,password:p,role:"counselor",fullName:x||r.split("@")[0]})}),n=await t.json();if(!t.ok)throw Error(n.error||"Counselor registration failed");e.push("/login?role=counselor")}catch(e){console.error("Counselor registration error:",e),N(e instanceof Error?e.message:"An error occurred during counselor registration")}finally{b(!1)}};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,n.jsxs)("div",{className:"w-full max-w-md",children:[(0,n.jsx)("div",{className:"flex justify-center mb-8",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(d.Z,{className:"h-8 w-8 text-primary"}),(0,n.jsx)("span",{className:"text-2xl font-bold",children:"Counselor Registration"})]})}),(0,n.jsxs)(o.Zb,{children:[(0,n.jsxs)(o.Ol,{children:[(0,n.jsx)(o.ll,{children:"Register as Counselor"}),(0,n.jsx)(o.SZ,{children:"Create a counselor account (requires authorization code)"})]}),(0,n.jsxs)("form",{onSubmit:k,children:[(0,n.jsxs)(o.aY,{className:"space-y-4",children:[w&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(u.Z,{className:"h-4 w-4 mr-2"}),w]})}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(c._,{htmlFor:"email",children:"Email"}),(0,n.jsx)(l.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:r,onChange:e=>t(e.target.value),required:!0})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(c._,{htmlFor:"full-name",children:"Full Name"}),(0,n.jsx)(l.I,{id:"full-name",type:"text",placeholder:"John Doe",value:x,onChange:e=>y(e.target.value)})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(c._,{htmlFor:"password",children:"Password"}),(0,n.jsx)(l.I,{id:"password",type:"password",placeholder:"••••••••",value:p,onChange:e=>h(e.target.value),required:!0})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(c._,{htmlFor:"counselor-code",children:"Counselor Authorization Code"}),(0,n.jsx)(l.I,{id:"counselor-code",type:"password",placeholder:"Enter counselor code",value:v,onChange:e=>g(e.target.value),required:!0})]})]}),(0,n.jsxs)(o.eW,{className:"flex flex-col space-y-4",children:[(0,n.jsx)(i.z,{type:"submit",className:"w-full",disabled:j,children:j?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(f.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating counselor account..."]}):"Register as Counselor"}),(0,n.jsx)("div",{className:"text-center w-full",children:(0,n.jsx)(m.default,{href:"/login",className:"text-sm text-primary hover:underline",children:"Back to Login"})})]})]})]})]})})}},2381:function(e,r,t){"use strict";t.d(r,{z:function(){return c}});var n=t(7437),s=t(2265),a=t(7495),o=t(535),i=t(3448);let l=(0,o.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,r)=>{let{className:t,variant:s,size:o,asChild:c=!1,...d}=e,u=c?a.g7:"button";return(0,n.jsx)(u,{className:(0,i.cn)(l({variant:s,size:o,className:t})),ref:r,...d})});c.displayName="Button"},9820:function(e,r,t){"use strict";t.d(r,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return o},aY:function(){return d},eW:function(){return u},ll:function(){return l}});var n=t(7437),s=t(2265),a=t(3448);let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...s})});o.displayName="Card";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",t),...s})});i.displayName="CardHeader";let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("h3",{ref:r,className:(0,a.cn)("font-semibold leading-none tracking-tight",t),...s})});l.displayName="CardTitle";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",t),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",t),...s})});d.displayName="CardContent";let u=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)(" flex items-center p-6 pt-0",t),...s})});u.displayName="CardFooter"},279:function(e,r,t){"use strict";t.d(r,{I:function(){return o}});var n=t(7437),s=t(2265),a=t(3448);let o=s.forwardRef((e,r)=>{let{className:t,type:s,...o}=e;return(0,n.jsx)("input",{type:s,className:(0,a.cn)("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...o})});o.displayName="Input"},5060:function(e,r,t){"use strict";t.d(r,{_:function(){return c}});var n=t(7437),s=t(2265),a=t(6394),o=t(535),i=t(3448);let l=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)(a.f,{ref:r,className:(0,i.cn)(l(),t),...s})});c.displayName=a.f.displayName},3448:function(e,r,t){"use strict";t.d(r,{cn:function(){return a}});var n=t(1994),s=t(3335);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.m6)((0,n.W)(r))}},6394:function(e,r,t){"use strict";t.d(r,{f:function(){return i}});var n=t(2265),s=t(6840),a=t(7437),o=n.forwardRef((e,r)=>(0,a.jsx)(s.WV.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null===(t=e.onMouseDown)||void 0===t||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));o.displayName="Label";var i=o},6840:function(e,r,t){"use strict";t.d(r,{WV:function(){return i},jH:function(){return l}});var n=t(2265),s=t(4887),a=t(7495),o=t(7437),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=n.forwardRef((e,t)=>{let{asChild:n,...s}=e,i=n?a.g7:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i,{...s,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{});function l(e,r){e&&s.flushSync(()=>e.dispatchEvent(r))}}},function(e){e.O(0,[569,216,971,117,744],function(){return e(e.s=9991)}),_N_E=e.O()}]);