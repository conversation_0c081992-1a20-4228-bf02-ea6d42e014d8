(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[18],{8407:function(e,t,s){Promise.resolve().then(s.bind(s,1901))},505:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9205).Z)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9322:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9205).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},1671:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9205).Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1723:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9205).Z)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},8736:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9205).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},8997:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9205).Z)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},1817:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9205).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7692:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9205).Z)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},2718:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9205).Z)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1884:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});let n=(0,s(9205).Z)("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9376:function(e,t,s){"use strict";var n=s(5475);s.o(n,"useParams")&&s.d(t,{useParams:function(){return n.useParams}}),s.o(n,"useRouter")&&s.d(t,{useRouter:function(){return n.useRouter}}),s.o(n,"useSearchParams")&&s.d(t,{useSearchParams:function(){return n.useSearchParams}})},1901:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return b}});var n=s(7437),r=s(2265),l=s(9376),a=s(9820),i=s(2381),o=s(7204),c=s(8933),d=s(1399),u=s(1817),m=s(8997),p=s(8736),h=s(1671),f=s(505),x=s(2718),g=s(7692),v=s(9322),j=s(1723),y=s(1884),N=s(7648);function b(){let e=(0,l.useRouter)(),[t,s]=(0,r.useState)(!0),[b,w]=(0,r.useState)(null),[k,C]=(0,r.useState)({userName:"",coupleCode:"",partnerName:"",isConnected:!1,completedDomains:[],counselorName:"",nextSessionDate:null});(0,r.useEffect)(()=>{(async()=>{try{var e,t,n,r;s(!0);let l=(0,c.s3)(),{data:{user:a}}=await l.auth.getUser();if(!a){window.location.href="/login";return}let{data:i}=await l.from("profiles").select("*").eq("id",a.id).single(),{data:o}=await l.from("individual_results").select("domains").eq("user_id",a.id).single(),{data:{session:d}}=await l.auth.getSession(),u=null==d?void 0:d.access_token,m=null,p=null;if(u)try{let e=await fetch("/api/couples/status",{headers:{Authorization:"Bearer ".concat(u)}});e.ok&&(m=await e.json()).isConnected&&m.couple&&(p={couple_id:m.couple.friendlyCode,profiles:{full_name:m.partner.displayName}})}catch(e){}let h="",f=null;if(null==p?void 0:p.couple_id){let{data:e}=await l.from("counselor_couple_assignments").select("*, counselor_profiles(full_name)").eq("couple_id",p.couple_id).single();if(e){h=(null===(r=e.counselor_profiles)||void 0===r?void 0:r.full_name)||"";let{data:t}=await l.from("counseling_sessions").select("*").eq("couple_id",p.couple_id).eq("status","scheduled").order("session_date",{ascending:!0}).limit(1).single();t&&(f=new Date(t.session_date))}}let x=(null==o?void 0:null===(e=o.domains)||void 0===e?void 0:e.map(e=>e.domain.toLowerCase()))||[];C({userName:(null==i?void 0:i.full_name)||(null===(t=a.email)||void 0===t?void 0:t.split("@")[0])||"User",coupleCode:(null==p?void 0:p.couple_id)||"",partnerName:(null==p?void 0:null===(n=p.profiles)||void 0===n?void 0:n.full_name)||"",isConnected:(null==m?void 0:m.isConnected)||!1,completedDomains:x,counselorName:h,nextSessionDate:f})}catch(e){console.error("Error fetching user data:",e),w(e instanceof Error?e.message:"An error occurred while loading your dashboard")}finally{s(!1)}})()},[]);let D=[{id:"vision",title:"Vision",description:"Life goals and future plans",icon:"\uD83D\uDD2D",completed:k.completedDomains.includes("vision"),status:k.completedDomains.includes("vision")?"completed":"not-started"},{id:"finances",title:"Finances",description:"Money management and financial goals",icon:"\uD83D\uDCB0",completed:k.completedDomains.includes("finances"),status:k.completedDomains.includes("finances")?"completed":"not-started"},{id:"parenting",title:"Parenting",description:"Child-rearing philosophies and approaches",icon:"\uD83D\uDC76",completed:k.completedDomains.includes("parenting"),status:k.completedDomains.includes("parenting")?"completed":"not-started"},{id:"communication",title:"Communication",description:"Styles and patterns of interaction",icon:"\uD83D\uDCAC",completed:k.completedDomains.includes("communication"),status:k.completedDomains.includes("communication")?"completed":"not-started"},{id:"roles",title:"Roles",description:"Functions and responsibilities in marriage",icon:"\uD83D\uDD04",completed:k.completedDomains.includes("roles"),status:k.completedDomains.includes("roles")?"completed":"not-started"},{id:"sexuality",title:"Sexuality",description:"Intimacy and physical relationship",icon:"❤️",completed:k.completedDomains.includes("sexuality"),status:k.completedDomains.includes("sexuality")?"completed":"not-started"},{id:"spirituality",title:"Spirituality",description:"Faith practices and spiritual growth",icon:"✝️",completed:k.completedDomains.includes("spirituality"),status:k.completedDomains.includes("spirituality")?"completed":"not-started"},{id:"darkside",title:"Dark Side",description:"Potential challenges and negative patterns",icon:"\uD83C\uDF11",completed:k.completedDomains.includes("darkside"),status:k.completedDomains.includes("darkside")?"completed":"not-started"}],Z=Math.round(k.completedDomains.length/D.length*100);return t?(0,n.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,n.jsx)(u.Z,{className:"h-8 w-8 animate-spin text-primary"}),(0,n.jsx)("span",{className:"ml-2",children:"Loading your dashboard..."})]}):(0,n.jsx)("div",{className:"min-h-screen bg-background",children:(0,n.jsxs)("div",{className:"flex",children:[(0,n.jsx)("div",{className:"hidden md:flex w-64 flex-col fixed inset-y-0 z-50 border-r bg-background",children:(0,n.jsxs)("div",{className:"flex flex-col space-y-6 p-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 px-2 py-4",children:[(0,n.jsx)(m.Z,{className:"h-6 w-6 text-primary"}),(0,n.jsx)("span",{className:"text-xl font-bold",children:"Couple Portal"})]}),(0,n.jsxs)("nav",{className:"flex flex-col space-y-1",children:[(0,n.jsxs)(N.default,{href:"/couple/dashboard",className:"flex items-center gap-3 rounded-lg bg-primary/10 px-3 py-2 text-primary transition-all hover:text-primary",children:[(0,n.jsx)(m.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Dashboard"})]}),(0,n.jsxs)(N.default,{href:"/couple/assessments",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,n.jsx)(p.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Assessments"})]}),(0,n.jsxs)(N.default,{href:"/couple/results",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,n.jsx)(h.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Results"})]}),(0,n.jsxs)(N.default,{href:"/couple/sessions",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,n.jsx)(f.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Sessions"})]}),(0,n.jsxs)(N.default,{href:"/couple/messages",className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",children:[(0,n.jsx)(x.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Messages"})]}),(0,n.jsxs)("button",{onClick:d.h,className:"flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:bg-red-50 hover:text-red-600 w-full mt-4",children:[(0,n.jsx)(g.Z,{className:"h-5 w-5"}),(0,n.jsx)("span",{children:"Logout"})]})]})]})}),(0,n.jsx)("div",{className:"flex-1 md:pl-64",children:(0,n.jsxs)("div",{className:"container py-8",children:[(0,n.jsx)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6",children:(0,n.jsxs)("div",{children:[(0,n.jsxs)("h1",{className:"text-3xl font-bold tracking-tight",children:["Welcome, ",k.userName]}),(0,n.jsx)("p",{className:"text-muted-foreground",children:k.isConnected?"Connected with ".concat(k.partnerName):"Complete your assessment and connect with your partner"})]})}),b&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(v.Z,{className:"h-4 w-4 mr-2"}),b]})}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,n.jsxs)("div",{className:"md:col-span-2 space-y-6",children:[(0,n.jsxs)(a.Zb,{children:[(0,n.jsxs)(a.Ol,{children:[(0,n.jsx)(a.ll,{children:"Assessment Progress"}),(0,n.jsx)(a.SZ,{children:"Complete all 8 domains to get comprehensive insights"})]}),(0,n.jsxs)(a.aY,{children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,n.jsxs)("span",{children:[k.completedDomains.length," of ",D.length," ","completed"]}),(0,n.jsxs)("span",{children:[Z,"%"]})]}),(0,n.jsx)(o.E,{value:Z,className:"h-2"})]}),(0,n.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 mt-6",children:D.map(e=>(0,n.jsxs)("div",{className:"flex flex-col items-center p-3 border rounded-lg",children:[(0,n.jsx)("div",{className:"text-2xl mb-1",children:e.icon}),(0,n.jsx)("div",{className:"text-sm font-medium",children:e.title}),(0,n.jsx)("div",{className:"mt-1",children:e.completed?(0,n.jsx)(h.Z,{className:"h-4 w-4 text-green-500"}):(0,n.jsx)(j.Z,{className:"h-4 w-4 text-amber-500"})})]},e.id))})]}),(0,n.jsx)(a.eW,{children:(0,n.jsx)(i.z,{className:"w-full",onClick:()=>e.push("/dashboard"),children:"Continue Assessment"})})]}),k.isConnected&&k.completedDomains.length>0&&(0,n.jsxs)(a.Zb,{children:[(0,n.jsxs)(a.Ol,{children:[(0,n.jsx)(a.ll,{children:"Compatibility Results"}),(0,n.jsx)(a.SZ,{children:"See how your responses align with your partner's"})]}),(0,n.jsx)(a.aY,{children:(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-muted-foreground mb-4",children:k.completedDomains.length===D.length?"All assessments completed! View your detailed results below.":"Complete all assessment domains to view detailed compatibility results."})})}),(0,n.jsx)(a.eW,{children:(0,n.jsx)(i.z,{className:"w-full",disabled:k.completedDomains.length!==D.length,onClick:()=>e.push("/couple/results"),children:"View Detailed Results"})})]})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)(a.Zb,{children:[(0,n.jsx)(a.Ol,{children:(0,n.jsx)(a.ll,{children:"Connection Status"})}),(0,n.jsx)(a.aY,{children:k.isConnected?(0,n.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,n.jsx)("div",{className:"bg-green-50 rounded-full p-3 mb-3",children:(0,n.jsx)(h.Z,{className:"h-6 w-6 text-green-500"})}),(0,n.jsx)("p",{className:"font-medium",children:"Connected with Partner"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:k.partnerName}),(0,n.jsxs)("div",{className:"mt-4 p-2 bg-muted rounded-md w-full",children:[(0,n.jsx)("p",{className:"text-xs font-medium",children:"Couple Code"}),(0,n.jsx)("p",{className:"font-mono text-sm",children:k.coupleCode})]})]}):(0,n.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,n.jsx)("div",{className:"bg-amber-50 rounded-full p-3 mb-3",children:(0,n.jsx)(v.Z,{className:"h-6 w-6 text-amber-500"})}),(0,n.jsx)("p",{className:"font-medium",children:"Not Connected"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Connect with your partner to compare results"}),(0,n.jsx)(i.z,{className:"mt-4 w-full",onClick:()=>e.push("/dashboard?tab=connect"),children:"Connect Now"})]})})]}),(0,n.jsxs)(a.Zb,{children:[(0,n.jsx)(a.Ol,{children:(0,n.jsx)(a.ll,{children:"Counselor"})}),(0,n.jsx)(a.aY,{children:k.counselorName?(0,n.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,n.jsx)("div",{className:"bg-primary/10 rounded-full p-3 mb-3",children:(0,n.jsx)(y.Z,{className:"h-6 w-6 text-primary"})}),(0,n.jsx)("p",{className:"font-medium",children:k.counselorName}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Your assigned counselor"}),(0,n.jsx)(i.z,{className:"mt-4 w-full",variant:"outline",onClick:()=>e.push("/couple/messages"),children:"Message Counselor"})]}):(0,n.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,n.jsx)("div",{className:"bg-muted rounded-full p-3 mb-3",children:(0,n.jsx)(y.Z,{className:"h-6 w-6 text-muted-foreground"})}),(0,n.jsx)("p",{className:"font-medium",children:"No Counselor Assigned"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Complete your assessments to get matched with a counselor"})]})})]}),k.nextSessionDate&&(0,n.jsxs)(a.Zb,{children:[(0,n.jsx)(a.Ol,{children:(0,n.jsx)(a.ll,{children:"Next Session"})}),(0,n.jsx)(a.aY,{children:(0,n.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,n.jsx)("div",{className:"bg-primary/10 rounded-full p-3 mb-3",children:(0,n.jsx)(f.Z,{className:"h-6 w-6 text-primary"})}),(0,n.jsx)("p",{className:"font-medium",children:k.nextSessionDate.toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric"})}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:k.nextSessionDate.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit"})}),(0,n.jsx)(i.z,{className:"mt-4 w-full",onClick:()=>e.push("/couple/sessions"),children:"View Details"})]})})]}),(0,n.jsxs)(a.Zb,{children:[(0,n.jsx)(a.Ol,{children:(0,n.jsx)(a.ll,{children:"Quick Actions"})}),(0,n.jsxs)(a.aY,{className:"space-y-2",children:[(0,n.jsxs)(i.z,{variant:"outline",className:"w-full justify-start",onClick:()=>e.push("/dashboard"),children:[(0,n.jsx)(p.Z,{className:"mr-2 h-4 w-4"}),"Continue Assessment"]}),(0,n.jsxs)(i.z,{variant:"outline",className:"w-full justify-start",onClick:()=>e.push("/couple/results"),disabled:0===k.completedDomains.length,children:[(0,n.jsx)(h.Z,{className:"mr-2 h-4 w-4"}),"View Results"]}),(0,n.jsxs)(i.z,{variant:"outline",className:"w-full justify-start",onClick:()=>e.push("/couple/sessions/schedule"),disabled:!k.counselorName,children:[(0,n.jsx)(f.Z,{className:"mr-2 h-4 w-4"}),"Schedule Session"]})]})]})]})]})]})})]})})}},2381:function(e,t,s){"use strict";s.d(t,{z:function(){return c}});var n=s(7437),r=s(2265),l=s(7495),a=s(535),i=s(3448);let o=(0,a.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:s,variant:r,size:a,asChild:c=!1,...d}=e,u=c?l.g7:"button";return(0,n.jsx)(u,{className:(0,i.cn)(o({variant:r,size:a,className:s})),ref:t,...d})});c.displayName="Button"},9820:function(e,t,s){"use strict";s.d(t,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return a},aY:function(){return d},eW:function(){return u},ll:function(){return o}});var n=s(7437),r=s(2265),l=s(3448);let a=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow",s),...r})});a.displayName="Card";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...r})});i.displayName="CardHeader";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("h3",{ref:t,className:(0,l.cn)("font-semibold leading-none tracking-tight",s),...r})});o.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})});c.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...r})});d.displayName="CardContent";let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)(" flex items-center p-6 pt-0",s),...r})});u.displayName="CardFooter"},7204:function(e,t,s){"use strict";s.d(t,{E:function(){return i}});var n=s(7437),r=s(2265),l=s(610),a=s(3448);let i=r.forwardRef((e,t)=>{let{className:s,value:r,...i}=e;return(0,n.jsx)(l.fC,{ref:t,className:(0,a.cn)("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",s),...i,children:(0,n.jsx)(l.z$,{className:"h-full w-full flex-1 bg-primary transition-all bg-white",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});i.displayName=l.fC.displayName},1399:function(e,t,s){"use strict";s.d(t,{h:function(){return r}});var n=s(8933);async function r(){try{let e=(0,n.s3)(),{error:t}=await e.auth.signOut();if(t)return console.error("Error signing out:",t),!1;return window.location.href="/login",!0}catch(e){return console.error("Unexpected error during logout:",e),!1}}},8933:function(e,t,s){"use strict";s.d(t,{eI:function(){return o},s3:function(){return c}});var n=s(3777),r=s(257);let l=null,a="https://eqghwtejdnzgopmcjlho.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxZ2h3dGVqZG56Z29wbWNqbGhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjk4MDIsImV4cCI6MjA2MzkwNTgwMn0.QqMVwRWBkSzmI9NgtjuAbv6sZq069O7XSb4J2PED9yY";if(r.env.SUPABASE_SERVICE_ROLE_KEY,!a||!i)throw Error("Missing Supabase environment variables");let o=()=>(l||(l=(0,n.eI)(a,i,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})),l),c=()=>o()},3448:function(e,t,s){"use strict";s.d(t,{cn:function(){return l}});var n=s(1994),r=s(3335);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.m6)((0,n.W)(t))}},3966:function(e,t,s){"use strict";s.d(t,{b:function(){return l}});var n=s(2265),r=s(7437);function l(e,t=[]){let s=[],l=()=>{let t=s.map(e=>n.createContext(e));return function(s){let r=s?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...s,[e]:r}}),[s,r])}};return l.scopeName=e,[function(t,l){let a=n.createContext(l),i=s.length;s=[...s,l];let o=t=>{let{scope:s,children:l,...o}=t,c=s?.[e]?.[i]||a,d=n.useMemo(()=>o,Object.values(o));return(0,r.jsx)(c.Provider,{value:d,children:l})};return o.displayName=t+"Provider",[o,function(s,r){let o=r?.[e]?.[i]||a,c=n.useContext(o);if(c)return c;if(void 0!==l)return l;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=s.reduce((t,{useScope:s,scopeName:n})=>{let r=s(e)[`__scope${n}`];return{...t,...r}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return s.scopeName=t.scopeName,s}(l,...t)]}},6840:function(e,t,s){"use strict";s.d(t,{WV:function(){return i},jH:function(){return o}});var n=s(2265),r=s(4887),l=s(7495),a=s(7437),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let s=n.forwardRef((e,s)=>{let{asChild:n,...r}=e,i=n?l.g7:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i,{...r,ref:s})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function o(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},610:function(e,t,s){"use strict";s.d(t,{fC:function(){return y},z$:function(){return N}});var n=s(2265),r=s(3966),l=s(6840),a=s(7437),i="Progress",[o,c]=(0,r.b)(i),[d,u]=o(i),m=n.forwardRef((e,t)=>{var s,n,r,i;let{__scopeProgress:o,value:c=null,max:u,getValueLabel:m=f,...p}=e;(u||0===u)&&!v(u)&&console.error((s="".concat(u),n="Progress","Invalid prop `max` of value `".concat(s,"` supplied to `").concat(n,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let h=v(u)?u:100;null===c||j(c,h)||console.error((r="".concat(c),i="Progress","Invalid prop `value` of value `".concat(r,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let y=j(c,h)?c:null,N=g(y)?m(y,h):void 0;return(0,a.jsx)(d,{scope:o,value:y,max:h,children:(0,a.jsx)(l.WV.div,{"aria-valuemax":h,"aria-valuemin":0,"aria-valuenow":g(y)?y:void 0,"aria-valuetext":N,role:"progressbar","data-state":x(y,h),"data-value":null!=y?y:void 0,"data-max":h,...p,ref:t})})});m.displayName=i;var p="ProgressIndicator",h=n.forwardRef((e,t)=>{var s;let{__scopeProgress:n,...r}=e,i=u(p,n);return(0,a.jsx)(l.WV.div,{"data-state":x(i.value,i.max),"data-value":null!==(s=i.value)&&void 0!==s?s:void 0,"data-max":i.max,...r,ref:t})});function f(e,t){return"".concat(Math.round(e/t*100),"%")}function x(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function v(e){return g(e)&&!isNaN(e)&&e>0}function j(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=p;var y=m,N=h}},function(e){e.O(0,[569,466,216,971,117,744],function(){return e(e.s=8407)}),_N_E=e.O()}]);