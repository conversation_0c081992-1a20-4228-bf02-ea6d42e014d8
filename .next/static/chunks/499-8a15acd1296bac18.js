"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[499],{9376:function(e,t,n){var r=n(5475);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},9270:function(e,t,n){n.d(t,{fC:function(){return C},z$:function(){return R}});var r=n(2265),o=n(8575),i=n(3966),l=n(6741),a=n(886),u=n(6718),c=n(420),s=n(1599),d=n(6840),f=n(7437),p="Checkbox",[v,h]=(0,i.b)(p),[m,g]=v(p),y=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:i,checked:u,defaultChecked:c,required:s,disabled:p,value:v="on",onCheckedChange:h,form:g,...y}=e,[w,b]=r.useState(null),C=(0,o.e)(t,e=>b(e)),R=r.useRef(!1),T=!w||g||!!w.closest("form"),[P=!1,k]=(0,a.T)({prop:u,defaultProp:c,onChange:h}),L=r.useRef(P);return r.useEffect(()=>{let e=null==w?void 0:w.form;if(e){let t=()=>k(L.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[w,k]),(0,f.jsxs)(m,{scope:n,state:P,disabled:p,children:[(0,f.jsx)(d.WV.button,{type:"button",role:"checkbox","aria-checked":E(P)?"mixed":P,"aria-required":s,"data-state":S(P),"data-disabled":p?"":void 0,disabled:p,value:v,...y,ref:C,onKeyDown:(0,l.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.M)(e.onClick,e=>{k(e=>!!E(e)||!e),T&&(R.current=e.isPropagationStopped(),R.current||e.stopPropagation())})}),T&&(0,f.jsx)(x,{control:w,bubbles:!R.current,name:i,value:v,checked:P,required:s,disabled:p,form:g,style:{transform:"translateX(-100%)"},defaultChecked:!E(c)&&c})]})});y.displayName=p;var w="CheckboxIndicator",b=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,i=g(w,n);return(0,f.jsx)(s.z,{present:r||E(i.state)||!0===i.state,children:(0,f.jsx)(d.WV.span,{"data-state":S(i.state),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});b.displayName=w;var x=e=>{let{control:t,checked:n,bubbles:o=!0,defaultChecked:i,...l}=e,a=r.useRef(null),s=(0,u.D)(n),d=(0,c.t)(t);r.useEffect(()=>{let e=a.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(s!==n&&t){let r=new Event("click",{bubbles:o});e.indeterminate=E(n),t.call(e,!E(n)&&n),e.dispatchEvent(r)}},[s,n,o]);let p=r.useRef(!E(n)&&n);return(0,f.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=i?i:p.current,...l,tabIndex:-1,ref:a,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function E(e){return"indeterminate"===e}function S(e){return E(e)?"indeterminate":e?"checked":"unchecked"}var C=y,R=b},6394:function(e,t,n){n.d(t,{f:function(){return a}});var r=n(2265),o=n(6840),i=n(7437),l=r.forwardRef((e,t)=>(0,i.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},7864:function(e,t,n){let r;n.d(t,{VY:function(){return n4},ZA:function(){return n3},JO:function(){return n7},ck:function(){return rt},wU:function(){return rr},eT:function(){return rn},__:function(){return re},h_:function(){return n8},fC:function(){return n2},Z0:function(){return ro},xz:function(){return n5},B4:function(){return n6},l_:function(){return n9}});var o,i,l,a,u,c,s,d,f=n(2265),p=n(4887);function v(e,[t,n]){return Math.min(n,Math.max(t,e))}var h=n(6741),m=n(7822),g=n(8575),y=n(3966),w=n(9114),b=n(6840),x=n(6606),E=n(7437),S="dismissableLayer.update",C=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),R=f.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:a,onInteractOutside:u,onDismiss:c,...d}=e,p=f.useContext(C),[v,m]=f.useState(null),y=null!==(r=null==v?void 0:v.ownerDocument)&&void 0!==r?r:null===(n=globalThis)||void 0===n?void 0:n.document,[,w]=f.useState({}),R=(0,g.e)(t,e=>m(e)),k=Array.from(p.layers),[L]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),M=k.indexOf(L),A=v?k.indexOf(v):-1,j=p.layersWithOutsidePointerEventsDisabled.size>0,D=A>=M,N=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,x.W)(e),o=f.useRef(!1),i=f.useRef(()=>{});return f.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){P("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...p.branches].some(e=>e.contains(t));!D||n||(null==l||l(e),null==u||u(e),e.defaultPrevented||null==c||c())},y),W=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,x.W)(e),o=f.useRef(!1);return f.useEffect(()=>{let e=e=>{e.target&&!o.current&&P("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...p.branches].some(e=>e.contains(t))||(null==a||a(e),null==u||u(e),e.defaultPrevented||null==c||c())},y);return!function(e,t=globalThis?.document){let n=(0,x.W)(e);f.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{A!==p.layers.size-1||(null==i||i(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},y),f.useEffect(()=>{if(v)return o&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(s=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(v)),p.layers.add(v),T(),()=>{o&&1===p.layersWithOutsidePointerEventsDisabled.size&&(y.body.style.pointerEvents=s)}},[v,y,o,p]),f.useEffect(()=>()=>{v&&(p.layers.delete(v),p.layersWithOutsidePointerEventsDisabled.delete(v),T())},[v,p]),f.useEffect(()=>{let e=()=>w({});return document.addEventListener(S,e),()=>document.removeEventListener(S,e)},[]),(0,E.jsx)(b.WV.div,{...d,ref:R,style:{pointerEvents:j?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,h.M)(e.onFocusCapture,W.onFocusCapture),onBlurCapture:(0,h.M)(e.onBlurCapture,W.onBlurCapture),onPointerDownCapture:(0,h.M)(e.onPointerDownCapture,N.onPointerDownCapture)})});function T(){let e=new CustomEvent(S);document.dispatchEvent(e)}function P(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,b.jH)(i,l):i.dispatchEvent(l)}R.displayName="DismissableLayer",f.forwardRef((e,t)=>{let n=f.useContext(C),r=f.useRef(null),o=(0,g.e)(t,r);return f.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,E.jsx)(b.WV.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var k=0;function L(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var M="focusScope.autoFocusOnMount",A="focusScope.autoFocusOnUnmount",j={bubbles:!1,cancelable:!0},D=f.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[a,u]=f.useState(null),c=(0,x.W)(o),s=(0,x.W)(i),d=f.useRef(null),p=(0,g.e)(t,e=>u(e)),v=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(r){let e=function(e){if(v.paused||!a)return;let t=e.target;a.contains(t)?d.current=t:O(d.current,{select:!0})},t=function(e){if(v.paused||!a)return;let t=e.relatedTarget;null===t||a.contains(t)||O(d.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&O(a)});return a&&n.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,a,v.paused]),f.useEffect(()=>{if(a){I.add(v);let e=document.activeElement;if(!a.contains(e)){let t=new CustomEvent(M,j);a.addEventListener(M,c),a.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(O(r,{select:t}),document.activeElement!==n)return}(N(a).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&O(a))}return()=>{a.removeEventListener(M,c),setTimeout(()=>{let t=new CustomEvent(A,j);a.addEventListener(A,s),a.dispatchEvent(t),t.defaultPrevented||O(null!=e?e:document.body,{select:!0}),a.removeEventListener(A,s),I.remove(v)},0)}}},[a,c,s,v]);let h=f.useCallback(e=>{if(!n&&!r||v.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=N(e);return[W(t,e),W(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&O(i,{select:!0})):(e.preventDefault(),n&&O(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,v.paused]);return(0,E.jsx)(b.WV.div,{tabIndex:-1,...l,ref:p,onKeyDown:h})});function N(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function W(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function O(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}D.displayName="FocusScope";var I=(r=[],{add(e){let t=r[0];e!==t&&(null==t||t.pause()),(r=V(r,e)).unshift(e)},remove(e){var t;null===(t=(r=V(r,e))[0])||void 0===t||t.resume()}});function V(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var F=n(9255);let H=["top","right","bottom","left"],B=Math.min,_=Math.max,z=Math.round,K=Math.floor,Y=e=>({x:e,y:e}),X={left:"right",right:"left",bottom:"top",top:"bottom"},U={start:"end",end:"start"};function q(e,t){return"function"==typeof e?e(t):e}function Z(e){return e.split("-")[0]}function $(e){return e.split("-")[1]}function G(e){return"x"===e?"y":"x"}function J(e){return"y"===e?"height":"width"}function Q(e){return["top","bottom"].includes(Z(e))?"y":"x"}function ee(e){return e.replace(/start|end/g,e=>U[e])}function et(e){return e.replace(/left|right|bottom|top/g,e=>X[e])}function en(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function er(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function eo(e,t,n){let r,{reference:o,floating:i}=e,l=Q(t),a=G(Q(t)),u=J(a),c=Z(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch($(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let ei=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=eo(c,r,u),f=r,p={},v=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:m,y:g,data:y,reset:w}=await h({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=m?m:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&v<=50&&(v++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=eo(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function el(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=q(t,e),v=en(p),h=a[f?"floating"===d?"reference":"floating":d],m=er(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=er(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(m.top-b.top+v.top)/w.y,bottom:(b.bottom-m.bottom+v.bottom)/w.y,left:(m.left-b.left+v.left)/w.x,right:(b.right-m.right+v.right)/w.x}}function ea(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function eu(e){return H.some(t=>e[t]>=0)}async function ec(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=Z(n),a=$(n),u="y"===Q(n),c=["left","top"].includes(l)?-1:1,s=i&&u?-1:1,d=q(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:v}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof v&&(p="end"===a?-1*v:v),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function es(){return"undefined"!=typeof window}function ed(e){return ev(e)?(e.nodeName||"").toLowerCase():"#document"}function ef(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ep(e){var t;return null==(t=(ev(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ev(e){return!!es()&&(e instanceof Node||e instanceof ef(e).Node)}function eh(e){return!!es()&&(e instanceof Element||e instanceof ef(e).Element)}function em(e){return!!es()&&(e instanceof HTMLElement||e instanceof ef(e).HTMLElement)}function eg(e){return!!es()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ef(e).ShadowRoot)}function ey(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eS(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function ew(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function eb(e){let t=ex(),n=eh(e)?eS(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function ex(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function eE(e){return["html","body","#document"].includes(ed(e))}function eS(e){return ef(e).getComputedStyle(e)}function eC(e){return eh(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eR(e){if("html"===ed(e))return e;let t=e.assignedSlot||e.parentNode||eg(e)&&e.host||ep(e);return eg(t)?t.host:t}function eT(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eR(t);return eE(n)?t.ownerDocument?t.ownerDocument.body:t.body:em(n)&&ey(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=ef(o);if(i){let e=eP(l);return t.concat(l,l.visualViewport||[],ey(o)?o:[],e&&n?eT(e):[])}return t.concat(o,eT(o,[],n))}function eP(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ek(e){let t=eS(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=em(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=z(n)!==i||z(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eL(e){return eh(e)?e:e.contextElement}function eM(e){let t=eL(e);if(!em(t))return Y(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ek(t),l=(i?z(n.width):n.width)/r,a=(i?z(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let eA=Y(0);function ej(e){let t=ef(e);return ex()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eA}function eD(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eL(e),a=Y(1);t&&(r?eh(r)&&(a=eM(r)):a=eM(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===ef(l))&&o)?ej(l):Y(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=ef(l),t=r&&eh(r)?ef(r):r,n=e,o=eP(n);for(;o&&r&&t!==n;){let e=eM(o),t=o.getBoundingClientRect(),r=eS(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=l,o=eP(n=ef(o))}}return er({width:d,height:f,x:c,y:s})}function eN(e,t){let n=eC(e).scrollLeft;return t?t.left+n:eD(ep(e)).left+n}function eW(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eN(e,r)),y:r.top+t.scrollTop}}function eO(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ef(e),r=ep(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=ex();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=ep(e),n=eC(e),r=e.ownerDocument.body,o=_(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=_(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eN(e),a=-n.scrollTop;return"rtl"===eS(r).direction&&(l+=_(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(ep(e));else if(eh(t))r=function(e,t){let n=eD(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=em(e)?eM(e):Y(1),l=e.clientWidth*i.x;return{width:l,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=ej(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return er(r)}function eI(e){return"static"===eS(e).position}function eV(e,t){if(!em(e)||"fixed"===eS(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ep(e)===n&&(n=n.ownerDocument.body),n}function eF(e,t){let n=ef(e);if(ew(e))return n;if(!em(e)){let t=eR(e);for(;t&&!eE(t);){if(eh(t)&&!eI(t))return t;t=eR(t)}return n}let r=eV(e,t);for(;r&&["table","td","th"].includes(ed(r))&&eI(r);)r=eV(r,t);return r&&eE(r)&&eI(r)&&!eb(r)?n:r||function(e){let t=eR(e);for(;em(t)&&!eE(t);){if(eb(t))return t;if(ew(t))break;t=eR(t)}return null}(e)||n}let eH=async function(e){let t=this.getOffsetParent||eF,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=em(t),o=ep(t),i="fixed"===n,l=eD(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=Y(0);if(r||!r&&!i){if(("body"!==ed(t)||ey(o))&&(a=eC(t)),r){let e=eD(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eN(o))}let c=!o||r||i?Y(0):eW(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eB={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=ep(r),a=!!t&&ew(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=Y(1),s=Y(0),d=em(r);if((d||!d&&!i)&&(("body"!==ed(r)||ey(l))&&(u=eC(r)),em(r))){let e=eD(r);c=eM(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!l||d||i?Y(0):eW(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:ep,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?ew(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eT(e,[],!1).filter(e=>eh(e)&&"body"!==ed(e)),o=null,i="fixed"===eS(e).position,l=i?eR(e):e;for(;eh(l)&&!eE(l);){let t=eS(l),n=eb(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||ey(l)&&!n&&function e(t,n){let r=eR(t);return!(r===n||!eh(r)||eE(r))&&("fixed"===eS(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eR(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=eO(t,n,o);return e.top=_(r.top,e.top),e.right=B(r.right,e.right),e.bottom=B(r.bottom,e.bottom),e.left=_(r.left,e.left),e},eO(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eF,getElementRects:eH,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ek(e);return{width:t,height:n}},getScale:eM,isElement:eh,isRTL:function(e){return"rtl"===eS(e).direction}};function e_(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ez=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=q(e,t)||{};if(null==c)return{};let d=en(s),f={x:n,y:r},p=G(Q(o)),v=J(p),h=await l.getDimensions(c),m="y"===p,g=m?"clientHeight":"clientWidth",y=i.reference[v]+i.reference[p]-f[p]-i.floating[v],w=f[p]-i.reference[p],b=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),x=b?b[g]:0;x&&await (null==l.isElement?void 0:l.isElement(b))||(x=a.floating[g]||i.floating[v]);let E=x/2-h[v]/2-1,S=B(d[m?"top":"left"],E),C=B(d[m?"bottom":"right"],E),R=x-h[v]-C,T=x/2-h[v]/2+(y/2-w/2),P=_(S,B(T,R)),k=!u.arrow&&null!=$(o)&&T!==P&&i.reference[v]/2-(T<S?S:C)-h[v]/2<0,L=k?T<S?T-S:T-R:0;return{[p]:f[p]+L,data:{[p]:P,centerOffset:T-P-L,...k&&{alignmentOffset:L}},reset:k}}}),eK=(e,t,n)=>{let r=new Map,o={platform:eB,...n},i={...o.platform,_c:r};return ei(e,t,{...o,platform:i})};var eY="undefined"!=typeof document?f.useLayoutEffect:f.useEffect;function eX(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eX(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eX(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eU(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eq(e,t){let n=eU(e);return Math.round(t*n)/n}function eZ(e){let t=f.useRef(e);return eY(()=>{t.current=e}),t}let e$=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ez({element:n.current,padding:r}).fn(t):{}:n?ez({element:n,padding:r}).fn(t):{}}}),eG=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:o,y:i,placement:l,middlewareData:a}=e,u=await ec(e,n);return l===(null==(t=a.offset)?void 0:t.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}),options:[e,t]}},eJ=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=q(n,e),c={x:t,y:r},s=await el(e,u),d=Q(Z(o)),f=G(d),p=c[f],v=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=_(n,B(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=v+s[e],r=v-s[t];v=_(n,B(v,r))}let h=a.fn({...e,[f]:p,[d]:v});return{...h,data:{x:h.x-t,y:h.y-r,enabled:{[f]:i,[d]:l}}}}}),options:[e,t]}},eQ=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:o,rects:i,middlewareData:l}=e,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=q(n,e),s={x:t,y:r},d=Q(o),f=G(d),p=s[f],v=s[d],h=q(a,e),m="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+m.mainAxis,n=i.reference[f]+i.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=["top","left"].includes(Z(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:m.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?m.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[f]:p,[d]:v}}}),options:[e,t]}},e0=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=e,{mainAxis:p=!0,crossAxis:v=!0,fallbackPlacements:h,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=q(n,e);if(null!=(t=u.arrow)&&t.alignmentOffset)return{};let b=Z(a),x=Q(s),E=Z(s)===s,S=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=h||(E||!y?[et(s)]:function(e){let t=et(e);return[ee(e),t,ee(t)]}(s)),R="none"!==g;!h&&R&&C.push(...function(e,t,n,r){let o=$(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(Z(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(ee)))),i}(s,y,g,S));let T=[s,...C],P=await el(e,w),k=[],L=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&k.push(P[b]),v){let e=function(e,t,n){void 0===n&&(n=!1);let r=$(e),o=G(Q(e)),i=J(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=et(l)),[l,et(l)]}(a,c,S);k.push(P[e[0]],P[e[1]])}if(L=[...L,{placement:a,overflows:k}],!k.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=T[e];if(t)return{data:{index:e,overflows:L},reset:{placement:t}};let n=null==(i=L.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(m){case"bestFit":{let e=null==(l=L.filter(e=>{if(R){let t=Q(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},e1=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let o,i;let{placement:l,rects:a,platform:u,elements:c}=e,{apply:s=()=>{},...d}=q(n,e),f=await el(e,d),p=Z(l),v=$(l),h="y"===Q(l),{width:m,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,i=v===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===v?"top":"bottom");let y=g-f.top-f.bottom,w=m-f.left-f.right,b=B(g-f[o],y),x=B(m-f[i],w),E=!e.middlewareData.shift,S=b,C=x;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(C=w),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(S=y),E&&!v){let e=_(f.left,0),t=_(f.right,0),n=_(f.top,0),r=_(f.bottom,0);h?C=m-2*(0!==e||0!==t?e+t:_(f.left,f.right)):S=g-2*(0!==n||0!==r?n+r:_(f.top,f.bottom))}await s({...e,availableWidth:C,availableHeight:S});let R=await u.getDimensions(c.floating);return m!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},e2=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...o}=q(n,e);switch(r){case"referenceHidden":{let n=ea(await el(e,{...o,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:eu(n)}}}case"escaped":{let n=ea(await el(e,{...o,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:eu(n)}}}default:return{}}}}),options:[e,t]}},e5=(e,t)=>({...e$(e),options:[e,t]});var e6=f.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,E.jsx)(b.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,E.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e6.displayName="Arrow";var e7=n(1188),e8=n(420),e4="Popper",[e9,e3]=(0,y.b)(e4),[te,tt]=e9(e4),tn=e=>{let{__scopePopper:t,children:n}=e,[r,o]=f.useState(null);return(0,E.jsx)(te,{scope:t,anchor:r,onAnchorChange:o,children:n})};tn.displayName=e4;var tr="PopperAnchor",to=f.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=tt(tr,n),l=f.useRef(null),a=(0,g.e)(t,l);return f.useEffect(()=>{i.onAnchorChange((null==r?void 0:r.current)||l.current)}),r?null:(0,E.jsx)(b.WV.div,{...o,ref:a})});to.displayName=tr;var ti="PopperContent",[tl,ta]=e9(ti),tu=f.forwardRef((e,t)=>{var n,r,o,i,l,a,u,c;let{__scopePopper:s,side:d="bottom",sideOffset:v=0,align:h="center",alignOffset:m=0,arrowPadding:y=0,avoidCollisions:w=!0,collisionBoundary:S=[],collisionPadding:C=0,sticky:R="partial",hideWhenDetached:T=!1,updatePositionStrategy:P="optimized",onPlaced:k,...L}=e,M=tt(ti,s),[A,j]=f.useState(null),D=(0,g.e)(t,e=>j(e)),[N,W]=f.useState(null),O=(0,e8.t)(N),I=null!==(u=null==O?void 0:O.width)&&void 0!==u?u:0,V=null!==(c=null==O?void 0:O.height)&&void 0!==c?c:0,F="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},H=Array.isArray(S)?S:[S],z=H.length>0,Y={padding:F,boundary:H.filter(tf),altBoundary:z},{refs:X,floatingStyles:U,placement:q,isPositioned:Z,middlewareData:$}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:a=!0,whileElementsMounted:u,open:c}=e,[s,d]=f.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,h]=f.useState(r);eX(v,r)||h(r);let[m,g]=f.useState(null),[y,w]=f.useState(null),b=f.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=f.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=i||m,S=l||y,C=f.useRef(null),R=f.useRef(null),T=f.useRef(s),P=null!=u,k=eZ(u),L=eZ(o),M=eZ(c),A=f.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:v};L.current&&(e.platform=L.current),eK(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==M.current};j.current&&!eX(T.current,t)&&(T.current=t,p.flushSync(()=>{d(t)}))})},[v,t,n,L,M]);eY(()=>{!1===c&&T.current.isPositioned&&(T.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let j=f.useRef(!1);eY(()=>(j.current=!0,()=>{j.current=!1}),[]),eY(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(k.current)return k.current(E,S,A);A()}},[E,S,A,k,P]);let D=f.useMemo(()=>({reference:C,floating:R,setReference:b,setFloating:x}),[b,x]),N=f.useMemo(()=>({reference:E,floating:S}),[E,S]),W=f.useMemo(()=>{let e={position:n,left:0,top:0};if(!N.floating)return e;let t=eq(N.floating,s.x),r=eq(N.floating,s.y);return a?{...e,transform:"translate("+t+"px, "+r+"px)",...eU(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,a,N.floating,s.x,s.y]);return f.useMemo(()=>({...s,update:A,refs:D,elements:N,floatingStyles:W}),[s,A,D,N,W])}({strategy:"fixed",placement:d+("center"!==h?"-"+h:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eL(e),d=i||l?[...s?eT(s):[],...eT(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=ep(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let v=K(d),h=K(o.clientWidth-(s+f)),m={rootMargin:-v+"px "+-h+"px "+-K(o.clientHeight-(d+p))+"px "+-K(s)+"px",threshold:_(0,B(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||e_(c,e.getBoundingClientRect())||l(),g=!1}try{r=new IntersectionObserver(y,{...m,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,m)}r.observe(e)}(!0),i}(s,n):null,p=-1,v=null;a&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&v&&(v.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),s&&!c&&v.observe(s),v.observe(t));let h=c?eD(e):null;return c&&function t(){let r=eD(e);h&&!e_(h,r)&&n(),h=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=v)||e.disconnect(),v=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===P})},elements:{reference:M.anchor},middleware:[eG({mainAxis:v+V,alignmentAxis:m}),w&&eJ({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?eQ():void 0,...Y}),w&&e0({...Y}),e1({...Y,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),N&&e5({element:N,padding:y}),tp({arrowWidth:I,arrowHeight:V}),T&&e2({strategy:"referenceHidden",...Y})]}),[G,J]=tv(q),Q=(0,x.W)(k);(0,e7.b)(()=>{Z&&(null==Q||Q())},[Z,Q]);let ee=null===(n=$.arrow)||void 0===n?void 0:n.x,et=null===(r=$.arrow)||void 0===r?void 0:r.y,en=(null===(o=$.arrow)||void 0===o?void 0:o.centerOffset)!==0,[er,eo]=f.useState();return(0,e7.b)(()=>{A&&eo(window.getComputedStyle(A).zIndex)},[A]),(0,E.jsx)("div",{ref:X.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:Z?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:er,"--radix-popper-transform-origin":[null===(i=$.transformOrigin)||void 0===i?void 0:i.x,null===(l=$.transformOrigin)||void 0===l?void 0:l.y].join(" "),...(null===(a=$.hide)||void 0===a?void 0:a.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,E.jsx)(tl,{scope:s,placedSide:G,onArrowChange:W,arrowX:ee,arrowY:et,shouldHideArrow:en,children:(0,E.jsx)(b.WV.div,{"data-side":G,"data-align":J,...L,ref:D,style:{...L.style,animation:Z?void 0:"none"}})})})});tu.displayName=ti;var tc="PopperArrow",ts={top:"bottom",right:"left",bottom:"top",left:"right"},td=f.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ta(tc,n),i=ts[o.placedSide];return(0,E.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,E.jsx)(e6,{...r,ref:t,style:{...r.style,display:"block"}})})});function tf(e){return null!==e}td.displayName=tc;var tp=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null===(n=c.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,v]=tv(a),h={start:"0%",center:"50%",end:"100%"}[v],m=(null!==(i=null===(r=c.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+d/2,g=(null!==(l=null===(o=c.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,y="",w="";return"bottom"===p?(y=s?h:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=s?h:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?h:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?h:"".concat(g,"px")),{data:{x:y,y:w}}}});function tv(e){let[t,n="center"]=e.split("-");return[t,n]}var th=f.forwardRef((e,t)=>{var n,r;let{container:o,...i}=e,[l,a]=f.useState(!1);(0,e7.b)(()=>a(!0),[]);let u=o||l&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return u?p.createPortal((0,E.jsx)(b.WV.div,{...i,ref:t}),u):null});th.displayName="Portal";var tm=n(7495),tg=n(886),ty=n(6718),tw=f.forwardRef((e,t)=>(0,E.jsx)(b.WV.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));tw.displayName="VisuallyHidden";var tb=new WeakMap,tx=new WeakMap,tE={},tS=0,tC=function(e){return e&&(e.host||tC(e.parentNode))},tR=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tC(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tE[n]||(tE[n]=new WeakMap);var i=tE[n],l=[],a=new Set,u=new Set(o),c=function(e){!e||a.has(e)||(a.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||u.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tb.get(e)||0)+1,c=(i.get(e)||0)+1;tb.set(e,u),i.set(e,c),l.push(e),1===u&&o&&tx.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),a.clear(),tS++,function(){l.forEach(function(e){var t=tb.get(e)-1,o=i.get(e)-1;tb.set(e,t),i.set(e,o),t||(tx.has(e)||e.removeAttribute(r),tx.delete(e)),o||e.removeAttribute(n)}),--tS||(tb=new WeakMap,tb=new WeakMap,tx=new WeakMap,tE={})}},tT=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),tR(r,o,n,"aria-hidden")):function(){return null}},tP=function(){return(tP=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tk(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}"function"==typeof SuppressedError&&SuppressedError;var tL="right-scroll-bar-position",tM="width-before-scroll-bar";function tA(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tj="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,tD=new WeakMap,tN=(void 0===o&&(o={}),(void 0===i&&(i=function(e){return e}),l=[],a=!1,u={read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return l.length?l[l.length-1]:null},useMedium:function(e){var t=i(e,a);return l.push(t),function(){l=l.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;l.length;){var t=l;l=[],t.forEach(e)}l={push:function(t){return e(t)},filter:function(){return l}}},assignMedium:function(e){a=!0;var t=[];if(l.length){var n=l;l=[],n.forEach(e),t=l}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),l={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),l}}}}).options=tP({async:!0,ssr:!1},o),u),tW=function(){},tO=f.forwardRef(function(e,t){var n,r,o,i,l=f.useRef(null),a=f.useState({onScrollCapture:tW,onWheelCapture:tW,onTouchMoveCapture:tW}),u=a[0],c=a[1],s=e.forwardProps,d=e.children,p=e.className,v=e.removeScrollBar,h=e.enabled,m=e.shards,g=e.sideCar,y=e.noIsolation,w=e.inert,b=e.allowPinchZoom,x=e.as,E=e.gapMode,S=tk(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=(n=[l,t],r=function(e){return n.forEach(function(t){return tA(t,e)})},(o=(0,f.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tj(function(){var e=tD.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||tA(e,null)}),r.forEach(function(e){t.has(e)||tA(e,o)})}tD.set(i,n)},[n]),i),R=tP(tP({},S),u);return f.createElement(f.Fragment,null,h&&f.createElement(g,{sideCar:tN,removeScrollBar:v,shards:m,noIsolation:y,inert:w,setCallbacks:c,allowPinchZoom:!!b,lockRef:l,gapMode:E}),s?f.cloneElement(f.Children.only(d),tP(tP({},R),{ref:C})):f.createElement(void 0===x?"div":x,tP({},R,{className:p,ref:C}),d))});tO.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tO.classNames={fullWidth:tM,zeroRight:tL};var tI=function(e){var t=e.sideCar,n=tk(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return f.createElement(r,tP({},n))};tI.isSideCarExport=!0;var tV=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=d||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tF=function(){var e=tV();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},tH=function(){var e=tF();return function(t){return e(t.styles,t.dynamic),null}},tB={left:0,top:0,right:0,gap:0},t_=function(e){return parseInt(e||"",10)||0},tz=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[t_(n),t_(r),t_(o)]},tK=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return tB;var t=tz(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},tY=tH(),tX="data-scroll-locked",tU=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(tX,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tL," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tM," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tL," .").concat(tL," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tM," .").concat(tM," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(tX,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},tq=function(){var e=parseInt(document.body.getAttribute(tX)||"0",10);return isFinite(e)?e:0},tZ=function(){f.useEffect(function(){return document.body.setAttribute(tX,(tq()+1).toString()),function(){var e=tq()-1;e<=0?document.body.removeAttribute(tX):document.body.setAttribute(tX,e.toString())}},[])},t$=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;tZ();var i=f.useMemo(function(){return tK(o)},[o]);return f.createElement(tY,{styles:tU(i,!t,o,n?"":"!important")})},tG=!1;if("undefined"!=typeof window)try{var tJ=Object.defineProperty({},"passive",{get:function(){return tG=!0,!0}});window.addEventListener("test",tJ,tJ),window.removeEventListener("test",tJ,tJ)}catch(e){tG=!1}var tQ=!!tG&&{passive:!1},t0=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},t1=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),t2(e,r)){var o=t5(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},t2=function(e,t){return"v"===e?t0(t,"overflowY"):t0(t,"overflowX")},t5=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},t6=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{var v=t5(e,u),h=v[0],m=v[1]-v[2]-l*h;(h||m)&&t2(e,u)&&(f+=m,p+=h),u instanceof ShadowRoot?u=u.host:u=u.parentNode}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},t7=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},t8=function(e){return[e.deltaX,e.deltaY]},t4=function(e){return e&&"current"in e?e.current:e},t9=0,t3=[],ne=(c=function(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),o=f.useState(t9++)[0],i=f.useState(tH)[0],l=f.useRef(e);f.useEffect(function(){l.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(t4),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=f.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=t7(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=t1(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=t1(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return t6(p,t,e,"h"===p?u:c,!0)},[]),u=f.useCallback(function(e){if(t3.length&&t3[t3.length-1]===i){var n="deltaY"in e?t8(e):t7(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(t4).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?a(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=f.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),s=f.useCallback(function(e){n.current=t7(e),r.current=void 0},[]),d=f.useCallback(function(t){c(t.type,t8(t),t.target,a(t,e.lockRef.current))},[]),p=f.useCallback(function(t){c(t.type,t7(t),t.target,a(t,e.lockRef.current))},[]);f.useEffect(function(){return t3.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",u,tQ),document.addEventListener("touchmove",u,tQ),document.addEventListener("touchstart",s,tQ),function(){t3=t3.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,tQ),document.removeEventListener("touchmove",u,tQ),document.removeEventListener("touchstart",s,tQ)}},[]);var v=e.removeScrollBar,h=e.inert;return f.createElement(f.Fragment,null,h?f.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?f.createElement(t$,{gapMode:e.gapMode}):null)},tN.useMedium(c),tI),nt=f.forwardRef(function(e,t){return f.createElement(tO,tP({},e,{ref:t,sideCar:ne}))});nt.classNames=tO.classNames;var nn=[" ","Enter","ArrowUp","ArrowDown"],nr=[" ","Enter"],no="Select",[ni,nl,na]=(0,m.B)(no),[nu,nc]=(0,y.b)(no,[na,e3]),ns=e3(),[nd,nf]=nu(no),[np,nv]=nu(no),nh=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:l,defaultValue:a,onValueChange:u,dir:c,name:s,autoComplete:d,disabled:p,required:v,form:h}=e,m=ns(t),[g,y]=f.useState(null),[b,x]=f.useState(null),[S,C]=f.useState(!1),R=(0,w.gm)(c),[T=!1,P]=(0,tg.T)({prop:r,defaultProp:o,onChange:i}),[k,L]=(0,tg.T)({prop:l,defaultProp:a,onChange:u}),M=f.useRef(null),A=!g||h||!!g.closest("form"),[j,D]=f.useState(new Set),N=Array.from(j).map(e=>e.props.value).join(";");return(0,E.jsx)(tn,{...m,children:(0,E.jsxs)(nd,{required:v,scope:t,trigger:g,onTriggerChange:y,valueNode:b,onValueNodeChange:x,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:(0,F.M)(),value:k,onValueChange:L,open:T,onOpenChange:P,dir:R,triggerPointerDownPosRef:M,disabled:p,children:[(0,E.jsx)(ni.Provider,{scope:t,children:(0,E.jsx)(np,{scope:e.__scopeSelect,onNativeOptionAdd:f.useCallback(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:f.useCallback(e=>{D(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),A?(0,E.jsxs)(nQ,{"aria-hidden":!0,required:v,tabIndex:-1,name:s,autoComplete:d,value:k,onChange:e=>L(e.target.value),disabled:p,form:h,children:[void 0===k?(0,E.jsx)("option",{value:""}):null,Array.from(j)]},N):null]})})};nh.displayName=no;var nm="SelectTrigger",ng=f.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,i=ns(n),l=nf(nm,n),a=l.disabled||r,u=(0,g.e)(t,l.onTriggerChange),c=nl(n),s=f.useRef("touch"),[d,p,v]=n0(e=>{let t=c().filter(e=>!e.disabled),n=t.find(e=>e.value===l.value),r=n1(t,e,n);void 0!==r&&l.onValueChange(r.value)}),m=e=>{a||(l.onOpenChange(!0),v()),e&&(l.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,E.jsx)(to,{asChild:!0,...i,children:(0,E.jsx)(b.WV.button,{type:"button",role:"combobox","aria-controls":l.contentId,"aria-expanded":l.open,"aria-required":l.required,"aria-autocomplete":"none",dir:l.dir,"data-state":l.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":nJ(l.value)?"":void 0,...o,ref:u,onClick:(0,h.M)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==s.current&&m(e)}),onPointerDown:(0,h.M)(o.onPointerDown,e=>{s.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(m(e),e.preventDefault())}),onKeyDown:(0,h.M)(o.onKeyDown,e=>{let t=""!==d.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||p(e.key),(!t||" "!==e.key)&&nn.includes(e.key)&&(m(),e.preventDefault())})})})});ng.displayName=nm;var ny="SelectValue",nw=f.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=nf(ny,n),{onValueNodeHasChildrenChange:c}=u,s=void 0!==i,d=(0,g.e)(t,u.onValueNodeChange);return(0,e7.b)(()=>{c(s)},[c,s]),(0,E.jsx)(b.WV.span,{...a,ref:d,style:{pointerEvents:"none"},children:nJ(u.value)?(0,E.jsx)(E.Fragment,{children:l}):i})});nw.displayName=ny;var nb=f.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,E.jsx)(b.WV.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nb.displayName="SelectIcon";var nx=e=>(0,E.jsx)(th,{asChild:!0,...e});nx.displayName="SelectPortal";var nE="SelectContent",nS=f.forwardRef((e,t)=>{let n=nf(nE,e.__scopeSelect),[r,o]=f.useState();return((0,e7.b)(()=>{o(new DocumentFragment)},[]),n.open)?(0,E.jsx)(nT,{...e,ref:t}):r?p.createPortal((0,E.jsx)(nC,{scope:e.__scopeSelect,children:(0,E.jsx)(ni.Slot,{scope:e.__scopeSelect,children:(0,E.jsx)("div",{children:e.children})})}),r):null});nS.displayName=nE;var[nC,nR]=nu(nE),nT=f.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:l,side:a,sideOffset:u,align:c,alignOffset:s,arrowPadding:d,collisionBoundary:p,collisionPadding:v,sticky:m,hideWhenDetached:y,avoidCollisions:w,...b}=e,x=nf(nE,n),[S,C]=f.useState(null),[T,P]=f.useState(null),M=(0,g.e)(t,e=>C(e)),[A,j]=f.useState(null),[N,W]=f.useState(null),O=nl(n),[I,V]=f.useState(!1),F=f.useRef(!1);f.useEffect(()=>{if(S)return tT(S)},[S]),f.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:L()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:L()),k++,()=>{1===k&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),k--}},[]);let H=f.useCallback(e=>{let[t,...n]=O().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&T&&(T.scrollTop=0),n===r&&T&&(T.scrollTop=T.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[O,T]),B=f.useCallback(()=>H([A,S]),[H,A,S]);f.useEffect(()=>{I&&B()},[I,B]);let{onOpenChange:_,triggerPointerDownPosRef:z}=x;f.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(n=z.current)||void 0===n?void 0:n.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(i=null===(r=z.current)||void 0===r?void 0:r.y)&&void 0!==i?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||_(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[S,_,z]),f.useEffect(()=>{let e=()=>_(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[_]);let[K,Y]=n0(e=>{let t=O().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=n1(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),X=f.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==x.value&&x.value===t||r)&&(j(e),r&&(F.current=!0))},[x.value]),U=f.useCallback(()=>null==S?void 0:S.focus(),[S]),q=f.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==x.value&&x.value===t||r)&&W(e)},[x.value]),Z="popper"===r?nk:nP,$=Z===nk?{side:a,sideOffset:u,align:c,alignOffset:s,arrowPadding:d,collisionBoundary:p,collisionPadding:v,sticky:m,hideWhenDetached:y,avoidCollisions:w}:{};return(0,E.jsx)(nC,{scope:n,content:S,viewport:T,onViewportChange:P,itemRefCallback:X,selectedItem:A,onItemLeave:U,itemTextRefCallback:q,focusSelectedItem:B,selectedItemText:N,position:r,isPositioned:I,searchRef:K,children:(0,E.jsx)(nt,{as:tm.g7,allowPinchZoom:!0,children:(0,E.jsx)(D,{asChild:!0,trapped:x.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,h.M)(o,e=>{var t;null===(t=x.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,E.jsx)(R,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>x.onOpenChange(!1),children:(0,E.jsx)(Z,{role:"listbox",id:x.contentId,"data-state":x.open?"open":"closed",dir:x.dir,onContextMenu:e=>e.preventDefault(),...b,...$,onPlaced:()=>V(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:(0,h.M)(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>H(t)),e.preventDefault()}})})})})})})});nT.displayName="SelectContentImpl";var nP=f.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=nf(nE,n),l=nR(nE,n),[a,u]=f.useState(null),[c,s]=f.useState(null),d=(0,g.e)(t,e=>s(e)),p=nl(n),h=f.useRef(!1),m=f.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:x,focusSelectedItem:S}=l,C=f.useCallback(()=>{if(i.trigger&&i.valueNode&&a&&c&&y&&w&&x){let e=i.trigger.getBoundingClientRect(),t=c.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),o=x.getBoundingClientRect();if("rtl"!==i.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,u=e.width+l,c=Math.max(u,t.width),s=v(i,[10,Math.max(10,window.innerWidth-10-c)]);a.style.minWidth=u+"px",a.style.left=s+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,u=e.width+l,c=Math.max(u,t.width),s=v(i,[10,Math.max(10,window.innerWidth-10-c)]);a.style.minWidth=u+"px",a.style.right=s+"px"}let l=p(),u=window.innerHeight-20,s=y.scrollHeight,d=window.getComputedStyle(c),f=parseInt(d.borderTopWidth,10),m=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),b=f+m+s+parseInt(d.paddingBottom,10)+g,E=Math.min(5*w.offsetHeight,b),S=window.getComputedStyle(y),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),T=e.top+e.height/2-10,P=w.offsetHeight/2,k=f+m+(w.offsetTop+P);if(k<=T){let e=l.length>0&&w===l[l.length-1].ref.current;a.style.bottom="0px";let t=c.clientHeight-y.offsetTop-y.offsetHeight;a.style.height=k+Math.max(u-T,P+(e?R:0)+t+g)+"px"}else{let e=l.length>0&&w===l[0].ref.current;a.style.top="0px";let t=Math.max(T,f+y.offsetTop+(e?C:0)+P);a.style.height=t+(b-k)+"px",y.scrollTop=k-T+y.offsetTop}a.style.margin="".concat(10,"px 0"),a.style.minHeight=E+"px",a.style.maxHeight=u+"px",null==r||r(),requestAnimationFrame(()=>h.current=!0)}},[p,i.trigger,i.valueNode,a,c,y,w,x,i.dir,r]);(0,e7.b)(()=>C(),[C]);let[R,T]=f.useState();(0,e7.b)(()=>{c&&T(window.getComputedStyle(c).zIndex)},[c]);let P=f.useCallback(e=>{e&&!0===m.current&&(C(),null==S||S(),m.current=!1)},[C,S]);return(0,E.jsx)(nL,{scope:n,contentWrapper:a,shouldExpandOnScrollRef:h,onScrollButtonChange:P,children:(0,E.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,E.jsx)(b.WV.div,{...o,ref:d,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});nP.displayName="SelectItemAlignedPosition";var nk=f.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=ns(n);return(0,E.jsx)(tu,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nk.displayName="SelectPopperPosition";var[nL,nM]=nu(nE,{}),nA="SelectViewport",nj=f.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,i=nR(nA,n),l=nM(nA,n),a=(0,g.e)(t,i.onViewportChange),u=f.useRef(0);return(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,E.jsx)(ni.Slot,{scope:n,children:(0,E.jsx)(b.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:a,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,h.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=l;if((null==r?void 0:r.current)&&n){let e=Math.abs(u.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});nj.displayName=nA;var nD="SelectGroup",[nN,nW]=nu(nD),nO=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,F.M)();return(0,E.jsx)(nN,{scope:n,id:o,children:(0,E.jsx)(b.WV.div,{role:"group","aria-labelledby":o,...r,ref:t})})});nO.displayName=nD;var nI="SelectLabel",nV=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nW(nI,n);return(0,E.jsx)(b.WV.div,{id:o.id,...r,ref:t})});nV.displayName=nI;var nF="SelectItem",[nH,nB]=nu(nF),n_=f.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...l}=e,a=nf(nF,n),u=nR(nF,n),c=a.value===r,[s,d]=f.useState(null!=i?i:""),[p,v]=f.useState(!1),m=(0,g.e)(t,e=>{var t;return null===(t=u.itemRefCallback)||void 0===t?void 0:t.call(u,e,r,o)}),y=(0,F.M)(),w=f.useRef("touch"),x=()=>{o||(a.onValueChange(r),a.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,E.jsx)(nH,{scope:n,value:r,disabled:o,textId:y,isSelected:c,onItemTextChange:f.useCallback(e=>{d(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,E.jsx)(ni.ItemSlot,{scope:n,value:r,disabled:o,textValue:s,children:(0,E.jsx)(b.WV.div,{role:"option","aria-labelledby":y,"data-highlighted":p?"":void 0,"aria-selected":c&&p,"data-state":c?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...l,ref:m,onFocus:(0,h.M)(l.onFocus,()=>v(!0)),onBlur:(0,h.M)(l.onBlur,()=>v(!1)),onClick:(0,h.M)(l.onClick,()=>{"mouse"!==w.current&&x()}),onPointerUp:(0,h.M)(l.onPointerUp,()=>{"mouse"===w.current&&x()}),onPointerDown:(0,h.M)(l.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:(0,h.M)(l.onPointerMove,e=>{if(w.current=e.pointerType,o){var t;null===(t=u.onItemLeave)||void 0===t||t.call(u)}else"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,h.M)(l.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=u.onItemLeave)||void 0===t||t.call(u)}}),onKeyDown:(0,h.M)(l.onKeyDown,e=>{var t;(null===(t=u.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(nr.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});n_.displayName=nF;var nz="SelectItemText",nK=f.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...i}=e,l=nf(nz,n),a=nR(nz,n),u=nB(nz,n),c=nv(nz,n),[s,d]=f.useState(null),v=(0,g.e)(t,e=>d(e),u.onItemTextChange,e=>{var t;return null===(t=a.itemTextRefCallback)||void 0===t?void 0:t.call(a,e,u.value,u.disabled)}),h=null==s?void 0:s.textContent,m=f.useMemo(()=>(0,E.jsx)("option",{value:u.value,disabled:u.disabled,children:h},u.value),[u.disabled,u.value,h]),{onNativeOptionAdd:y,onNativeOptionRemove:w}=c;return(0,e7.b)(()=>(y(m),()=>w(m)),[y,w,m]),(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(b.WV.span,{id:u.textId,...i,ref:v}),u.isSelected&&l.valueNode&&!l.valueNodeHasChildren?p.createPortal(i.children,l.valueNode):null]})});nK.displayName=nz;var nY="SelectItemIndicator",nX=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return nB(nY,n).isSelected?(0,E.jsx)(b.WV.span,{"aria-hidden":!0,...r,ref:t}):null});nX.displayName=nY;var nU="SelectScrollUpButton";f.forwardRef((e,t)=>{let n=nR(nU,e.__scopeSelect),r=nM(nU,e.__scopeSelect),[o,i]=f.useState(!1),l=(0,g.e)(t,r.onScrollButtonChange);return(0,e7.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,E.jsx)(nZ,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}).displayName=nU;var nq="SelectScrollDownButton";f.forwardRef((e,t)=>{let n=nR(nq,e.__scopeSelect),r=nM(nq,e.__scopeSelect),[o,i]=f.useState(!1),l=(0,g.e)(t,r.onScrollButtonChange);return(0,e7.b)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,E.jsx)(nZ,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}).displayName=nq;var nZ=f.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,i=nR("SelectScrollButton",n),l=f.useRef(null),a=nl(n),u=f.useCallback(()=>{null!==l.current&&(window.clearInterval(l.current),l.current=null)},[]);return f.useEffect(()=>()=>u(),[u]),(0,e7.b)(()=>{var e;let t=a().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[a]),(0,E.jsx)(b.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,h.M)(o.onPointerDown,()=>{null===l.current&&(l.current=window.setInterval(r,50))}),onPointerMove:(0,h.M)(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===l.current&&(l.current=window.setInterval(r,50))}),onPointerLeave:(0,h.M)(o.onPointerLeave,()=>{u()})})}),n$=f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,E.jsx)(b.WV.div,{"aria-hidden":!0,...r,ref:t})});n$.displayName="SelectSeparator";var nG="SelectArrow";function nJ(e){return""===e||void 0===e}f.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ns(n),i=nf(nG,n),l=nR(nG,n);return i.open&&"popper"===l.position?(0,E.jsx)(td,{...o,...r,ref:t}):null}).displayName=nG;var nQ=f.forwardRef((e,t)=>{let{value:n,...r}=e,o=f.useRef(null),i=(0,g.e)(t,o),l=(0,ty.D)(n);return f.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(l!==n&&t){let r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[l,n]),(0,E.jsx)(tw,{asChild:!0,children:(0,E.jsx)("select",{...r,ref:i,defaultValue:n})})});function n0(e){let t=(0,x.W)(e),n=f.useRef(""),r=f.useRef(0),o=f.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),i=f.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return f.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function n1(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}nQ.displayName="BubbleSelect";var n2=nh,n5=ng,n6=nw,n7=nb,n8=nx,n4=nS,n9=nj,n3=nO,re=nV,rt=n_,rn=nK,rr=nX,ro=n$},271:function(e,t,n){n.d(t,{VY:function(){return A},aV:function(){return L},fC:function(){return k},xz:function(){return M}});var r=n(2265),o=n(6741),i=n(3966),l=n(1353),a=n(1599),u=n(6840),c=n(9114),s=n(886),d=n(9255),f=n(7437),p="Tabs",[v,h]=(0,i.b)(p,[l.Pc]),m=(0,l.Pc)(),[g,y]=v(p),w=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:l="horizontal",dir:a,activationMode:p="automatic",...v}=e,h=(0,c.gm)(a),[m,y]=(0,s.T)({prop:r,onChange:o,defaultProp:i});return(0,f.jsx)(g,{scope:n,baseId:(0,d.M)(),value:m,onValueChange:y,orientation:l,dir:h,activationMode:p,children:(0,f.jsx)(u.WV.div,{dir:h,"data-orientation":l,...v,ref:t})})});w.displayName=p;var b="TabsList",x=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,i=y(b,n),a=m(n);return(0,f.jsx)(l.fC,{asChild:!0,...a,orientation:i.orientation,dir:i.dir,loop:r,children:(0,f.jsx)(u.WV.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});x.displayName=b;var E="TabsTrigger",S=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:i=!1,...a}=e,c=y(E,n),s=m(n),d=T(c.baseId,r),p=P(c.baseId,r),v=r===c.value;return(0,f.jsx)(l.ck,{asChild:!0,...s,focusable:!i,active:v,children:(0,f.jsx)(u.WV.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":p,"data-state":v?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:d,...a,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,o.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;v||i||!e||c.onValueChange(r)})})})});S.displayName=E;var C="TabsContent",R=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:i,children:l,...c}=e,s=y(C,n),d=T(s.baseId,o),p=P(s.baseId,o),v=o===s.value,h=r.useRef(v);return r.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(a.z,{present:i||v,children:n=>{let{present:r}=n;return(0,f.jsx)(u.WV.div,{"data-state":v?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:p,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&l})}})});function T(e,t){return"".concat(e,"-trigger-").concat(t)}function P(e,t){return"".concat(e,"-content-").concat(t)}R.displayName=C;var k=w,L=x,M=S,A=R},6718:function(e,t,n){n.d(t,{D:function(){return o}});var r=n(2265);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},420:function(e,t,n){n.d(t,{t:function(){return i}});var r=n(2265),o=n(1188);function i(e){let[t,n]=r.useState(void 0);return(0,o.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}}]);