(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[28],{867:function(t){var e="function"==typeof Float32Array;function i(t,e,i){return(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t}function n(t,e,i){return 3*(1-3*i+3*e)*t*t+2*(3*i-6*e)*t+3*e}function s(t){return t}t.exports=function(t,r,a,l){if(!(0<=t&&t<=1&&0<=a&&a<=1))throw Error("bezier x values must be in [0, 1] range");if(t===r&&a===l)return s;for(var o=e?new Float32Array(11):Array(11),u=0;u<11;++u)o[u]=i(.1*u,t,a);return function(e){return 0===e?0:1===e?1:i(function(e){for(var s=0,r=1;10!==r&&o[r]<=e;++r)s+=.1;var l=s+(e-o[--r])/(o[r+1]-o[r])*.1,u=n(l,t,a);return u>=.001?function(t,e,s,r){for(var a=0;a<4;++a){var l=n(e,s,r);if(0===l)break;var o=i(e,s,r)-t;e-=o/l}return e}(e,l,t,a):0===u?l:function(t,e,n,s,r){var a,l,o=0;do(a=i(l=e+(n-e)/2,s,r)-t)>0?n=l:e=l;while(Math.abs(a)>1e-7&&++o<10);return l}(e,s,s+.1,t,a)}(e),r,l)}}},1671:function(t,e,i){"use strict";i.d(e,{Z:function(){return n}});let n=(0,i(9205).Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8997:function(t,e,i){"use strict";i.d(e,{Z:function(){return n}});let n=(0,i(9205).Z)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2718:function(t,e,i){"use strict";i.d(e,{Z:function(){return n}});let n=(0,i(9205).Z)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},8906:function(t,e,i){"use strict";i.d(e,{Z:function(){return n}});let n=(0,i(9205).Z)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5805:function(t,e,i){"use strict";i.d(e,{Z:function(){return n}});let n=(0,i(9205).Z)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},3755:function(t,e,i){"use strict";i.d(e,{VS:function(){return F},UG:function(){return $},Xu:function(){return tt},YT:function(){return D}});var n,s,r,a,l,o,u,h,c,d,f,p=i(867),v=i.n(p),g=function(t){this.startX=t.startX,this.startY=t.startY,this.endX=t.endX,this.endY=t.endY,this.totalX=this.endX-this.startX,this.totalY=this.endY-this.startY,this.startMultiplierX=t.startMultiplierX||1,this.endMultiplierX=t.endMultiplierX||1,this.startMultiplierY=t.startMultiplierY||1,this.endMultiplierY=t.endMultiplierY||1};function m(){return(m=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}).apply(this,arguments)}(n=o||(o={})).speed="speed",n.translateX="translateX",n.translateY="translateY",n.rotate="rotate",n.rotateX="rotateX",n.rotateY="rotateY",n.rotateZ="rotateZ",n.scale="scale",n.scaleX="scaleX",n.scaleY="scaleY",n.scaleZ="scaleZ",n.opacity="opacity",(s=u||(u={})).px="px",s["%"]="%",s.vh="vh",s.vw="vw",(r=h||(h={})).deg="deg",r.turn="turn",r.rad="rad",(c||(c={}))[""]="",(a=d||(d={})).vertical="vertical",a.horizontal="horizontal",(l=f||(f={})).ease="ease",l.easeIn="easeIn",l.easeOut="easeOut",l.easeInOut="easeInOut",l.easeInQuad="easeInQuad",l.easeInCubic="easeInCubic",l.easeInQuart="easeInQuart",l.easeInQuint="easeInQuint",l.easeInSine="easeInSine",l.easeInExpo="easeInExpo",l.easeInCirc="easeInCirc",l.easeOutQuad="easeOutQuad",l.easeOutCubic="easeOutCubic",l.easeOutQuart="easeOutQuart",l.easeOutQuint="easeOutQuint",l.easeOutSine="easeOutSine",l.easeOutExpo="easeOutExpo",l.easeOutCirc="easeOutCirc",l.easeInOutQuad="easeInOutQuad",l.easeInOutCubic="easeInOutCubic",l.easeInOutQuart="easeInOutQuart",l.easeInOutQuint="easeInOutQuint",l.easeInOutSine="easeInOutSine",l.easeInOutExpo="easeInOutExpo",l.easeInOutCirc="easeInOutCirc",l.easeInBack="easeInBack",l.easeOutBack="easeOutBack",l.easeInOutBack="easeInOutBack";var w=0,b=function(){function t(t){var e=t.el.getBoundingClientRect();if(t.view.scrollContainer){var i=t.view.scrollContainer.getBoundingClientRect();e=m({},e,{top:e.top-i.top,right:e.right-i.left,bottom:e.bottom-i.top,left:e.left-i.left})}this.height=t.el.offsetHeight,this.width=t.el.offsetWidth,this.left=e.left,this.right=e.right,this.top=e.top,this.bottom=e.bottom,t.rootMargin&&this._setRectWithRootMargin(t.rootMargin)}return t.prototype._setRectWithRootMargin=function(t){var e=t.top+t.bottom,i=t.left+t.right;this.top-=t.top,this.right+=t.right,this.bottom+=t.bottom,this.left-=t.left,this.height+=e,this.width+=i},t}(),E=[c[""],u.px,u["%"],u.vh,u.vw,h.deg,h.turn,h.rad];function y(t,e){void 0===e&&(e=u["%"]);var i={value:0,unit:e};if(void 0===t)return i;if(!("number"==typeof t||"string"==typeof t))throw Error("Invalid value provided. Must provide a value as a string or number");if(t=String(t),i.value=parseFloat(t),i.unit=t.match(/[\d.\-+]*\s*(.*)/)[1]||e,!E.includes(i.unit))throw Error("Invalid unit provided.");return i}var C={ease:[.25,.1,.25,1],easeIn:[.42,0,1,1],easeOut:[0,0,.58,1],easeInOut:[.42,0,.58,1],easeInQuad:[.55,.085,.68,.53],easeInCubic:[.55,.055,.675,.19],easeInQuart:[.895,.03,.685,.22],easeInQuint:[.755,.05,.855,.06],easeInSine:[.47,0,.745,.715],easeInExpo:[.95,.05,.795,.035],easeInCirc:[.6,.04,.98,.335],easeOutQuad:[.25,.46,.45,.94],easeOutCubic:[.215,.61,.355,1],easeOutQuart:[.165,.84,.44,1],easeOutQuint:[.23,1,.32,1],easeOutSine:[.39,.575,.565,1],easeOutExpo:[.19,1,.22,1],easeOutCirc:[.075,.82,.165,1],easeInOutQuad:[.455,.03,.515,.955],easeInOutCubic:[.645,.045,.355,1],easeInOutQuart:[.77,0,.175,1],easeInOutQuint:[.86,0,.07,1],easeInOutSine:[.445,.05,.55,.95],easeInOutExpo:[1,0,0,1],easeInOutCirc:[.785,.135,.15,.86],easeInBack:[.6,-.28,.735,.045],easeOutBack:[.175,.885,.32,1.275],easeInOutBack:[.68,-.55,.265,1.55]};function x(t){if(Array.isArray(t))return v()(t[0],t[1],t[2],t[3]);if("string"==typeof t&&void 0!==C[t]){var e=C[t];return v()(e[0],e[1],e[2],e[3])}}var _=Object.values(o),I={speed:"px",translateX:"%",translateY:"%",rotate:"deg",rotateX:"deg",rotateY:"deg",rotateZ:"deg",scale:"",scaleX:"",scaleY:"",scaleZ:"",opacity:""};function O(t,e){var i={};return _.forEach(function(n){var s=I[n];if("number"==typeof(null==t?void 0:t[n])){var r=null==t?void 0:t[n],a=y(10*(r||0)+"px"),l=y(-10*(r||0)+"px"),o={start:a.value,end:l.value,unit:a.unit};e===d.vertical&&(i.translateY=o),e===d.horizontal&&(i.translateX=o)}if(Array.isArray(null==t?void 0:t[n])){var u=null==t?void 0:t[n];if(void 0!==u[0]&&void 0!==u[1]){var h=y(null==u?void 0:u[0],s),c=y(null==u?void 0:u[1],s),f=x(null==u?void 0:u[2]);if(i[n]={start:h.value,end:c.value,unit:h.unit,easing:f},h.unit!==c.unit)throw Error("Must provide matching units for the min and max offset values of each axis.")}}}),i}function S(t,e,i,n){var s=(i-t)/e;return n&&(s=n(s)),s}function P(t,e){var i,n;return{value:(i="function"==typeof t.easing?t.easing(e):e,n=(null==t?void 0:t.start)||0,(((null==t?void 0:t.end)||0)-n)*(i-0)/1+n),unit:null==t?void 0:t.unit}}var M=Object.values(o).filter(function(t){return"opacity"!==t});function Y(t){var e=t.el;e&&(e.style.transform="",e.style.opacity="")}function A(t,e,i){return Math.max(i/(i+(Math.abs(t)+Math.abs(e))*(e>t?-1:1)),1)}function X(t,e){var i=t.start,n=t.end,s=t.unit;if("%"===s){var r=e/100;i*=r,n*=r}if("vw"===s){var a=i/100,l=n/100;i=window.innerWidth*a,n=window.innerWidth*l}if("vh"===s){var o=i/100,u=n/100;i=window.innerHeight*o,n=window.innerHeight*u}return{start:i,end:n}}var k={start:0,end:0,unit:""},z=function(t,e,i){return Math.min(Math.max(t,e),i)},V=function(){function t(t){var e,i;this.el=t.el,this.props=t.props,this.scrollAxis=t.scrollAxis,this.disabledParallaxController=t.disabledParallaxController||!1,this.id=++w,this.effects=O(this.props,this.scrollAxis),this.isInView=null,this.progress=0,this._setElementEasing(t.props.easing),e=t.el,i=Object.keys(this.effects).includes("opacity"),e.style.willChange="transform"+(i?",opacity":"")}var e=t.prototype;return e.updateProps=function(t){return this.props=m({},this.props,t),this.effects=O(t,this.scrollAxis),this._setElementEasing(t.easing),this},e.setCachedAttributes=function(t,e){Y(this),this.rect=new b({el:this.props.targetElement||this.el,rootMargin:this.props.rootMargin,view:t});var i,n,s,r,a,l,o,u,h,c,f,p,v=(i=this.props,n=this.effects,s=this.scrollAxis,!i.rootMargin&&!i.targetElement&&!i.shouldDisableScalingTranslations&&(!!n.translateX&&s===d.horizontal||!!n.translateY&&s===d.vertical));return"number"==typeof this.props.startScroll&&"number"==typeof this.props.endScroll?this.limits=new g({startX:this.props.startScroll,startY:this.props.startScroll,endX:this.props.endScroll,endY:this.props.endScroll}):v?(this.limits=function(t,e,i,n,s,r){var a=i.translateX||k,l=i.translateY||k,o=X(a,t.width),u=o.start,h=o.end,c=X(l,t.height),f=c.start,p=c.end,v=t.top-e.height,m=t.left-e.width,w=t.bottom,b=t.right,E=1,y=1;s===d.vertical&&(y=E=A(f,p,e.height+t.height));var C=1,x=1;if(s===d.horizontal&&(x=C=A(u,h,e.width+t.width)),f<0&&(v+=f*E),p>0&&(w+=p*y),u<0&&(m+=u*C),h>0&&(b+=h*x),m+=n.x,b+=n.x,v+=n.y,w+=n.y,r){var _=n.y+t.top<e.height,I=n.x+t.left<e.width,O=n.y+t.bottom>e.scrollHeight-e.height,S=n.x+t.right>e.scrollWidth-e.height;_&&O&&(E=1,y=1,v=0,w=e.scrollHeight-e.height),I&&S&&(C=1,x=1,m=0,b=e.scrollWidth-e.width),!_&&O&&(v=t.top-e.height+n.y,E=A(f,p,(w=e.scrollHeight-e.height)-v),y=1,f<0&&(v+=f*E)),!I&&S&&(m=t.left-e.width+n.x,C=A(u,h,(b=e.scrollWidth-e.width)-m),x=1,u<0&&(m+=u*C)),_&&!O&&(v=0,E=1,y=A(f,p,(w=t.bottom+n.y)-v),p>0&&(w+=p*y)),I&&!S&&(m=0,C=1,x=A(u,h,(b=t.right+n.x)-m),h>0&&(b+=h*x))}return new g({startX:m,startY:v,endX:b,endY:w,startMultiplierX:C,endMultiplierX:x,startMultiplierY:E,endMultiplierY:y})}(this.rect,t,this.effects,e,this.scrollAxis,this.props.shouldAlwaysCompleteAnimation),this.scaledEffects=(r=this.effects,a=this.limits,(l=m({},r)).translateX&&(l.translateX=m({},r.translateX,{start:l.translateX.start*a.startMultiplierX,end:l.translateX.end*a.endMultiplierX})),l.translateY&&(l.translateY=m({},r.translateY,{start:l.translateY.start*a.startMultiplierY,end:l.translateY.end*a.endMultiplierY})),l)):this.limits=(o=this.rect,u=this.props.shouldAlwaysCompleteAnimation,h=o.top-t.height,c=o.left-t.width,f=o.bottom,p=o.right,c+=e.x,p+=e.x,h+=e.y,f+=e.y,u&&(e.y+o.top<t.height&&(h=0),e.x+o.left<t.width&&(c=0),f>t.scrollHeight-t.height&&(f=t.scrollHeight-t.height),p>t.scrollWidth-t.width&&(p=t.scrollWidth-t.width)),new g({startX:c,startY:h,endX:p,endY:f})),this._setElementStyles(),this},e._updateElementIsInView=function(t){var e=null===this.isInView;t!==this.isInView&&(t?this.props.onEnter&&this.props.onEnter(this):!e&&(this._setFinalProgress(),this._setElementStyles(),this.props.onExit&&this.props.onExit(this))),this.isInView=t},e._setFinalProgress=function(){var t=z(Math.round(this.progress),0,1);this._updateElementProgress(t)},e._setElementStyles=function(){this.props.disabled||this.disabledParallaxController||function(t,e,i){if(i){var n,s=M.reduce(function(i,n){var s=t[n]&&P(t[n],e);return void 0===s||void 0===s.value||void 0===s.unit?i:i+(n+"("+s.value)+s.unit+")"},""),r=void 0===(n=t.opacity&&P(t.opacity,e))||void 0===n.value||void 0===n.unit?"":""+n.value;i.style.transform=s,i.style.opacity=r}}(this.scaledEffects||this.effects,this.progress,this.el)},e._updateElementProgress=function(t){this.progress=t,this.props.onProgressChange&&this.props.onProgressChange(this.progress),this.props.onChange&&this.props.onChange(this)},e._setElementEasing=function(t){this.easing=x(t)},e.updateElementOptions=function(t){this.scrollAxis=t.scrollAxis,this.disabledParallaxController=t.disabledParallaxController||!1},e.updatePosition=function(t){if(!this.limits)return this;var e=this.scrollAxis===d.vertical,i=null===this.isInView,n=e?this.limits.startY:this.limits.startX,s=e?this.limits.endY:this.limits.endX,r=e?this.limits.totalY:this.limits.totalX,a=e?t.y:t.x,l=a>=n&&a<=s;if(this._updateElementIsInView(l),l){var o=S(n,r,a,this.easing);this._updateElementProgress(o),this._setElementStyles()}else i&&(this.progress=z(Math.round(S(n,r,a,this.easing)),0,1),this._setElementStyles());return this},t}(),H=function(){function t(t){this.scrollContainer=t.scrollContainer,this.width=t.width,this.height=t.height,this.scrollHeight=t.scrollHeight,this.scrollWidth=t.scrollWidth}var e=t.prototype;return e.hasChanged=function(t){return t.width!==this.width||t.height!==this.height||t.scrollWidth!==this.scrollWidth||t.scrollHeight!==this.scrollHeight},e.setSize=function(t){return this.width=t.width,this.height=t.height,this.scrollHeight=t.scrollHeight,this.scrollWidth=t.scrollWidth,this},t}(),L=function(){function t(t,e){this.x=t,this.y=e,this.dx=0,this.dy=0}return t.prototype.setScroll=function(t,e){return this.dx=t-this.x,this.dy=e-this.y,this.x=t,this.y=e,this},t}(),W=function(){function t(t){var e=t.scrollAxis,i=void 0===e?d.vertical:e,n=t.scrollContainer,s=t.disabled;this.disabled=void 0!==s&&s,this.scrollAxis=i,this.elements=[],this._hasScrollContainer=!!n,this.viewEl=null!=n?n:window;var r=this._getScrollPosition(),a=r[0],l=r[1];this.scroll=new L(a,l),this.view=new H({width:0,height:0,scrollWidth:0,scrollHeight:0,scrollContainer:this._hasScrollContainer?n:void 0}),this._ticking=!1,this._supportsPassive=function(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){return t=!0,!0}});window.addEventListener("test",null,e),window.removeEventListener("test",null,e)}catch(t){}return t}(),this._bindAllMethods(),this.disabled||(this._addListeners(this.viewEl),this._addResizeObserver(),this._setViewSize())}t.init=function(e){if(!("undefined"!=typeof window))throw Error("Looks like ParallaxController.init() was called on the server. This method must be called on the client.");return new t(e)};var e=t.prototype;return e._bindAllMethods=function(){var t=this;["_addListeners","_removeListeners","_getScrollPosition","_handleScroll","_handleUpdateCache","_updateAllElements","_updateElementPosition","_setViewSize","_addResizeObserver","_checkIfViewHasChanged","_getViewParams","getElements","createElement","removeElementById","resetElementStyles","updateElementPropsById","update","updateScrollContainer","destroy"].forEach(function(e){t[e]=t[e].bind(t)})},e._addListeners=function(t){t.addEventListener("scroll",this._handleScroll,!!this._supportsPassive&&{passive:!0}),window.addEventListener("resize",this._handleUpdateCache,!1),window.addEventListener("blur",this._handleUpdateCache,!1),window.addEventListener("focus",this._handleUpdateCache,!1),window.addEventListener("load",this._handleUpdateCache,!1)},e._removeListeners=function(t){var e;t.removeEventListener("scroll",this._handleScroll,!1),window.removeEventListener("resize",this._handleUpdateCache,!1),window.removeEventListener("blur",this._handleUpdateCache,!1),window.removeEventListener("focus",this._handleUpdateCache,!1),window.removeEventListener("load",this._handleUpdateCache,!1),null==(e=this._resizeObserver)||e.disconnect()},e._addResizeObserver=function(){var t=this;try{var e=this._hasScrollContainer?this.viewEl:document.documentElement;this._resizeObserver=new ResizeObserver(function(){return t.update()}),this._resizeObserver.observe(e)}catch(t){console.warn("Failed to create the resize observer in the ParallaxContoller")}},e._getScrollPosition=function(){return[this._hasScrollContainer?this.viewEl.scrollLeft:window.pageXOffset,this._hasScrollContainer?this.viewEl.scrollTop:window.pageYOffset]},e._handleScroll=function(){var t,e=this._getScrollPosition(),i=e[0],n=e[1];this.scroll.setScroll(i,n),!this._ticking&&(null==(t=this.elements)?void 0:t.length)>0&&(this._ticking=!0,window.requestAnimationFrame(this._updateAllElements))},e._handleUpdateCache=function(){this._setViewSize(),this._updateAllElements({updateCache:!0})},e._updateAllElements=function(t){var e=this,i=(void 0===t?{}:t).updateCache;this.elements&&this.elements.forEach(function(t){i&&t.setCachedAttributes(e.view,e.scroll),e._updateElementPosition(t)}),this._ticking=!1},e._updateElementPosition=function(t){t.props.disabled||this.disabled||t.updatePosition(this.scroll)},e._getViewParams=function(){if(this._hasScrollContainer){var t=this.viewEl.offsetWidth,e=this.viewEl.offsetHeight,i=this.viewEl.scrollHeight,n=this.viewEl.scrollWidth;return this.view.setSize({width:t,height:e,scrollHeight:i,scrollWidth:n})}var s=document.documentElement;return{width:window.innerWidth||s.clientWidth,height:window.innerHeight||s.clientHeight,scrollHeight:s.scrollHeight,scrollWidth:s.scrollWidth}},e._setViewSize=function(){return this.view.setSize(this._getViewParams())},e._checkIfViewHasChanged=function(){return this.view.hasChanged(this._getViewParams())},e.getElements=function(){return this.elements},e.createElement=function(t){var e=new V(m({},t,{scrollAxis:this.scrollAxis,disabledParallaxController:this.disabled}));return e.setCachedAttributes(this.view,this.scroll),this.elements=this.elements?[].concat(this.elements,[e]):[e],this._updateElementPosition(e),this._checkIfViewHasChanged()&&this.update(),e},e.removeElementById=function(t){this.elements&&(this.elements=this.elements.filter(function(e){return e.id!==t}))},e.updateElementPropsById=function(t,e){this.elements&&(this.elements=this.elements.map(function(i){return i.id===t?i.updateProps(e):i})),this.update()},e.resetElementStyles=function(t){Y(t)},e.update=function(){var t=this._getScrollPosition(),e=t[0],i=t[1];this.scroll.setScroll(e,i),this._setViewSize(),this._updateAllElements({updateCache:!0})},e.updateScrollContainer=function(t){this._removeListeners(this.viewEl),this.viewEl=t,this._hasScrollContainer=!!t,this.view=new H({width:0,height:0,scrollWidth:0,scrollHeight:0,scrollContainer:t}),this._setViewSize(),this._addListeners(this.viewEl),this._updateAllElements({updateCache:!0})},e.disableParallaxController=function(){this.disabled=!0,this._removeListeners(this.viewEl),this.elements&&this.elements.forEach(function(t){return Y(t)})},e.enableParallaxController=function(){var t=this;this.disabled=!1,this.elements&&this.elements.forEach(function(e){return e.updateElementOptions({disabledParallaxController:!1,scrollAxis:t.scrollAxis})}),this._addListeners(this.viewEl),this._addResizeObserver(),this._setViewSize()},e.disableAllElements=function(){console.warn("deprecated: use disableParallaxController() instead"),this.elements&&(this.elements=this.elements.map(function(t){return t.updateProps({disabled:!0})})),this.update()},e.enableAllElements=function(){console.warn("deprecated: use enableParallaxController() instead"),this.elements&&(this.elements=this.elements.map(function(t){return t.updateProps({disabled:!1})})),this.update()},e.destroy=function(){this._removeListeners(this.viewEl),this.elements&&this.elements.forEach(function(t){return Y(t)}),this.elements=void 0},t}(),Q=i(2265);function Z(){return(Z=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}).apply(this,arguments)}function j(t,e){if(null==t)return{};var i,n,s={},r=Object.keys(t);for(n=0;n<r.length;n++)i=r[n],e.indexOf(i)>=0||(s[i]=t[i]);return s}var R=["disabled","easing","endScroll","onChange","onEnter","onExit","onProgressChange","opacity","rootMargin","rotate","rotateX","rotateY","rotateZ","scale","scaleX","scaleY","scaleZ","shouldAlwaysCompleteAnimation","shouldDisableScalingTranslations","speed","startScroll","targetElement","translateX","translateY"];function B(t){var e,i=t.disabled,n=t.easing,s=t.endScroll,r=t.onChange,a=t.onEnter,l=t.onExit,o=t.onProgressChange,u=t.opacity,h=t.rootMargin,c=t.rotate,d=t.rotateX,f=t.rotateY,p=t.rotateZ,v=t.scale,g=t.scaleX,m=t.scaleY,w=t.scaleZ,b=t.shouldAlwaysCompleteAnimation,E=t.shouldDisableScalingTranslations,y=t.speed,C=t.startScroll,x=t.targetElement,_=t.translateX,I=t.translateY,O=j(t,R);return{parallaxProps:(Object.keys(e={disabled:i,easing:n,endScroll:s,onChange:r,onEnter:a,onExit:l,onProgressChange:o,opacity:u,rootMargin:h,rotate:c,rotateX:d,rotateY:f,rotateZ:p,scale:v,scaleX:g,scaleY:m,scaleZ:w,shouldAlwaysCompleteAnimation:b,shouldDisableScalingTranslations:E,speed:y,startScroll:C,targetElement:x,translateX:_,translateY:I}).forEach(function(t){return void 0===e[t]&&delete e[t]}),e),rest:O}}var T=Q.createContext(null);function D(t){var e=function(){var t=(0,Q.useContext)(T);if("undefined"==typeof window)return null;if(!t)throw Error("Could not find `react-scroll-parallax` context value. Please ensure the component is wrapped in a <ParallaxProvider>");return t}(),i=(0,Q.useRef)(null),n=B(t).parallaxProps;(0,Q.useEffect)(function(){var t=e instanceof W;if("undefined"!=typeof window&&!e&&!t)throw Error("Must wrap your application's <Parallax /> components in a <ParallaxProvider />.")},[e]);var s=(0,Q.useState)(),r=s[0],a=s[1];return(0,Q.useEffect)(function(){var t;if(i.current instanceof HTMLElement){var s={el:i.current,props:n};a(t=null==e?void 0:e.createElement(s))}else throw Error("You must assign the ref returned by the useParallax() hook to an HTML Element.");return function(){t&&(null==e||e.removeElementById(t.id))}},[]),(0,Q.useEffect)(function(){r&&(t.disabled&&(null==e||e.resetElementStyles(r)),null==e||e.updateElementPropsById(r.id,n))},[t.disabled,t.easing,t.endScroll,t.onChange,t.onEnter,t.onExit,t.onProgressChange,t.opacity,t.rootMargin,t.rotate,t.rotateX,t.rotateY,t.rotateZ,t.scale,t.scaleX,t.scaleY,t.scaleZ,t.shouldAlwaysCompleteAnimation,t.shouldDisableScalingTranslations,t.speed,t.startScroll,t.targetElement,t.translateX,t.translateY]),{ref:i,controller:e,element:r}}function F(t){var e=B(t),i=e.parallaxProps,n=e.rest,s=D(i).ref;return Q.createElement("div",Object.assign({ref:s},n),t.children)}var U={height:0},N=["children","disabled","style","expanded","image","testId"],q={position:"absolute",top:0,left:0,right:0,bottom:0},K=function(t){var e=B(t),i=e.parallaxProps,n=e.rest,s=n.style,r=n.expanded,a=n.testId,l=j(n,N),o=t.image?{backgroundImage:"url("+t.image+")",backgroundPosition:"center",backgroundSize:"cover"}:{},u=void 0===r||r?function(t){if(Array.isArray(t.translateY)){var e=y(t.translateY[0]),i=y(t.translateY[1]);if("px"===e.unit&&"px"===i.unit)return{top:-1*Math.abs(i.value)+"px",bottom:-1*Math.abs(e.value)+"px"};if("%"===e.unit&&"%"===i.unit){var n,s,r=null!=(n=null==(s=t.targetElement)?void 0:s.getBoundingClientRect())?n:U;return{top:-1*Math.abs(.01*r.height*i.value)+"px",bottom:-1*Math.abs(.01*r.height*e.value)+"px"}}}if(t.speed){var a=-10*Math.abs(t.speed||0);return{top:a+"px",bottom:a+"px"}}return{}}(t):{},h=D(Z({targetElement:t.targetElement,shouldDisableScalingTranslations:!0},i));return Q.createElement("div",Object.assign({"data-testid":a,ref:h.ref,style:Z({},o,q,u,s)},l),n.children)},G=["disabled","style","layers"],J={position:"relative",overflow:"hidden",width:"100%"},$=function(t){var e=(0,Q.useState)(null),i=e[0],n=e[1],s=(0,Q.useRef)(null);(0,Q.useEffect)(function(){n(s.current)},[]);var r=t.style,a=t.layers,l=void 0===a?[]:a,o=j(t,G);return Q.createElement("div",Object.assign({ref:s,style:Z({},J,r)},o),i&&l&&l.length>0?l.map(function(t,e){return Q.createElement(K,Object.assign({},t,{targetElement:i,key:"layer-"+e,testId:"layer-"+e}))}):null,i?Q.Children.map(t.children,function(t){return(null==t?void 0:t.type)===K?Q.cloneElement(t,{targetElement:i}):t}):null)};function tt(t){var e,i=(0,Q.useRef)(null);return i.current||(i.current=(e={scrollAxis:t.scrollAxis||d.vertical,scrollContainer:t.scrollContainer,disabled:t.isDisabled},"undefined"!=typeof window?W.init(e):null)),(0,Q.useEffect)(function(){t.scrollContainer&&i.current&&i.current.updateScrollContainer(t.scrollContainer)},[t.scrollContainer,i.current]),(0,Q.useEffect)(function(){t.isDisabled&&i.current&&i.current.disableParallaxController(),!t.isDisabled&&i.current&&i.current.enableParallaxController()},[t.isDisabled,i.current]),(0,Q.useEffect)(function(){return function(){(null==i?void 0:i.current)&&(null==i||i.current.destroy())}},[]),Q.createElement(T.Provider,{value:i.current},t.children)}},271:function(t,e,i){"use strict";i.d(e,{VY:function(){return A},aV:function(){return M},fC:function(){return P},xz:function(){return Y}});var n=i(2265),s=i(6741),r=i(3966),a=i(1353),l=i(1599),o=i(6840),u=i(9114),h=i(886),c=i(9255),d=i(7437),f="Tabs",[p,v]=(0,r.b)(f,[a.Pc]),g=(0,a.Pc)(),[m,w]=p(f),b=n.forwardRef((t,e)=>{let{__scopeTabs:i,value:n,onValueChange:s,defaultValue:r,orientation:a="horizontal",dir:l,activationMode:f="automatic",...p}=t,v=(0,u.gm)(l),[g,w]=(0,h.T)({prop:n,onChange:s,defaultProp:r});return(0,d.jsx)(m,{scope:i,baseId:(0,c.M)(),value:g,onValueChange:w,orientation:a,dir:v,activationMode:f,children:(0,d.jsx)(o.WV.div,{dir:v,"data-orientation":a,...p,ref:e})})});b.displayName=f;var E="TabsList",y=n.forwardRef((t,e)=>{let{__scopeTabs:i,loop:n=!0,...s}=t,r=w(E,i),l=g(i);return(0,d.jsx)(a.fC,{asChild:!0,...l,orientation:r.orientation,dir:r.dir,loop:n,children:(0,d.jsx)(o.WV.div,{role:"tablist","aria-orientation":r.orientation,...s,ref:e})})});y.displayName=E;var C="TabsTrigger",x=n.forwardRef((t,e)=>{let{__scopeTabs:i,value:n,disabled:r=!1,...l}=t,u=w(C,i),h=g(i),c=O(u.baseId,n),f=S(u.baseId,n),p=n===u.value;return(0,d.jsx)(a.ck,{asChild:!0,...h,focusable:!r,active:p,children:(0,d.jsx)(o.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":f,"data-state":p?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:c,...l,ref:e,onMouseDown:(0,s.M)(t.onMouseDown,t=>{r||0!==t.button||!1!==t.ctrlKey?t.preventDefault():u.onValueChange(n)}),onKeyDown:(0,s.M)(t.onKeyDown,t=>{[" ","Enter"].includes(t.key)&&u.onValueChange(n)}),onFocus:(0,s.M)(t.onFocus,()=>{let t="manual"!==u.activationMode;p||r||!t||u.onValueChange(n)})})})});x.displayName=C;var _="TabsContent",I=n.forwardRef((t,e)=>{let{__scopeTabs:i,value:s,forceMount:r,children:a,...u}=t,h=w(_,i),c=O(h.baseId,s),f=S(h.baseId,s),p=s===h.value,v=n.useRef(p);return n.useEffect(()=>{let t=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(t)},[]),(0,d.jsx)(l.z,{present:r||p,children:i=>{let{present:n}=i;return(0,d.jsx)(o.WV.div,{"data-state":p?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:f,tabIndex:0,...u,ref:e,style:{...t.style,animationDuration:v.current?"0s":void 0},children:n&&a})}})});function O(t,e){return"".concat(t,"-trigger-").concat(e)}function S(t,e){return"".concat(t,"-content-").concat(e)}I.displayName=_;var P=b,M=y,Y=x,A=I}}]);