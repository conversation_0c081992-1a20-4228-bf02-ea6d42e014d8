"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[60],{44:function(e,t,E){var o=this&&this.__awaiter||function(e,t,E,o){return new(E||(E=Promise))(function(n,l){function r(e){try{a(o.next(e))}catch(e){l(e)}}function i(e){try{a(o.throw(e))}catch(e){l(e)}}function a(e){var t;e.done?n(e.value):((t=e.value)instanceof E?t:new E(function(e){e(t)})).then(r,i)}a((o=o.apply(e,t||[])).next())})},n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.initChannelMessagingFunctions=void 0;let l=E(2802),r=E(9538),i=E(6227),a=n(E(5943)),d=n(E(8946)),s=E(8372),_=E(7321),m=E(6590),u=E(84),S=E(7928),g=E(5427),T=E(71),M=E(1161),y="IMMEDIATELY_REMOVE_POINTER_LOCK",I="LAST_NAV_TREE_REFRESH_TIME";t.initChannelMessagingFunctions=()=>{var e;(0,u.resetIntermediateClassesForSliderInstantUpdate)();let t=M.defaultUIUpdateRunner;String.prototype.hashCode=function(){var e,t=0;if(0===this.length)return t;for(e=0;e<this.length;e++)t=(t<<5)-t+this.charCodeAt(e)|0;return t};let E=()=>{try{return{get passive(){return!1}}}catch(e){return!1}},n=(e=window.MutationObserver||window.WebKitMutationObserver,function(t,E){var o;let n=t.filter(e=>e&&1===e.nodeType);return 0===n.length?M.defaultUIUpdateRunner:(e?(o=new e(E),n.forEach(e=>{o.observe(e,{childList:!0,subtree:!0,attributes:!0,attributeOldValue:!0})})):window.addEventListener&&n.forEach(e=>{e.addEventListener("DOMNodeInserted",E,!1),e.addEventListener("DOMNodeRemoved",E,!1)}),e=>{o&&o.disconnect(),e(),o&&n.forEach(e=>{o.observe(e,{childList:!0,subtree:!0,attributes:!0,attributeOldValue:!0})})})}),c=e=>{let t=(0,r.getMemoryStorageItem)(r.SELECTED_ELEMENT_KEY),E=S.TempoElement.fromKey(t),o=(0,r.getMemoryStorageItem)(r.ELEMENT_KEY_TO_NAV_NODE),n=null,a=e.target;for(;a&&!n;)n=o[(0,l.getElementKeyFromNode)(a)||""],a=a.parentElement;if(!n)return m.SELECT_OR_HOVER_STORYBOARD;let d=e=>{var t,n,l,r;if(E.isEmpty())throw Error("No selected element when isNavNodeMatch called");if(!e||!e.tempoElement.codebaseId.startsWith("tempo-")||e.tempoElement.codebaseId===i.SKIP_ROOT_CODEBASE_ID)return!1;if(E.isEqual(e.tempoElement)||e.tempoElement.isParentOf(E))return!0;let a=e.parent;for(;a&&!a.tempoElement.codebaseId.startsWith("tempo-");)a=a.parent;if(null===(t=null==a?void 0:a.tempoElement)||void 0===t?void 0:t.isEqual(E))return!0;let d=o[E.getKey()];return!!(d&&(null===(r=null===(l=null===(n=e.parent)||void 0===n?void 0:n.children)||void 0===l?void 0:l.includes)||void 0===r?void 0:r.call(l,d)))},s=null,_=n;for(;_;){if(E.isEmpty()||E.isStoryboard())_.tempoElement.codebaseId&&_.tempoElement.codebaseId.startsWith("tempo-")&&(s=_);else if(d(_)){s=_;break}_=_.parent}return s||null},L=(e,t,E,o)=>{let n;let i=A(e,t,E),a=(0,g.getEditingInfo)();if(e.altKey||i&&!a||(0,r.getMemoryStorageItem)("mouseDragContext"))return;let d=(0,r.getMemoryStorageItem)(r.HOVERED_ELEMENT_KEY),_=(0,r.getMemoryStorageItem)(r.ELEMENT_KEY_TO_NAV_NODE)||{};e.metaKey||e.ctrlKey||o?(n=_[(0,l.getElementKeyFromNode)(e.target)])||e.target.parentNode!==document.body||(n=m.SELECT_OR_HOVER_STORYBOARD):n=c(e);let u=(0,r.getMemoryStorageItem)(r.SELECTED_ELEMENT_KEY),T=S.TempoElement.fromKey(u);if(e.shiftKey&&n&&u&&("string"!=typeof n||T.isStoryboard()||(n=null),"string"==typeof n||(null==n?void 0:n.tempoElement.isSiblingOf(T))||(n=null)),!n){null!==d&&((0,r.setMemoryStorageItem)(r.HOVERED_ELEMENT_KEY,null),t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.HOVERED_ELEMENT_KEY,elementKey:null}),(0,s.updateOutlines)(t,E));return}if("string"==typeof n){if(n===m.SELECT_OR_HOVER_STORYBOARD){let e=S.TempoElement.forStoryboard(E).getKey();d!==e&&((0,r.setMemoryStorageItem)(r.HOVERED_ELEMENT_KEY,e),t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.HOVERED_ELEMENT_KEY,elementKey:e}),(0,s.updateOutlines)(t,E))}return}let M=n.tempoElement.getKey();d!==M&&(t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.HOVERED_ELEMENT_KEY,elementKey:M}),(0,r.setMemoryStorageItem)(r.HOVERED_ELEMENT_KEY,M),(0,s.updateOutlines)(t,E))},O=(e,t)=>{(0,r.getMemoryStorageItem)(r.HOVERED_ELEMENT_KEY)&&(e.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.HOVERED_ELEMENT_KEY,elementKey:null}),(0,r.setMemoryStorageItem)(r.HOVERED_ELEMENT_KEY,null),(0,s.updateOutlines)(e,t))},p=(e,t,E)=>o(void 0,void 0,void 0,function*(){var o;A(e,t,E);let n=(0,r.getMemoryStorageItem)("mouseDragContext");!e.buttons&&n&&((0,r.setMemoryStorageItem)("mouseDragContext",null),(null==n?void 0:n.dragging)&&t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.DRAG_CANCEL_EVENT,event:{}}),n=null);let i={pageX:e.pageX,pageY:e.pageY,clientX:e.clientX,clientY:e.clientY};if((0,r.setMemoryStorageItem)("mousePos",i),t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.MOUSE_MOVE_EVENT,event:i}),n&&!n.dragging){let E=(0,r.getMemoryStorageItem)("zoomPerc")||1;if(Math.abs(n.pageX-e.pageX)+Math.abs(n.pageY-e.pageY)>=20/E&&(n.parentSelectedElementKey&&((0,r.getMemoryStorageItem)(r.ELEMENT_KEY_TO_NAV_NODE)||{})[n.parentSelectedElementKey]&&(t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,elementKey:n.parentSelectedElementKey,outerHTML:null===(o=(0,l.getNodeForElementKey)(n.parentSelectedElementKey))||void 0===o?void 0:o.outerHTML}),(0,r.setMemoryStorageItem)(r.SELECTED_ELEMENT_KEY,n.parentSelectedElementKey)),!(0,r.getMemoryStorageItem)("aiContext"))){(0,r.setMemoryStorageItem)("mouseDragContext",Object.assign(Object.assign({},n),{dragging:!0}));let e=(0,r.getMemoryStorageItem)(r.SELECTED_ELEMENT_KEY),E=(0,l.getNodeForElementKey)(e);t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.DRAG_START_EVENT,event:n,outerHTML:null==E?void 0:E.outerHTML});let o=(0,a.default)("body").get(0);(0,r.setMemoryStorageItem)(y,!0),yield null==o?void 0:o.requestPointerLock()}}(0,r.getMemoryStorageItem)("mouseDragContext")&&(0,s.updateOutlines)(t,E)}),v=e=>{let t;if(!e)return null;if(!(null==e?void 0:e.isComponent)){let t=(0,l.getNodeForElementKey)(e.tempoElement.getKey());return null==t?void 0:t.parentElement}return(((0,r.getMemoryStorageItem)(r.ELEMENT_KEY_TO_LOOKUP_LIST)||{})[e.tempoElement.getKey()]||[]).forEach(e=>{t||(t=(0,l.getNodeForElementKey)(e))}),null==t?void 0:t.parentElement},N=(e,t,E)=>{let o,n;if(1!==e.which||(0,l.hasClass)(e.target,l.EDIT_TEXT_BUTTON)||A(e,t,E))return;let i=(0,r.getMemoryStorageItem)(r.SELECTED_ELEMENT_KEY),a=S.TempoElement.fromKey(i),d=f(e,t,E),m=!a.isEmpty()&&a.isParentOf(null==d?void 0:d.tempoElement);(null==d?void 0:d.pageBoundingBox)&&(o=d.pageBoundingBox.pageX+d.pageBoundingBox.width/2-e.pageX,n=d.pageBoundingBox.pageY+d.pageBoundingBox.height/2-e.pageY);let u={pageX:e.pageX,pageY:e.pageY,offsetX:o,offsetY:n,parentSelectedElementKey:m?i:null},g=(0,r.getMemoryStorageItem)(r.ELEMENT_KEY_TO_NAV_NODE)||{},T=v(m?g[i]:d);T&&(u.selectedParentDisplay=(0,_.cssEval)(T,"display"),u.selectedParentFlexDirection=(0,_.cssEval)(T,"flex-direction")),(0,r.getMemoryStorageItem)("aiContext")||(0,r.setMemoryStorageItem)("mouseDragContext",u),(0,s.updateOutlines)(t,E)},D=(e,t,E)=>{A(e,t,E);let o=(0,r.getMemoryStorageItem)("mouseDragContext");(0,r.setMemoryStorageItem)("mouseDragContext",null),(null==o?void 0:o.dragging)&&t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.DRAG_END_EVENT,event:{}}),(0,s.updateOutlines)(t,E)},f=(e,t,E)=>{var o,n,i;let a;if((0,r.getSessionStorageItem)("driveModeEnabled",E))return null;let d=(0,r.getMemoryStorageItem)(r.ELEMENT_KEY_TO_NAV_NODE)||{};e.metaKey||e.ctrlKey?(a=d[(0,l.getElementKeyFromNode)(e.target)])||e.target.parentNode!==document.body||(a=m.SELECT_OR_HOVER_STORYBOARD):a=c(e);let _=(0,r.getMemoryStorageItem)(r.SELECTED_ELEMENT_KEY);if(!a)return _&&(t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,elementKey:null}),(0,r.setMemoryStorageItem)(r.SELECTED_ELEMENT_KEY,null),(0,s.updateOutlines)(t,E)),null;let u=S.TempoElement.fromKey(_),T=(0,r.getMemoryStorageItem)(r.MULTI_SELECTED_ELEMENT_KEYS)||[],M="string"==typeof a?S.TempoElement.forStoryboard(E):a.tempoElement,y=[];if(e.shiftKey&&_){let e=T.map(e=>S.TempoElement.fromKey(e)).find(e=>e.isParentOf(M)||e.isEqual(M));if(e)y=T.filter(t=>t!==e.getKey()),e.isEqual(u)&&y.length>1&&(t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,elementKey:y[0],outerHTML:null===(o=(0,l.getNodeForElementKey)(y[0]))||void 0===o?void 0:o.outerHTML}),(0,r.setMemoryStorageItem)(r.SELECTED_ELEMENT_KEY,y[0]));else{if(!u.isSiblingOf(M))return null;y=(null==T?void 0:T.length)?T.concat([M.getKey()]):[_,M.getKey()]}}if(y.length>1)return t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.MULTI_SELECTED_ELEMENT_KEYS,elementKeys:y,outerHTMLs:null==y?void 0:y.map(e=>{var t;return null===(t=(0,l.getNodeForElementKey)(e))||void 0===t?void 0:t.outerHTML})}),(0,r.setMemoryStorageItem)(r.MULTI_SELECTED_ELEMENT_KEYS,y),(0,s.updateOutlines)(t,E),(0,g.teardownEditableText)(t,E),null;1===y.length&&(M=S.TempoElement.fromKey(y[0]));let I=()=>{t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.MULTI_SELECTED_ELEMENT_KEYS,elementKeys:[],outerHTMLs:[]}),(0,r.setMemoryStorageItem)(r.MULTI_SELECTED_ELEMENT_KEYS,null)};if(M.isStoryboard())return M.getKey()!==_&&(t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,elementKey:M.getKey(),outerHTML:null===(n=(0,l.getNodeForElementKey)(M.getKey()))||void 0===n?void 0:n.outerHTML}),(0,r.setMemoryStorageItem)(r.SELECTED_ELEMENT_KEY,M.getKey()),(0,s.updateOutlines)(t,E)),(0,g.teardownEditableText)(t,E),I(),null;if((0,g.currentlyEditing)()){let e=(0,g.getEditingInfo)();return(null==e?void 0:e.key)!==_&&(0,g.teardownEditableText)(t,E),I(),null}return e.preventDefault(),e.stopPropagation(),(0,g.canEditText)(M)&&M.getKey()===_&&(0,g.setupEditableText)(M,t,E),M.getKey()===_||(t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,elementKey:M.getKey(),outerHTML:null===(i=(0,l.getNodeForElementKey)(M.getKey()))||void 0===i?void 0:i.outerHTML}),(0,r.setMemoryStorageItem)(r.SELECTED_ELEMENT_KEY,M.getKey()),(0,s.updateOutlines)(t,E)),I(),a},A=(e,t,E)=>{var o,n;let l=!!(0,r.getSessionStorageItem)("driveModeEnabled",E),i=(0,g.getEditingInfo)();return!!l||!!i||(null===(o=null==e?void 0:e.preventDefault)||void 0===o||o.call(e),null===(n=null==e?void 0:e.stopPropagation)||void 0===n||n.call(e),!1)},K=(e,t,E)=>{var o;let n;if(A(e,t,E))return;e.preventDefault(),e.stopPropagation(),(0,r.setMemoryStorageItem)("mouseDragContext",null);let i=(0,r.getMemoryStorageItem)(r.ELEMENT_KEY_TO_NAV_NODE)||{};e.metaKey||e.ctrlKey?(n=i[(0,l.getElementKeyFromNode)(e.target)])||e.target.parentNode!==document.body||(n=m.SELECT_OR_HOVER_STORYBOARD):n=c(e);let a=(0,r.getMemoryStorageItem)(r.SELECTED_ELEMENT_KEY),d=(0,r.getMemoryStorageItem)(r.MULTI_SELECTED_ELEMENT_KEYS);if(!n||"string"==typeof n){if(n===m.SELECT_OR_HOVER_STORYBOARD&&!(null==d?void 0:d.length)){let e=S.TempoElement.forStoryboard(E).getKey();if(a===e)return;t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,elementKey:e}),(0,r.setMemoryStorageItem)(r.SELECTED_ELEMENT_KEY,e),(0,s.updateOutlines)(t,E)}return}let _=null,u=(0,r.getMemoryStorageItem)(r.SELECTED_ELEMENT_KEY),g=S.TempoElement.fromKey(u);n.tempoElement.isEqual(g)||g.isParentOf(n.tempoElement)||(null==d?void 0:d.length)||(_=n.tempoElement.getKey(),t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,elementKey:_,outerHTML:null===(o=(0,l.getNodeForElementKey)(_))||void 0===o?void 0:o.outerHTML}),(0,r.setMemoryStorageItem)(r.SELECTED_ELEMENT_KEY,_),(0,s.updateOutlines)(t,E));let T={clientX:e.clientX,clientY:e.clientY};t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.CONTEXT_REQUESTED,event:T})},R=(e,t,E,n,l)=>o(void 0,void 0,void 0,function*(){let o=E;o||(o=(0,r.getMemoryStorageItem)(r.TREE_ELEMENT_LOOKUP)||{});let d=n;d||(d=(0,r.getMemoryStorageItem)(r.SCOPE_LOOKUP)||{});let s=l;"EXPLICIT_NONE"===l?s=null:s||(s=(0,r.getMemoryStorageItem)(r.STORYBOARD_COMPONENT)||{});let _=new Set,u=new Set;o&&Object.values(o).forEach(e=>{("component"===e.type||"storybook-component"===e.type)&&_.add(e.componentName),"component-instance"===e.type&&u.add(e.componentName)});let S={},g={},T=yield new Promise((e,E)=>{(0,i.buildNavForNodeNonBlocking)({storyboardId:t,parent:void 0,node:(0,a.default)("body").get(0),uniquePathBase:"",uniquePathAddon:"root",scopeLookup:d,treeElements:o,knownComponentNames:_,knownComponentInstanceNames:u,elementKeyToLookupList:S,elementKeyToNavNode:g,domUniquePath:"0"},t=>{e(t)})});(0,r.setMemoryStorageItem)(r.ELEMENT_KEY_TO_LOOKUP_LIST,S),(0,r.setMemoryStorageItem)(r.CURRENT_NAV_TREE,T),(0,r.setMemoryStorageItem)(r.ELEMENT_KEY_TO_NAV_NODE,g),e.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.NAV_TREE,navTree:T,outerHtml:document.documentElement.outerHTML}),(0,i.runNavTreeBuiltCallbacks)()}),h=()=>{let e=[];(0,a.default)(`*[class*=${l.TEMPO_INSTANT_UPDATE_STYLING_PREFIX}]`).each((t,E)=>{(E.getAttribute("class")||"").split(" ").forEach(t=>{t.startsWith(l.TEMPO_INSTANT_UPDATE_STYLING_PREFIX)&&e.push(t)})}),(0,a.default)(`*[${l.TEMPO_DELETE_AFTER_REFRESH}=true]`).attr(l.TEMPO_QUEUE_DELETE_AFTER_HOT_RELOAD,"true"),(0,r.setMemoryStorageItem)(u.ADD_CLASS_INSTANT_UPDATE_QUEUE,[]),(0,r.setMemoryStorageItem)("POST_HOT_RELOAD_CLEAR",{classesToDelete:e})},C=(e,E)=>o(void 0,void 0,void 0,function*(){t(()=>{(0,r.setMemoryStorageItem)(I,new Date);let{classesToDelete:e}=(0,r.getMemoryStorageItem)("POST_HOT_RELOAD_CLEAR")||{};(0,a.default)(`*[${l.TEMPO_QUEUE_DELETE_AFTER_HOT_RELOAD}=true]`).remove(),(0,a.default)(`.${l.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS}`).removeClass(l.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS),(0,a.default)(`*[${l.TEMPO_INSTANT_UPDATE}=true]`).removeAttr(l.TEMPO_INSTANT_UPDATE),(0,a.default)(`*[${l.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH}=true]`).removeAttr(l.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH),(0,a.default)(`.${u.TEMPORARY_STYLING_CLASS_NAME}`).removeClass(u.TEMPORARY_STYLING_CLASS_NAME),null==e||e.forEach(e=>{(0,a.default)(`.${e}`).removeClass(e)}),((0,r.getMemoryStorageItem)(u.ADD_CLASS_INSTANT_UPDATE_QUEUE)||[]).forEach(e=>{if(!e)return;let{codebaseId:t,className:E}=e;t&&E&&((0,a.default)(`.${t}`).attr(l.TEMPO_INSTANT_UPDATE,"true"),(0,a.default)(`.${t}`).addClass(E))})});try{yield(0,M.sleep)(300),t(()=>o(void 0,void 0,void 0,function*(){yield R(e,E),(0,s.updateOutlines)(e,E)}))}catch(e){console.error("ERROR: Could not re-create nav tree on DOM change, "+e)}}),w=({mutations:e,parentPort:t,storyboardId:E,fromNextJsLoader:o})=>{var n;(0,r.getMemoryStorageItem)("href")!==window.location.href&&(t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.LATEST_HREF,href:window.location.href}),(0,r.setMemoryStorageItem)("href",window.location.href));let i=!1;if(o){let t=null===(n=null==e?void 0:e[0])||void 0===n?void 0:n.target;t&&"container"===t.id&&((0,r.getMemoryStorageItem)(r.HOT_RELOADING),t.classList.contains("visible")?(0,r.setMemoryStorageItem)(r.HOT_RELOADING,!0):((0,r.setMemoryStorageItem)(r.HOT_RELOADING,!1),i=!0))}else e.forEach(e=>{if(!i){if("attributes"===e.type&&"class"===e.attributeName&&e.target&&!(0,s.isNodeOutline)(e.target)&&!(0,l.isMovingElement)(e.target)&&!e.target.tagName.toLowerCase().includes("script")){(0,l.isElementInSvg)(e.target)||(i=!0);return}[e.addedNodes,e.removedNodes].forEach(e=>{!i&&e&&e.forEach(e=>{if(!(0,s.isNodeOutline)(e)&&!(0,l.isMovingElement)(e)){i=!0;return}})})}});if(i){if(o){let e=new Date;setTimeout(()=>{let o=(0,r.getMemoryStorageItem)(I);(!o||o<e)&&C(t,E)},1e3);return}C(t,E)}},Y=(e,t,E)=>{let o=A(e,t,E),n=e.altKey;if(!(e.ctrlKey||e.metaKey)&&(o||n))return;e.preventDefault(),e.stopPropagation();let l={deltaX:e.deltaX,deltaY:e.deltaY,wheelDelta:e.wheelDelta,x:e.x,y:e.y,altKey:e.altKey,ctrlKey:e.ctrlKey,shiftKey:e.shiftKey,metaKey:e.metaKey};t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.WHEEL_EVENT,event:l})},F=()=>{let e,t,E;let o=document.activeElement;return o&&(e=o.tagName,o instanceof HTMLElement&&(t=o.isContentEditable),o instanceof HTMLInputElement&&(E=o.type)),{tagName:e,isContentEditable:t,elementType:E}},P=(e,t)=>{t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.KEY_DOWN_EVENT,event:{key:e.key,metaKey:e.metaKey,shiftKey:e.shiftKey,ctrlKey:e.ctrlKey,activeElement:Object.assign({},F())}})},b=(e,t)=>{t.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.KEY_UP_EVENT,event:{key:e.key,metaKey:e.metaKey,shiftKey:e.shiftKey,ctrlKey:e.ctrlKey,activeElement:Object.assign({},F())}})},H=d.default.throttle((e,t)=>(0,s.updateOutlines)(e,t),15),U=(e,t,E)=>{H(t,E)};window.initProject=(e,o,l,i,d,_={},m,u,S)=>{let g=E();g.capture=!0;let M=(0,a.default)("body");(0,r.setMemoryStorageItem)(r.TREE_ELEMENT_LOOKUP,l),(0,r.setMemoryStorageItem)(r.SCOPE_LOOKUP,i),d&&(0,r.setMemoryStorageItem)(r.STORYBOARD_COMPONENT,d),(0,r.setMemoryStorageItem)(r.STORYBOARD_TYPE,m),(0,r.setMemoryStorageItem)(r.SAVED_STORYBOARD_COMPONENT_FILENAME,u),S&&(0,r.setMemoryStorageItem)(r.ORIGINAL_STORYBOARD_URL,S),(0,r.removeMemoryStorageItem)(r.SELECTED_ELEMENT_KEY),(0,r.removeMemoryStorageItem)(r.HOVERED_ELEMENT_KEY),(0,s.updateOutlines)(e,o);let I=M.get(0);null==I||I.addEventListener("click",t=>{A(t,e,o)},g),null==I||I.addEventListener("pointerover",t=>{L(t,e,o)},g),null==I||I.addEventListener("pointerdown",t=>{N(t,e,o)},g),null==I||I.addEventListener("pointerup",t=>{D(t,e,o)},g),null==I||I.addEventListener("pointermove",t=>{p(t,e,o)},g),null==I||I.addEventListener("pointerleave",t=>{A(t,e,o)},g),null==I||I.addEventListener("contextmenu",t=>{K(t,e,o)},g),null==I||I.addEventListener("dblclick",t=>{A(t,e,o)},g),null==I||I.addEventListener("mouseover",t=>{A(t,e,o)},g),null==I||I.addEventListener("mouseout",t=>{A(t,e,o)},g),null==I||I.addEventListener("mousemove",t=>{A(t,e,o)},g),null==I||I.addEventListener("mousedown",t=>{A(t,e,o)},g),null==I||I.addEventListener("mouseup",t=>{A(t,e,o)},g),null==I||I.addEventListener("wheel",t=>{Y(t,e,o)},g),null==I||I.addEventListener("keydown",t=>{P(t,e)},g),null==I||I.addEventListener("keyup",t=>{b(t,e)},g),window.addEventListener("scroll",t=>{U(t,e,o)},g),document.addEventListener("pointerlockchange",()=>{document.pointerLockElement&&(0,r.getMemoryStorageItem)(y)&&(document.exitPointerLock(),(0,r.setMemoryStorageItem)(y,!1))},!1);let c=new T.DebounceExecutor,O=[I],v=document.getElementById("__next-build-watcher");v&&v.shadowRoot&&O.push(...Array.from(v.shadowRoot.children)),t=n(O,t=>{c.schedule(()=>{w({mutations:t,parentPort:e,storyboardId:o})})}),_.driveModeEnabled?V(e,o):X(e,o),_.aiContextSelection?(0,r.setMemoryStorageItem)("aiContext",!0):(0,r.setMemoryStorageItem)("aiContext",!1),(0,s.updateOutlines)(e,o);try{t(()=>{R(e,o,l,i,d||"EXPLICIT_NONE")})}catch(e){console.log(e),console.error("Error building nav tree: "+e)}};let V=(e,t)=>{(0,r.getSessionStorageItem)("driveModeEnabled",t)||((0,r.setSessionStorageItem)("driveModeEnabled","enabled",t),O(e,t),(0,s.clearAllOutlines)()),(0,a.default)("body").css("cursor","")},X=(e,t)=>{(0,r.getSessionStorageItem)("driveModeEnabled",t)&&((0,r.removeSessionStorageItem)("driveModeEnabled",t),(0,s.updateOutlines)(e,t),O(e,t)),(0,a.default)("body").attr("style",function(e,t){return(t||"")+"cursor: default !important;"})};window.enableDriveMode=(e,t)=>{V(e,t)},window.disableDriveMode=(e,t)=>{X(e,t)},window.setNewLookups=(e,t,E,o)=>{let n=(0,r.getMemoryStorageItem)(r.TREE_ELEMENT_LOOKUP)||{},l=(0,r.getMemoryStorageItem)(r.SCOPE_LOOKUP)||{},i=Object.assign({},n);Object.keys(E).forEach(e=>{E[e]?i[e]=E[e]:i[e]&&delete i[e]});let a=Object.assign({},l);Object.keys(o).forEach(e=>{o[e]?a[e]=o[e]:a[e]&&delete a[e]}),(0,r.setMemoryStorageItem)(r.TREE_ELEMENT_LOOKUP,i),(0,r.setMemoryStorageItem)(r.SCOPE_LOOKUP,a)},window.setHoveredElement=(e,t,E)=>{(0,r.getSessionStorageItem)("driveModeEnabled",t)||(0,r.getMemoryStorageItem)(r.HOVERED_ELEMENT_KEY)===E||(E?(0,r.setMemoryStorageItem)(r.HOVERED_ELEMENT_KEY,E):(0,r.removeMemoryStorageItem)(r.HOVERED_ELEMENT_KEY),(0,s.updateOutlines)(e,t))},window.setSelectedElement=(e,t,E)=>{var o,n;if((0,r.getMemoryStorageItem)(r.SELECTED_ELEMENT_KEY)!==E){if(E){let i=S.TempoElement.fromKey(E),a=E;if(i.isStoryboard(t)){let e=(0,r.getMemoryStorageItem)(r.CURRENT_NAV_TREE),t=null===(o=null==e?void 0:e.tempoElement)||void 0===o?void 0:o.getKey();t&&(a=t)}e.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,doNotSetElementKey:!0,outerHTML:null===(n=(0,l.getNodeForElementKey)(a))||void 0===n?void 0:n.outerHTML}),(0,r.setMemoryStorageItem)(r.SELECTED_ELEMENT_KEY,E)}else e.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,doNotSetElementKey:!0,outerHTML:null}),(0,r.removeMemoryStorageItem)(r.SELECTED_ELEMENT_KEY);(0,s.updateOutlines)(e,t)}},window.setMultiselectedElementKeys=(e,t,E)=>{let o=new Set((0,r.getMemoryStorageItem)(r.MULTI_SELECTED_ELEMENT_KEYS)||[]),n=new Set(E||[]);o.size===n.size&&[...o].every(e=>n.has(e))||(E?((0,r.setMemoryStorageItem)(r.MULTI_SELECTED_ELEMENT_KEYS,E),e.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.MULTI_SELECTED_ELEMENT_KEYS,doNotSetElementKeys:!0,outerHTMLs:null==E?void 0:E.map(e=>{var t;return null===(t=(0,l.getNodeForElementKey)(e))||void 0===t?void 0:t.outerHTML})})):((0,r.removeMemoryStorageItem)(r.MULTI_SELECTED_ELEMENT_KEYS),e.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.MULTI_SELECTED_ELEMENT_KEYS,doNotSetElementKeys:!0,outerHTMLs:[]})),(0,s.updateOutlines)(e,t))},window.processRulesForSelectedElement=(e,t,E,o)=>{(0,_.processRulesForSelectedElement)(e,E,o)},window.setModifiersForSelectedElement=(e,t,E,o)=>{(0,_.setModifiersForSelectedElement)(e,E,o)},window.getCssEvals=(e,t,E)=>{(0,_.getCssEvals)(e,E)},window.ruleMatchesElement=(e,t,E,o,n)=>{(0,_.ruleMatchesElement)(e,E,o,n)},window.getElementClassList=(e,t,E)=>{(0,_.getElementClassList)(e,E)},window.applyChangeItemToDocument=(e,t,E)=>o(void 0,void 0,void 0,function*(){let{sendNewNavTree:o}=(0,u.applyChangeItemToDocument)(e,t,E);o&&(yield R(e,t)),(0,s.updateOutlines)(e,t)}),window.updateCodebaseIds=(e,t,E,n,l)=>o(void 0,void 0,void 0,function*(){(0,u.updateCodebaseIds)(e,E,!0)&&(yield R(e,t,n,l)),(0,s.updateOutlines)(e,t)}),window.dispatchEvent=(e,t,E,o)=>{let n=new CustomEvent(E,Object.assign({},o));document.dispatchEvent(n)},window.updateOutlines=(e,t)=>{(0,s.updateOutlines)(e,t)},window.goBack=(e,t)=>{""!==document.referrer&&window.history.back()},window.goForward=(e,t)=>{window.history.forward()},window.refresh=(e,t)=>{window.location.reload()},window.syntheticMouseOver=(e,t,E,o,n)=>{let i=document.elementFromPoint(E.x,E.y);if(o){let E=(0,r.getMemoryStorageItem)(r.SELECTED_ELEMENT_KEY);if(!S.TempoElement.fromKey(E).isEmpty()){let o=(0,l.getNodeForElementKey)(E);if(null==o?void 0:o.contains(i)){L({target:o},e,t);return}}}L({target:i},e,t,n)},window.syntheticMouseMove=(e,t,E)=>{p(Object.assign(Object.assign({},E),{pageX:E.clientX+(document.documentElement.scrollLeft||document.body.scrollLeft),pageY:E.clientY+(document.documentElement.scrollTop||document.body.scrollTop)}),e,t)},window.syntheticMouseUp=(e,t,E)=>{D(E,e,t)},window.clearHoveredOutlines=(e,t)=>{(0,r.getMemoryStorageItem)(r.HOVERED_ELEMENT_KEY)&&O(e,t)},window.setZoomPerc=(e,t,E)=>{(0,r.setMemoryStorageItem)("zoomPerc",E.toString()),(0,s.updateOutlines)(e,t)},window.setAiContext=(e,t,E)=>{(0,r.setMemoryStorageItem)("aiContext",!!E),(0,s.updateOutlines)(e,t)},window.tempMoveElement=(e,t,E,n)=>o(void 0,void 0,void 0,function*(){var o,i,d,_,u;let g=((0,r.getMemoryStorageItem)(r.ELEMENT_KEY_TO_NAV_NODE)||{})[E];if(!g)return;let T=S.TempoElement.fromKey(E),M=[],y=(0,r.getMemoryStorageItem)(r.ELEMENT_KEY_TO_LOOKUP_LIST)||{};(y[g.tempoElement.getKey()]||[]).forEach(e=>{M.push((0,l.getNodeForElementKey)(e))});let I=null===(o=M[0])||void 0===o?void 0:o.parentElement,c=g.parent;if(I&&c){let E=null===(i=null==c?void 0:c.children)||void 0===i?void 0:i.indexOf(g),o=null===(d=null==c?void 0:c.children)||void 0===d?void 0:d.length;if(E!==n){if(Array.from(I.children).forEach(e=>{(0,a.default)(e).attr(l.TEMPO_INSTANT_UPDATE,"true")}),(0,a.default)(I).attr(l.TEMPO_INSTANT_UPDATE,"true"),n===o-1)M.forEach(e=>{e.parentElement.appendChild(e)});else{let e=E>n?null==c?void 0:c.children[n]:null==c?void 0:c.children[n+1],t=y[null===(_=null==e?void 0:e.tempoElement)||void 0===_?void 0:_.getKey()]||[];if(!t.length){console.log("Cannot find element to insert before in lookup list");return}let o=(0,l.getNodeForElementKey)(t[0]);if(!o){console.log("Cannot find element to insert before");return}M.forEach(e=>{e.parentElement.insertBefore(e,o)})}let i=T.uniquePath.split("-"),d=i.slice(0,i.length-1).join("-")+`-${n}`,g=new S.TempoElement(T.codebaseId,T.storyboardId,d).getKey();yield R(e,t),e.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,elementKey:g,outerHTML:null===(u=(0,l.getNodeForElementKey)(g))||void 0===u?void 0:u.outerHTML}),(0,r.setMemoryStorageItem)(r.SELECTED_ELEMENT_KEY,g),(0,s.updateOutlines)(e,t)}}}),window.tempAddDiv=(e,t,E,n,r,i)=>o(void 0,void 0,void 0,function*(){let o=(0,a.default)(`.${l.TEMPO_INSTANT_DIV_DRAW_CLASS}`);if(o.length)o.css("width",r),o.css("height",i);else{let o=(0,a.default)(`.${E}`);o.length||(o=(0,a.default)("body")),o.each((e,t)=>{let E=(0,a.default)(`<div class="${l.TEMPO_INSTANT_DIV_DRAW_CLASS}" ${l.TEMPO_DELETE_AFTER_INSTANT_UPDATE}="true" ${l.TEMPO_DELETE_AFTER_REFRESH}="true" ${l.TEMPO_INSTANT_UPDATE}="true"></div>`),o=(0,a.default)(t).children().eq(n);(null==o?void 0:o.length)?o.before(E):(0,a.default)(t).append(E)}),yield R(e,t)}(0,s.updateOutlines)(e,t)}),window.tempMoveToNewParent=(e,t,E,o,n,r,i,d)=>{if((0,a.default)(`.${l.TEMPO_MOVE_BETWEEN_PARENTS_OUTLINE}`).remove(),d)return;let _=document.createElement("div");_.classList.add(l.TEMPO_MOVE_BETWEEN_PARENTS_OUTLINE),_.setAttribute(l.TEMPO_INSTANT_UPDATE,"true"),_.style.width=E+"px",_.style.height=o+"px",_.style.left=n+"px",_.style.top=r+"px",_.style.position="fixed",_.style.pointerEvents="none",_.style.zIndex="2000000004",_.style.boxSizing="border-box",_.style.cursor="default !important",_.style.backgroundColor=s.PRIMARY_OUTLINE_COLOUR;let m=document.getElementsByTagName("body")[0];m.appendChild(_);let u=(0,l.getNodeForElementKey)(i);if(u){let t=u.getBoundingClientRect(),E=(0,s.getOutlineElement)(e,s.OutlineType.PRIMARY,t.left,t.top,t.width,t.height);E.classList.remove(l.OUTLINE_CLASS),E.classList.add(l.TEMPO_MOVE_BETWEEN_PARENTS_OUTLINE),E.setAttribute(l.TEMPO_INSTANT_UPDATE,"true"),m.appendChild(E)}},window.checkIfHydrationError=(e,t)=>{var E,o,n,l,r,i,a,d,s,_,u,S,g,T,M;let y,I,c,L;if(window.location.href.includes("framework=VITE")){let e=null===(E=document.getElementsByTagName("vite-error-overlay")[0])||void 0===E?void 0:E.shadowRoot;y="A Vite Error Occurred",I=null===(l=null===(n=null===(o=null==e?void 0:e.querySelectorAll)||void 0===o?void 0:o.call(e,".file-link"))||void 0===n?void 0:n[0])||void 0===l?void 0:l.innerHTML,c=null===(a=null===(i=null===(r=null==e?void 0:e.querySelectorAll)||void 0===r?void 0:r.call(e,".message"))||void 0===i?void 0:i[0])||void 0===a?void 0:a.innerHTML,L=!!(I||c)}else{let e=null===(d=document.getElementsByTagName("nextjs-portal")[0])||void 0===d?void 0:d.shadowRoot;y=null===(_=null===(s=null==e?void 0:e.getElementById)||void 0===s?void 0:s.call(e,"nextjs__container_errors_desc"))||void 0===_?void 0:_.innerHTML,I=null===(S=null===(u=null==e?void 0:e.getElementById)||void 0===u?void 0:u.call(e,"nextjs__container_errors_label"))||void 0===S?void 0:S.innerHTML,c=null===(M=null===(T=null===(g=null==e?void 0:e.querySelectorAll)||void 0===g?void 0:g.call(e,".nextjs-container-errors-body"))||void 0===T?void 0:T[0])||void 0===M?void 0:M.innerHTML,L=!!y}L?(null==y?void 0:y.includes("Hydration failed"))?e.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.LATEST_HYDRATION_ERROR_STATUS,status:m.STORYBOARD_HYDRATION_STATUS.ERROR,errorDescr:y,errorLabel:I,errorBody:c}):e.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.LATEST_HYDRATION_ERROR_STATUS,status:m.STORYBOARD_HYDRATION_STATUS.OTHER_ERROR,errorDescr:y,errorLabel:I,errorBody:c}):e.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.LATEST_HYDRATION_ERROR_STATUS,status:m.STORYBOARD_HYDRATION_STATUS.NO_ERROR})},window.triggerDragStart=(e,t)=>{let E=(0,r.getMemoryStorageItem)(r.SELECTED_ELEMENT_KEY),o=(0,r.getMemoryStorageItem)(r.ELEMENT_KEY_TO_NAV_NODE)||{};if(!E)return;let n=v(o[E]),i=(0,l.getNodeForElementKey)(E),a={pageX:-1e4,pageY:-1e4,offsetX:0,offsetY:0,dragging:!0,selectedParentDisplay:(0,_.cssEval)(n,"display"),selectedParentFlexDirection:(0,_.cssEval)(n,"flex-direction")};(0,r.setMemoryStorageItem)("mouseDragContext",a),e.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.DRAG_START_EVENT,event:a,outerHTML:null==i?void 0:i.outerHTML}),(0,s.updateOutlines)(e,t)},window.triggerDragCancel=(e,t)=>{(0,r.setMemoryStorageItem)("mouseDragContext",null),e.postMessage({id:m.FIXED_IFRAME_MESSAGE_IDS.DRAG_CANCEL_EVENT,event:{}}),(0,s.updateOutlines)(e,t)},window.setIsFlushing=(e,t,E)=>{let o=(0,r.getMemoryStorageItem)(r.IS_FLUSHING);(0,r.setMemoryStorageItem)(r.IS_FLUSHING,E),E&&!o&&h()}}}}]);