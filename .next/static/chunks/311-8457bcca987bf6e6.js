(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[311],{2667:function(e,t,n){var r,a=function(){var e=String.fromCharCode,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",r={};function a(e,t){if(!r[e]){r[e]={};for(var n=0;n<e.length;n++)r[e][e.charAt(n)]=n}return r[e][t]}var o={compressToBase64:function(e){if(null==e)return"";var n=o._compress(e,6,function(e){return t.charAt(e)});switch(n.length%4){default:case 0:return n;case 1:return n+"===";case 2:return n+"==";case 3:return n+"="}},decompressFromBase64:function(e){return null==e?"":""==e?null:o._decompress(e.length,32,function(n){return a(t,e.charAt(n))})},compressToUTF16:function(t){return null==t?"":o._compress(t,15,function(t){return e(t+32)})+" "},decompressFromUTF16:function(e){return null==e?"":""==e?null:o._decompress(e.length,16384,function(t){return e.charCodeAt(t)-32})},compressToUint8Array:function(e){for(var t=o.compress(e),n=new Uint8Array(2*t.length),r=0,a=t.length;r<a;r++){var s=t.charCodeAt(r);n[2*r]=s>>>8,n[2*r+1]=s%256}return n},decompressFromUint8Array:function(t){if(null==t)return o.decompress(t);for(var n=Array(t.length/2),r=0,a=n.length;r<a;r++)n[r]=256*t[2*r]+t[2*r+1];var s=[];return n.forEach(function(t){s.push(e(t))}),o.decompress(s.join(""))},compressToEncodedURIComponent:function(e){return null==e?"":o._compress(e,6,function(e){return n.charAt(e)})},decompressFromEncodedURIComponent:function(e){return null==e?"":""==e?null:(e=e.replace(/ /g,"+"),o._decompress(e.length,32,function(t){return a(n,e.charAt(t))}))},compress:function(t){return o._compress(t,16,function(t){return e(t)})},_compress:function(e,t,n){if(null==e)return"";var r,a,o,s={},l={},i="",d="",u="",c=2,p=3,E=2,f=[],m=0,h=0;for(o=0;o<e.length;o+=1)if(i=e.charAt(o),Object.prototype.hasOwnProperty.call(s,i)||(s[i]=p++,l[i]=!0),d=u+i,Object.prototype.hasOwnProperty.call(s,d))u=d;else{if(Object.prototype.hasOwnProperty.call(l,u)){if(256>u.charCodeAt(0)){for(r=0;r<E;r++)m<<=1,h==t-1?(h=0,f.push(n(m)),m=0):h++;for(r=0,a=u.charCodeAt(0);r<8;r++)m=m<<1|1&a,h==t-1?(h=0,f.push(n(m)),m=0):h++,a>>=1}else{for(r=0,a=1;r<E;r++)m=m<<1|a,h==t-1?(h=0,f.push(n(m)),m=0):h++,a=0;for(r=0,a=u.charCodeAt(0);r<16;r++)m=m<<1|1&a,h==t-1?(h=0,f.push(n(m)),m=0):h++,a>>=1}0==--c&&(c=Math.pow(2,E),E++),delete l[u]}else for(r=0,a=s[u];r<E;r++)m=m<<1|1&a,h==t-1?(h=0,f.push(n(m)),m=0):h++,a>>=1;0==--c&&(c=Math.pow(2,E),E++),s[d]=p++,u=String(i)}if(""!==u){if(Object.prototype.hasOwnProperty.call(l,u)){if(256>u.charCodeAt(0)){for(r=0;r<E;r++)m<<=1,h==t-1?(h=0,f.push(n(m)),m=0):h++;for(r=0,a=u.charCodeAt(0);r<8;r++)m=m<<1|1&a,h==t-1?(h=0,f.push(n(m)),m=0):h++,a>>=1}else{for(r=0,a=1;r<E;r++)m=m<<1|a,h==t-1?(h=0,f.push(n(m)),m=0):h++,a=0;for(r=0,a=u.charCodeAt(0);r<16;r++)m=m<<1|1&a,h==t-1?(h=0,f.push(n(m)),m=0):h++,a>>=1}0==--c&&(c=Math.pow(2,E),E++),delete l[u]}else for(r=0,a=s[u];r<E;r++)m=m<<1|1&a,h==t-1?(h=0,f.push(n(m)),m=0):h++,a>>=1;0==--c&&(c=Math.pow(2,E),E++)}for(r=0,a=2;r<E;r++)m=m<<1|1&a,h==t-1?(h=0,f.push(n(m)),m=0):h++,a>>=1;for(;;){if(m<<=1,h==t-1){f.push(n(m));break}h++}return f.join("")},decompress:function(e){return null==e?"":""==e?null:o._decompress(e.length,32768,function(t){return e.charCodeAt(t)})},_decompress:function(t,n,r){var a,o,s,l,i,d,u,c=[],p=4,E=4,f=3,m="",h=[],_={val:r(0),position:n,index:1};for(a=0;a<3;a+=1)c[a]=a;for(s=0,i=4,d=1;d!=i;)l=_.val&_.position,_.position>>=1,0==_.position&&(_.position=n,_.val=r(_.index++)),s|=(l>0?1:0)*d,d<<=1;switch(s){case 0:for(s=0,i=256,d=1;d!=i;)l=_.val&_.position,_.position>>=1,0==_.position&&(_.position=n,_.val=r(_.index++)),s|=(l>0?1:0)*d,d<<=1;u=e(s);break;case 1:for(s=0,i=65536,d=1;d!=i;)l=_.val&_.position,_.position>>=1,0==_.position&&(_.position=n,_.val=r(_.index++)),s|=(l>0?1:0)*d,d<<=1;u=e(s);break;case 2:return""}for(c[3]=u,o=u,h.push(u);;){if(_.index>t)return"";for(s=0,i=Math.pow(2,f),d=1;d!=i;)l=_.val&_.position,_.position>>=1,0==_.position&&(_.position=n,_.val=r(_.index++)),s|=(l>0?1:0)*d,d<<=1;switch(u=s){case 0:for(s=0,i=256,d=1;d!=i;)l=_.val&_.position,_.position>>=1,0==_.position&&(_.position=n,_.val=r(_.index++)),s|=(l>0?1:0)*d,d<<=1;c[E++]=e(s),u=E-1,p--;break;case 1:for(s=0,i=65536,d=1;d!=i;)l=_.val&_.position,_.position>>=1,0==_.position&&(_.position=n,_.val=r(_.index++)),s|=(l>0?1:0)*d,d<<=1;c[E++]=e(s),u=E-1,p--;break;case 2:return h.join("")}if(0==p&&(p=Math.pow(2,f),f++),c[u])m=c[u];else{if(u!==E)return null;m=o+o.charAt(0)}h.push(m),c[E++]=o+m.charAt(0),p--,o=m,0==p&&(p=Math.pow(2,f),f++)}}};return o}();void 0!==(r=(function(){return a}).call(t,n,t,e))&&(e.exports=r)},257:function(e,t,n){"use strict";var r,a;e.exports=(null==(r=n.g.process)?void 0:r.env)&&"object"==typeof(null==(a=n.g.process)?void 0:a.env)?n.g.process:n(4227)},4227:function(e){!function(){var t={229:function(e){var t,n,r,a=e.exports={};function o(){throw Error("setTimeout has not been defined")}function s(){throw Error("clearTimeout has not been defined")}function l(e){if(t===setTimeout)return setTimeout(e,0);if((t===o||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(n){try{return t.call(null,e,0)}catch(n){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:o}catch(e){t=o}try{n="function"==typeof clearTimeout?clearTimeout:s}catch(e){n=s}}();var i=[],d=!1,u=-1;function c(){d&&r&&(d=!1,r.length?i=r.concat(i):u=-1,i.length&&p())}function p(){if(!d){var e=l(c);d=!0;for(var t=i.length;t;){for(r=i,i=[];++u<t;)r&&r[u].run();u=-1,t=i.length}r=null,d=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===s||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function E(e,t){this.fun=e,this.array=t}function f(){}a.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];i.push(new E(e,t)),1!==i.length||d||l(p)},E.prototype.run=function(){this.fun.apply(null,this.array)},a.title="browser",a.browser=!0,a.env={},a.argv=[],a.version="",a.versions={},a.on=f,a.addListener=f,a.once=f,a.off=f,a.removeListener=f,a.removeAllListeners=f,a.emit=f,a.prependListener=f,a.prependOnceListener=f,a.listeners=function(e){return[]},a.binding=function(e){throw Error("process.binding is not supported")},a.cwd=function(){return"/"},a.chdir=function(e){throw Error("process.chdir is not supported")},a.umask=function(){return 0}}},n={};function r(e){var a=n[e];if(void 0!==a)return a.exports;var o=n[e]={exports:{}},s=!0;try{t[e](o,o.exports,r),s=!1}finally{s&&delete n[e]}return o.exports}r.ab="//";var a=r(229);e.exports=a}()},8425:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TempoDevtools=void 0;let r=n(3859);t.TempoDevtools={state:{dependencies:{LzString:null},env:{}},init:function(e={}){e&&(this.state.env=Object.assign({},e)),(0,r.initChannelMessaging)()}}},84:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.updateCodebaseIds=t.applyChangeItemToDocument=t.resetIntermediateClassesForSliderInstantUpdate=t.TEMPORARY_STYLING_CLASS_NAME=t.ADD_CLASS_INSTANT_UPDATE_QUEUE=t.ADD_JSX_PREFIX=t.DUPLICATE_PLACEHOLDER_PREFIX=t.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID=void 0;let a=r(n(5943)),o=n(2802),s=n(6710),l=n(6590),i=n(9903),d=n(9538),u=n(7928),c=n(3465);t.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID="tempo-wrap-in-div-placeholder",t.DUPLICATE_PLACEHOLDER_PREFIX="tempo-duplicate-placeholder-",t.ADD_JSX_PREFIX="tempo-add-jsx-placeholder-",t.ADD_CLASS_INSTANT_UPDATE_QUEUE="ADD_CLASS_INSTANT_UPDATE_QUEUE",t.TEMPORARY_STYLING_CLASS_NAME="arb89-temp-styling";let p=e=>{let t=null,n=1/0;return(0,a.default)(`.component-${e}`).each((e,r)=>{(0,a.default)(r).parents().length<n&&(n=(0,a.default)(r).parents().length,t=(0,o.getCodebaseIdFromNode)(r))}),t},E={};t.resetIntermediateClassesForSliderInstantUpdate=()=>{E={}};let f={sm:"@media (width >= 40rem)",md:"@media (width >= 48rem)",lg:"@media (width >= 64rem)",xl:"@media (width >= 80rem)","2xl":"@media (width >= 96rem)"},m=(e,t,n,r)=>{let a=e.replace(/\[/g,"\\[").replace(/\]/g,"\\]").replace(/\(/g,"\\(").replace(/\)/g,"\\)").replace(/\./g,"\\.").replace(/\%/g,"\\%").replace(/\!/g,"\\!").replace(/\//g,"\\/").replace(/#/g,"\\#"),o=t;r&&"default"!==r&&(a=`${r}\\:${a}`,"hover"===r?a=`${a}:hover`:f[r]&&(o=`${f[r]} { ${t} }`)),a=`.${a}`;try{h(a,o,n)}catch(e){console.log("Error adding class CSS rule",e)}},h=(e,t,n)=>{var r=document.createElement("style");if(n){let e=document.getElementById(n);e&&e.remove(),r.id=n}document.head.appendChild(r);var a=r.sheet;a.insertRule?a.insertRule(e+"{"+t+"}",a.cssRules.length):a.addRule&&a.addRule(e,t,a.rules.length)};t.applyChangeItemToDocument=(e,n,r)=>{var u;if(!r||!r.type)return{sendNewNavTree:!1,instantUpdateSuccessful:!1};let f=(0,s.reconstructChangeLedgerClass)(r),h={},g=!1;document.getElementById(o.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS)||m(o.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS,"display: none !important",o.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS);let T=!1;if(f.type===s.ChangeType.ADD_JSX){let e=f.changeFields,n=[];if(e.htmlForInstantUpdate){let r=(0,a.default)(e.htmlForInstantUpdate);r.attr(o.TEMPO_DELETE_AFTER_REFRESH,"true"),r.attr(o.TEMPO_INSTANT_UPDATE,"true"),r.attr(o.TEMPO_OUTLINE_UNTIL_REFESH,"true");let s=`${t.ADD_JSX_PREFIX}${(0,c.v4)()}`;r.attr(o.TEMPO_ELEMENT_ID,s),r.addClass(s),n.push(s),(0,a.default)(`.${e.codebaseIdToAddTo}`).each((t,n)=>{if(e.afterCodebaseId){let t=(0,a.default)(`.${e.afterCodebaseId}`);if(!(null==t?void 0:t.length))return;r.insertAfter(t.first())}else if(e.beforeCodebaseId){let t=(0,a.default)(`.${e.beforeCodebaseId}`);if(!(null==t?void 0:t.length))return;r.insertBefore(t.first())}else(0,a.default)(n).append(r);T=!0,g=!0})}h.newAddedIds=n}else if(f.type===s.ChangeType.MOVE_JSX){let e=[];if((0,a.default)(`.${f.changeFields.codebaseIdToMove}`).length>0)(0,a.default)(`.${f.changeFields.codebaseIdToMove}`).each((t,n)=>{e.push((0,a.default)(n))});else{let t=p(f.changeFields.codebaseIdToMove||"");t&&(0,a.default)(`.${t}`).each((t,n)=>{e.push((0,a.default)(n))})}let t=p(f.changeFields.codebaseIdToMoveTo||"")||f.changeFields.codebaseIdToMoveTo,n=[];e.forEach(e=>{let r=null,a=e.parent();for(;a.length;){if(a.hasClass(t)){r=a;break}let e=a.find(`.${t}`);if(e.length){r=e.first();break}a=a.parent()}if(!r){n.push(null);return}n.push(r)}),e.forEach((e,t)=>{let r;let a=n[t];if(!a.length){console.log("Could not find new parent element for instant update");return}T=!0,g=!0,e.attr(o.TEMPO_INSTANT_UPDATE,"true");let s=!a.is(e.parent());if(s&&((r=e.clone()).attr(o.TEMPO_DELETE_AFTER_REFRESH,"true"),e.addClass(o.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS),e.attr(o.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH,"true")),f.changeFields.afterCodebaseId){let t=p(f.changeFields.afterCodebaseId)||f.changeFields.afterCodebaseId,n=a.children(`.${t}`);if(n.length){s&&r?r.insertAfter(n.first()):e.insertAfter(n.first());return}}if(f.changeFields.beforeCodebaseId){let t=p(f.changeFields.beforeCodebaseId)||f.changeFields.beforeCodebaseId,n=a.children(`.${t}`);if(n.length){s&&r?r.insertBefore(n.first()):e.insertBefore(n.first());return}}s&&r?r.appendTo(a):e.appendTo(a)})}else if(f.type===s.ChangeType.REMOVE_JSX){let e={};f.changeFields.codebaseIdsToRemove.forEach(t=>{let n;if((0,a.default)(`.${t}`).length>0)n=t;else{let e=p(t||"");if(!e)return console.log("Could not find component element for instant update"),!1;n=e}(0,a.default)(`.${n}`).each((t,n)=>{let r=(0,o.getElementKeyFromNode)(n),a=(0,o.getElementKeyFromNode)(n.parentElement);r&&a&&(e[a]||(e[a]=[]),e[a].push({outerHTML:n.outerHTML,elementKeyRemoved:r})),n.classList.add(o.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS),n.setAttribute(o.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH,"true"),T=!0,g=!0})}),h.parentToElementKeysRemoved=e}else if(f.type===s.ChangeType.ADD_CLASS||f.type===s.ChangeType.STYLING){let e,n,r,u,c,E;if(f.type===s.ChangeType.ADD_CLASS)c=f.changeFields.className,e=f.changeFields.className,n=f.changeFields.cssEquivalent,r=f.changeFields.codebaseIdToAddClass,u=f.changeFields.temporaryOnly,E=f.changeFields.modifiers,u&&(e=t.TEMPORARY_STYLING_CLASS_NAME);else{let t=f.changeFields;e="",n=Object.keys(t.stylingChanges).map(e=>t.stylingChanges[e]===l.DELETE_STYLE_CONSTANT?`${(0,i.camelToSnakeCase)(e)}: unset !important;`:`${(0,i.camelToSnakeCase)(e)}: ${t.stylingChanges[e]};`).join(""),r=t.codebaseId,E=t.modifiers}let _=(e||"").replace(/[^A-Za-z0-9_-]/g,"-").replace(/^\d/,"-$&");if(n&&!u){let e=Date.now()-17040672e5;_=`${o.TEMPO_INSTANT_UPDATE_STYLING_PREFIX}${e}-${_}`}if(_){if(u||(0,a.default)(`.${r}`).removeClass(t.TEMPORARY_STYLING_CLASS_NAME),n){if(E&&E.length>0){let e=["hover","required","focus","active","invalid","disabled"],t=E.filter(t=>e.includes(t)),r=t.join(":");if(t.length>0){let e=`${_}:${r}`;m(e,n,e)}else m(_,n,_);let a=E.map(e=>`.tempo-force-${e}`).join(""),o=`${_}${a}`;m(o,n,o)}else m(_,n,_)}let e=(0,d.getMemoryStorageItem)(t.ADD_CLASS_INSTANT_UPDATE_QUEUE)||[];if((0,a.default)(`.${r}`).length>0)(0,a.default)(`.${r}`).addClass(_),g=!0,e.push({codebaseId:r,className:_});else{let t=p(r||"");t&&(0,a.default)(`.${t}`).length>0&&(g=!0,(0,a.default)(`.${t}`).addClass(_),e.push({codebaseId:t,className:_}))}(0,d.setMemoryStorageItem)(t.ADD_CLASS_INSTANT_UPDATE_QUEUE,e),h.addedClass=_,h.codebaseAddedClass=c}}else if(f.type===s.ChangeType.REMOVE_CLASS){let e=f.changeFields;if((0,a.default)(`.${e.codebaseIdToRemoveClass}`).length>0)(0,a.default)(`.${e.codebaseIdToRemoveClass}`).removeClass(e.className),g=!0;else{let t=p(e.codebaseIdToRemoveClass||"");t&&(0,a.default)(`.${t}`).length>0&&(g=!0,(0,a.default)(`.${t}`).removeClass(e.className))}}else if(f.type===s.ChangeType.WRAP_DIV){let e=f.changeFields.codebaseIdsToWrap,n=e[0];(0,a.default)(`.${n}`).each((n,r)=>{let s=e.slice(1),l=(0,a.default)(r).siblings(),i=[r],d=r,u=(0,a.default)(r).index();s.forEach(e=>{let t=l.filter(`.${e}`).get(0);if(t){i.push(t);let e=(0,a.default)(t).index();e<u&&(d=t,u=e)}}),i.length,e.length;let c=document.createElement("div");c.className=t.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID,c.setAttribute(o.TEMPO_INSTANT_UPDATE,"true"),c.setAttribute("tempoelementid",t.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID),c.setAttribute("data-testid",t.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID),c.setAttribute(o.TEMPO_DELETE_AFTER_REFRESH,"true"),i.forEach(e=>{c.appendChild(e.cloneNode(!0)),e.classList.add(o.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS),e.setAttribute(o.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH,"true")}),d.insertAdjacentElement("beforebegin",c),T=!0,g=!0})}else if(f.type===s.ChangeType.DUPLICATE)f.changeFields.codebaseIdsToDuplicate.forEach(e=>{(0,a.default)(`.${e}`).each((n,r)=>{let a=r.cloneNode(!0);a.setAttribute(o.TEMPO_DELETE_AFTER_REFRESH,"true"),a.setAttribute(o.TEMPO_INSTANT_UPDATE,"true"),a.setAttribute("tempoelementid",`${t.DUPLICATE_PLACEHOLDER_PREFIX}${e}`),a.setAttribute("data-testid",`${t.DUPLICATE_PLACEHOLDER_PREFIX}${e}`),a.classList.add(t.DUPLICATE_PLACEHOLDER_PREFIX+e),a.classList.remove(e);let s=Array.from(a.children);for(;s.length;){let e=s.pop();if(!e)continue;let n=e.getAttribute("tempoelementid")||e.getAttribute("data-testid");n&&(e.setAttribute("tempoelementid",`${t.DUPLICATE_PLACEHOLDER_PREFIX}${n}`),e.setAttribute("data-testid",`${t.DUPLICATE_PLACEHOLDER_PREFIX}${n}`),e.classList.remove(n),e.classList.add(t.DUPLICATE_PLACEHOLDER_PREFIX+n),e.setAttribute(o.TEMPO_INSTANT_UPDATE,"true"),s.push(...Array.from(e.children)))}r.insertAdjacentElement("afterend",a),T=!0,g=!0})});else if(f.type===s.ChangeType.CHANGE_TAG){let e=f.changeFields;(0,a.default)(`.${e.codebaseIdToChange}`).each((t,n)=>{let r=(0,a.default)("<"+e.newTagName+"></"+e.newTagName+">");r.attr(o.TEMPO_INSTANT_UPDATE,"true"),r.attr(o.TEMPO_DELETE_AFTER_REFRESH,"true");let s=(0,a.default)(n);a.default.each(s[0].attributes,function(){r.attr(this.name,this.value)}),s.contents().clone(!0,!0).appendTo(r),s.before(r),s.addClass(o.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS),s.attr(o.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH,"true"),T=!0,g=!0})}else if(f.type===s.ChangeType.UNDO){let{sendNewNavTree:t,instantUpdateSuccessful:n}=_(e,f);T=t,g=n}else if(f.type===s.ChangeType.REDO){let r=f.changeFields.changeToRedo;if(s.CHANGE_TYPES_WITH_INSTANT_UNDO.includes(r.type)){let{sendNewNavTree:a,instantUpdateSuccessful:o}=(0,t.applyChangeItemToDocument)(e,n,r);T=a,g=o,r.prevIdToNewIdMap&&(T=(0,t.updateCodebaseIds)(e,r.prevIdToNewIdMap,!0)||T)}}else if(f.type===s.ChangeType.UPDATE_CLASSES){let{codebaseIdToUpdateClass:e,newClasses:t,oldClasses:n,involvedClasses:r,isInstantChangeOnly:o}=f.changeFields;for(let t of n)(0,a.default)(`.${e}`).removeClass(t);for(let n of((E[e]||[]).forEach(t=>{(0,a.default)(`.${e}`).removeClass(t)}),t))(0,a.default)(`.${e}`).addClass(n);for(let e of r)m(e.tailwind,e.css,void 0,e.variant);if(o)for(let t of(E[e]=[],r)){let n=t.variant?`${t.variant}:${t.tailwind}`:t.tailwind;E[e].push(n)}else E[e]=[]}let y=f.getElementKeyToSelectAfterInstantUpdate(),S=f.getElementKeysToMultiselectAfterInstantUpdate();return f.type===s.ChangeType.UNDO&&(y=f.changeFields.changeToUndo.getElementKeyToSelectAfterUndoInstantUpdate(),S=f.changeFields.changeToUndo.getElementKeysToMultiselectAfterUndoInstantUpdate()),void 0!==y&&((0,d.setMemoryStorageItem)(d.SELECTED_ELEMENT_KEY,y),e.postMessage({id:l.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY,elementKey:y,outerHTML:null===(u=(0,o.getNodeForElementKey)(y))||void 0===u?void 0:u.outerHTML})),void 0!==S&&((0,d.setMemoryStorageItem)(d.MULTI_SELECTED_ELEMENT_KEYS,S),e.postMessage({id:l.FIXED_IFRAME_MESSAGE_IDS.MULTI_SELECTED_ELEMENT_KEYS,elementKeys:S,outerHTMLs:null==S?void 0:S.map(e=>{var t;return null===(t=(0,o.getNodeForElementKey)(e))||void 0===t?void 0:t.outerHTML})})),g&&(0,a.default)(`*[${o.TEMPO_DELETE_AFTER_INSTANT_UPDATE}=true]`).remove(),e.postMessage({id:l.FIXED_IFRAME_MESSAGE_IDS.INSTANT_UPDATE_DONE,changeItem:r,instantUpdateData:h,instantUpdateSuccessful:g}),{sendNewNavTree:T,instantUpdateSuccessful:g}};let _=(e,n)=>{let r=n.changeFields,l=r.changeToUndo;if(!s.CHANGE_TYPES_WITH_INSTANT_UNDO.includes(l.type))return{sendNewNavTree:!1,instantUpdateSuccessful:!1};let i=!1,d=!1;if(l.prevIdToNewIdMap){let n={};Object.keys(l.prevIdToNewIdMap).forEach(e=>{n[l.prevIdToNewIdMap[e]]=e});let r=void 0!==l.getElementKeyToSelectAfterUndoInstantUpdate();i=(0,t.updateCodebaseIds)(e,n,!r)}if(l.type===s.ChangeType.REMOVE_JSX){let e=l.changeFields.codebaseIdsToRemove;r.matchingActivityFlushed?Object.entries(l.getInstantUpdateData().parentToElementKeysRemoved||{}).forEach(([e,t])=>{let n=Object.values(t).sort((e,t)=>{let n=u.TempoElement.fromKey(e.elementKeyRemoved),r=u.TempoElement.fromKey(t.elementKeyRemoved);return n.uniquePath.localeCompare(r.uniquePath)}),r=(0,o.getNodeForElementKey)(e);r&&n.forEach(e=>{let{elementKeyRemoved:t,outerHTML:n}=e,s=Number(u.TempoElement.fromKey(t).uniquePath.split("-").pop()),l=(0,a.default)(n).get(0);l&&(l.setAttribute(o.TEMPO_DELETE_AFTER_REFRESH,"true"),l.setAttribute(o.TEMPO_INSTANT_UPDATE,"true"),r.insertBefore(l,r.children[s]||null),d=!0,i=!0)})}):e.forEach(e=>{(0,a.default)(`.${e}`).each((e,t)=>{t.classList.remove(o.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS),t.removeAttribute(o.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH),i=!0,d=!0})})}else if(l.type===s.ChangeType.ADD_CLASS||l.type===s.ChangeType.STYLING){let e=l.getInstantUpdateData(),t=l.changeFields,n=null==e?void 0:e.addedClass;n&&(0,a.default)(`.${t.codebaseIdToAddClass}`).each((e,t)=>{(0,a.default)(t).hasClass(n)&&((0,a.default)(t).removeClass(n),d=!0)});let r=null==e?void 0:e.codebaseAddedClass;r&&(0,a.default)(`.${t.codebaseIdToAddClass}`).each((e,t)=>{(0,a.default)(t).hasClass(r)&&((0,a.default)(t).removeClass(r),d=!0)})}else if(l.type===s.ChangeType.UPDATE_CLASSES){let{codebaseIdToUpdateClass:e,newClasses:t,oldClasses:n}=l.changeFields;for(let n of t)(0,a.default)(`.${e}`).removeClass(n);for(let t of n)(0,a.default)(`.${e}`).addClass(t);E[e]=[]}else if(l.type===s.ChangeType.ADD_JSX){let e=l.getInstantUpdateData(),t=null==e?void 0:e.addedIds;null==t||t.forEach(e=>{(0,a.default)(`.${e}`).remove(),d=!0}),i=!0}return{sendNewNavTree:i,instantUpdateSuccessful:d}};t.updateCodebaseIds=(e,t,n)=>{let r=[];if(Object.entries(t).forEach(([e,t])=>{(0,a.default)(`.${e}`).each((n,a)=>{r.push({item:a,prevCodebaseId:e,newCodebaseId:t})})}),r.forEach(e=>{let t=(0,a.default)(e.item),n=(t.attr("class")||"").replace(RegExp(`${e.prevCodebaseId}`,"g"),e.newCodebaseId);t.attr("class",n),e.item.setAttribute("tempoelementid",e.newCodebaseId),e.item.setAttribute("data-testid",e.newCodebaseId)}),!n)return!!r.length;[{key:d.SELECTED_ELEMENT_KEY,messageId:l.FIXED_IFRAME_MESSAGE_IDS.SELECTED_ELEMENT_KEY},{key:d.HOVERED_ELEMENT_KEY,messageId:l.FIXED_IFRAME_MESSAGE_IDS.HOVERED_ELEMENT_KEY}].forEach(({key:n,messageId:r})=>{var a;let s=(0,d.getMemoryStorageItem)(n),l=u.TempoElement.fromKey(s);if(t[l.codebaseId]){let s=new u.TempoElement(t[l.codebaseId],l.storyboardId,l.uniquePath);(0,d.setMemoryStorageItem)(n,s.getKey()),e.postMessage({id:r,elementKey:s.getKey(),outerHTML:null===(a=(0,o.getNodeForElementKey)(s.getKey()))||void 0===a?void 0:a.outerHTML})}});let s=(0,d.getMemoryStorageItem)(d.MULTI_SELECTED_ELEMENT_KEYS);if(null==s?void 0:s.length){let n=[];s.forEach(e=>{let r=u.TempoElement.fromKey(e);if(t[r.codebaseId]){let e=new u.TempoElement(t[r.codebaseId],r.storyboardId,r.uniquePath);n.push(e.getKey())}else n.push(e)}),(0,d.setMemoryStorageItem)(d.MULTI_SELECTED_ELEMENT_KEYS,n),e.postMessage({id:l.FIXED_IFRAME_MESSAGE_IDS.MULTI_SELECTED_ELEMENT_KEYS,elementKeys:n,outerHTMLs:null==n?void 0:n.map(e=>{var t;return null===(t=(0,o.getNodeForElementKey)(e))||void 0===t?void 0:t.outerHTML})})}return!!r.length}},6710:function(e,t,n){"use strict";var r,a,o,s;Object.defineProperty(t,"__esModule",{value:!0}),t.reconstructChangeLedgerClass=t.UnknownChange=t.UpdateClassesChange=t.RedoChange=t.UndoChange=t.EditTextChange=t.RemoveClassChange=t.AddClassChange=t.ChangeTagChange=t.DuplicateChange=t.WrapDivChange=t.ChangePropChange=t.RemoveJsxChange=t.MoveJsxChange=t.AddJsxChange=t.StylingChange=t.ChangeLedgerItem=t.CHANGE_TYPES_WITH_INSTANT_UNDO=t.ChangeType=t.StylingFramework=t.DUPLICATE_PLACEHOLDER_PREFIX=t.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID=void 0;let l=n(7928),i=n(3465);t.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID="tempo-wrap-in-div-placeholder",t.DUPLICATE_PLACEHOLDER_PREFIX="tempo-duplicate-placeholder-",(o=r||(t.StylingFramework=r={})).INLINE="Inline",o.CSS="CSS",o.TAILWIND="Tailwind",(s=a||(t.ChangeType=a={})).STYLING="STYLING",s.ADD_JSX="ADD_JSX",s.MOVE_JSX="MOVE_JSX",s.REMOVE_JSX="REMOVE_JSX",s.CHANGE_PROP="CHANGE_PROP",s.ADD_CLASS="ADD_CLASS",s.REMOVE_CLASS="REMOVE_CLASS",s.UPDATE_CLASSES="UPDATE_CLASSES",s.EDIT_TEXT="EDIT_TEXT",s.WRAP_DIV="WRAP_DIV",s.CHANGE_TAG="CHANGE_TAG",s.DUPLICATE="DUPLICATE",s.UNDO="UNDO",s.REDO="REDO",s.UNKNOWN="UNKNOWN",t.CHANGE_TYPES_WITH_INSTANT_UNDO=[a.REMOVE_JSX,a.ADD_CLASS,a.STYLING,a.UPDATE_CLASSES];class d{constructor(e,t,n,r){this.prevIdToNewIdMap={},this.id=r||(0,i.v4)(),this.type=e,this.changeFields=n,this.changeName=t,this._consumed=!1,this._failed=!1,this._instantUpdateSent=!1,this._instantUpdateFinished=!1,this._instantUpdateSuccessful=!1,this._sendInstantUpdate=!0,this.canInstantUpdateWhileFlushing=!1,this._apiPromise=new Promise((e,t)=>{this._resolveApi=e,this._rejectApi=t})}resolveApi(e){var t;null===(t=this._resolveApi)||void 0===t||t.call(this,e)}rejectApi(e){var t;this._apiRejectionAdded&&(null===(t=this._rejectApi)||void 0===t||t.call(this,e))}needsToSendInstantUpdate(){return!this._instantUpdateSent&&this._sendInstantUpdate}markInstantUpdateSent(){this._instantUpdateSent=!0}getInstantUpdateSent(){return this._instantUpdateSent}markInstantUpdateFinished(e,t){this._instantUpdateFinished=!0,this._instantUpdateSuccessful=t,this._instantUpdateData=e}getInstantUpdateData(){return this._instantUpdateData}wasInstantUpdateSuccessful(){return this._instantUpdateSuccessful}isInstantUpdateFinished(){return this._instantUpdateFinished}markProcessedSucceeded(){this._consumed=!0}markProcessedFailed(){this._failed=!0,this._consumed=!0}isFailed(){return this._failed}needToProcessChange(){return!this._consumed}onApiResolve(e){return this._apiPromise.then(e)}onApiReject(e){return this._apiRejectionAdded=!0,this._apiPromise.catch(e)}doNotSendInstantUpdate(){this._sendInstantUpdate=!1}clearSelectedElementsAfterInstantUpdate(){this.elementKeyToSelectAfterInstantUpdate=null,this.elementKeysToMultiselectAfterInstantUpdate=null}setSelectedElementsAfterInstantUpdate(e,t){this.elementKeyToSelectAfterInstantUpdate=e,this.elementKeysToMultiselectAfterInstantUpdate=t}clearSelectedElementsAfterUndoInstantUpdate(){this.elementKeyToSelectAfterUndoInstantUpdate=null,this.elementKeysToMultiselectAfterUndoInstantUpdate=null}setSelectedElementsAfterUndoInstantUpdate(e,t){this.elementKeyToSelectAfterUndoInstantUpdate=e,this.elementKeysToMultiselectAfterUndoInstantUpdate=t}getElementKeyToSelectAfterInstantUpdate(){return this.elementKeyToSelectAfterInstantUpdate}getElementKeysToMultiselectAfterInstantUpdate(){return this.elementKeysToMultiselectAfterInstantUpdate}getElementKeyToSelectAfterUndoInstantUpdate(){return this.elementKeyToSelectAfterUndoInstantUpdate}getElementKeysToMultiselectAfterUndoInstantUpdate(){return this.elementKeysToMultiselectAfterUndoInstantUpdate}applyAllCodebaseIdChanges(e){var t,n;let r=t=>{if(!t)return null;let n=l.TempoElement.fromKey(t),r=e[n.codebaseId];return r?new l.TempoElement(r,n.storyboardId,n.uniquePath).getKey():null};if(this.elementKeyToSelectAfterInstantUpdate){let e=r(this.elementKeyToSelectAfterInstantUpdate);this.elementKeyToSelectAfterInstantUpdate=e||this.elementKeyToSelectAfterInstantUpdate}if(this.elementKeysToMultiselectAfterInstantUpdate&&(this.elementKeysToMultiselectAfterInstantUpdate=null===(t=this.elementKeysToMultiselectAfterInstantUpdate)||void 0===t?void 0:t.map(e=>r(e)||e)),this.elementKeyToSelectAfterUndoInstantUpdate){let e=r(this.elementKeyToSelectAfterUndoInstantUpdate);this.elementKeyToSelectAfterUndoInstantUpdate=e||this.elementKeyToSelectAfterUndoInstantUpdate}this.elementKeysToMultiselectAfterUndoInstantUpdate&&(this.elementKeysToMultiselectAfterUndoInstantUpdate=null===(n=this.elementKeysToMultiselectAfterUndoInstantUpdate)||void 0===n?void 0:n.map(e=>r(e)||e)),this.applyCodebaseIdChanges(e)}}t.ChangeLedgerItem=d;class u extends d{constructor(e,t){super(a.STYLING,"Styling",e,t),this.canInstantUpdateWhileFlushing=!0}prepareApiRequest(e,t){let{codebaseId:n,stylingChanges:r,stylingFramework:a,modifiers:o,customProperties:s}=this.changeFields;return{urlPath:`canvases/${e}/parseAndMutate/mutate/styling`,body:{reactElement:t[n],styling:r,stylingFramework:a,modifiers:o,customProperties:s}}}applyCodebaseIdChanges(e){let t=e[this.changeFields.codebaseId];t&&(this.changeFields.codebaseId=t)}}t.StylingChange=u;class c extends d{constructor(e,t){super(a.ADD_JSX,"Add Element",e,t)}prepareApiRequest(e,t){let{codebaseIdToAddTo:n,beforeCodebaseId:r,afterCodebaseId:a,addCodebaseId:o,addNativeTag:s,fileContentsToSourceFrom:l,fileContentsSourceFilename:i,propsToSet:d,deletedStoryboardId:u,htmlForInstantUpdate:c,extraPathToContents:p}=this.changeFields,E={destinationElement:t[n],beforeElement:t[r||""],afterElement:t[a||""],newElement:{},canvasId:e,deletedStoryboardId:u,fileContentsToSourceFrom:l,fileContentsSourceFilename:i,extraFiles:p};o?E.newElement=Object.assign({},t[o]):s&&(E.newElement.type="native",E.newElement.nativeTag=s,E.newElement.componentName=s),d&&(E.newElement.propsToSet=d),Object.keys(E.newElement).length||delete E.newElement;let f=!!c;return E.hasInstantUpdate=f,{urlPath:`canvases/${e}/parseAndMutate/mutate/addJsxElement`,body:E,successToastMessage:f?void 0:"Successfully added"}}applyCodebaseIdChanges(e){["codebaseIdToAddTo","beforeCodebaseId","afterCodebaseId","addCodebaseId"].forEach(t=>{let n=e[this.changeFields[t]];n&&(this.changeFields[t]=n)})}}t.AddJsxChange=c;class p extends d{constructor(e,t){super(a.MOVE_JSX,"Move Element",e,t)}prepareApiRequest(e,t){let{codebaseIdToMoveTo:n,codebaseIdToMove:r,afterCodebaseId:a,beforeCodebaseId:o,expectedCurrentParentCodebaseId:s}=this.changeFields;return{urlPath:`canvases/${e}/parseAndMutate/mutate/moveJsxElement`,body:{elementToMove:t[r],newContainerElement:t[n],afterElement:t[a||""],beforeElement:t[o||""],expectedCurrentParent:t[s||""]}}}applyCodebaseIdChanges(e){["codebaseIdToMoveTo","codebaseIdToMove","afterCodebaseId","beforeCodebaseId","expectedCurrentParentCodebaseId"].forEach(t=>{let n=e[this.changeFields[t]];n&&(this.changeFields[t]=n)})}}t.MoveJsxChange=p;class E extends d{constructor(e,t){e.codebaseIdsToRemove=Array.from(new Set(e.codebaseIdsToRemove)),super(a.REMOVE_JSX,"Delete Element",e,t)}prepareApiRequest(e,t){let{codebaseIdsToRemove:n}=this.changeFields;return{urlPath:`canvases/${e}/parseAndMutate/mutate/removeJsxElement`,body:{elementsToRemove:n.map(e=>t[e]).filter(e=>e)}}}applyCodebaseIdChanges(e){this.changeFields.codebaseIdsToRemove=this.changeFields.codebaseIdsToRemove.map(t=>e[t]||t)}}t.RemoveJsxChange=E;class f extends d{constructor(e,t){super(a.CHANGE_PROP,"Change Prop",e,t)}prepareApiRequest(e,t){let{codebaseIdToChange:n,propName:r,propValue:a}=this.changeFields;return{urlPath:`canvases/${e}/parseAndMutate/mutate/changePropValue`,body:{elementToModify:t[n],propName:r,propValue:a},successToastMessage:"Prop changed"}}applyCodebaseIdChanges(e){let t=e[this.changeFields.codebaseIdToChange];t&&(this.changeFields.codebaseIdToChange=t)}}t.ChangePropChange=f;class m extends d{constructor(e,t){e.codebaseIdsToWrap=Array.from(new Set(e.codebaseIdsToWrap)),super(a.WRAP_DIV,"Wrap In Div",e,t)}prepareApiRequest(e,t){let{codebaseIdsToWrap:n}=this.changeFields;return{urlPath:`canvases/${e}/parseAndMutate/mutate/wrapInDiv`,body:{reactElements:n.map(e=>t[e])}}}applyCodebaseIdChanges(e){this.changeFields.codebaseIdsToWrap=this.changeFields.codebaseIdsToWrap.map(t=>e[t]||t)}}t.WrapDivChange=m;class h extends d{constructor(e,t){e.codebaseIdsToDuplicate=Array.from(new Set(e.codebaseIdsToDuplicate)),super(a.DUPLICATE,"Duplicate",e,t)}prepareApiRequest(e,t){let{codebaseIdsToDuplicate:n}=this.changeFields;return{urlPath:`canvases/${e}/parseAndMutate/mutate/duplicate`,body:{reactElements:n.map(e=>t[e])}}}applyCodebaseIdChanges(e){this.changeFields.codebaseIdsToDuplicate=this.changeFields.codebaseIdsToDuplicate.map(t=>e[t]||t)}}t.DuplicateChange=h;class _ extends d{constructor(e,t){super(a.CHANGE_TAG,"Change Tag Name",e,t)}prepareApiRequest(e,t){let{codebaseIdToChange:n,newTagName:r}=this.changeFields;return{urlPath:`canvases/${e}/parseAndMutate/mutate/changeElementTag`,body:{elementToModify:t[n],newTag:r}}}applyCodebaseIdChanges(e){let t=e[this.changeFields.codebaseIdToChange];t&&(this.changeFields.codebaseIdToChange=t)}}t.ChangeTagChange=_;class g extends d{constructor(e,t){super(a.ADD_CLASS,"Add Class",e,t),this.canInstantUpdateWhileFlushing=!0}prepareApiRequest(e,t){let{codebaseIdToAddClass:n,className:a,addingTailwindClass:o,modifiers:s,customProperties:l}=this.changeFields;return{urlPath:`canvases/${e}/parseAndMutate/mutate/addClass`,body:{reactElement:t[n],className:a,stylingFramework:o?r.TAILWIND:null,modifiers:s,customProperties:l}}}applyCodebaseIdChanges(e){let t=e[this.changeFields.codebaseIdToAddClass];t&&(this.changeFields.codebaseIdToAddClass=t)}}t.AddClassChange=g;class T extends d{constructor(e,t){super(a.REMOVE_CLASS,"Remove Class",e,t)}prepareApiRequest(e,t){let{codebaseIdToRemoveClass:n,className:r}=this.changeFields;return{urlPath:`canvases/${e}/parseAndMutate/mutate/removeClass`,body:{reactElement:t[n],className:r}}}applyCodebaseIdChanges(e){let t=e[this.changeFields.codebaseIdToRemoveClass];t&&(this.changeFields.codebaseIdToRemoveClass=t)}}t.RemoveClassChange=T;class y extends d{constructor(e,t){super(a.EDIT_TEXT,"Edit Text",e,t)}prepareApiRequest(e,t){let{codebaseIdToEditText:n,newText:r,oldText:a}=this.changeFields;return{urlPath:`canvases/${e}/parseAndMutate/mutate/editText`,body:{element:t[n],newText:r,oldText:a}}}applyCodebaseIdChanges(e){let t=e[this.changeFields.codebaseIdToEditText];t&&(this.changeFields.codebaseIdToEditText=t)}}t.EditTextChange=y;class S extends d{constructor(e,t){var n;super(a.UNDO,"Undo",e,t),(null===(n=e.changeToUndo)||void 0===n?void 0:n.canInstantUpdateWhileFlushing)&&(this.canInstantUpdateWhileFlushing=!0)}prepareApiRequest(e,t){return{urlPath:`canvases/${e}/parseAndMutate/activities/undoChangeToFiles`,body:{}}}applyCodebaseIdChanges(e){}}t.UndoChange=S;class v extends d{constructor(e,t){var n;super(a.REDO,"Redo",e,t),(null===(n=e.changeToRedo)||void 0===n?void 0:n.canInstantUpdateWhileFlushing)&&(this.canInstantUpdateWhileFlushing=!0)}prepareApiRequest(e,t){return{urlPath:`canvases/${e}/parseAndMutate/activities/redoChangeToFiles`,body:{}}}applyCodebaseIdChanges(e){}}t.RedoChange=v;class I extends d{constructor(e,t){super(a.UPDATE_CLASSES,"Update Classes",e,t),this.canInstantUpdateWhileFlushing=!0}prepareApiRequest(e,t){let{codebaseIdToUpdateClass:n,newClasses:r,oldClasses:a}=this.changeFields;return{urlPath:`canvases/${e}/parseAndMutate/mutate/updateClasses`,body:{reactElement:t[n],newClasses:r,oldClasses:a}}}applyCodebaseIdChanges(e){let t=e[this.changeFields.codebaseIdToUpdateClass];t&&(this.changeFields.codebaseIdToUpdateClass=t)}}t.UpdateClassesChange=I;class A extends d{constructor(e,t){super(a.UNKNOWN,"",e,t),this.markProcessedSucceeded(),this.doNotSendInstantUpdate()}prepareApiRequest(e,t){throw Error("Unsupported operation")}applyCodebaseIdChanges(e){}}t.UnknownChange=A,t.reconstructChangeLedgerClass=e=>{if(!e||!e.type)return null;let n=e.type,r=e.changeFields,o=e.id,s=(()=>{switch(n){case a.STYLING:return new u(r,o);case a.ADD_JSX:return new c(r,o);case a.REMOVE_JSX:return new E(r,o);case a.MOVE_JSX:return new p(r,o);case a.CHANGE_PROP:return new f(r,o);case a.ADD_CLASS:return new g(r,o);case a.REMOVE_CLASS:return new T(r,o);case a.UPDATE_CLASSES:return new I(r,o);case a.WRAP_DIV:return new m(r,o);case a.CHANGE_TAG:return new _(r,o);case a.DUPLICATE:return new h(r,o);case a.EDIT_TEXT:return new y(r,o);case a.UNDO:return r.changeToUndo=(0,t.reconstructChangeLedgerClass)(r.changeToUndo),new S(r,o);case a.REDO:return r.changeToRedo=(0,t.reconstructChangeLedgerClass)(r.changeToRedo),new v(r,o);case a.UNKNOWN:return new A(r,o);default:throw Error(`Unknown change type: ${n}`)}})();return Object.keys(e).forEach(t=>{["type","changeFields","id"].includes(t)||(s[t]=e[t])}),s}},6590:function(e,t){"use strict";var n,r,a,o;Object.defineProperty(t,"__esModule",{value:!0}),t.STORYBOARD_HYDRATION_STATUS=t.SELECT_OR_HOVER_STORYBOARD=t.DELETE_STYLE_CONSTANT=t.FIXED_IFRAME_MESSAGE_IDS=t.INHERITABLE_CSS_PROPS=t.CSS_VALUES_TO_COLLECT_FOR_PARENT=t.CSS_VALUES_TO_COLLECT=void 0,t.CSS_VALUES_TO_COLLECT=new Set(["display","flex-direction","flex-grow","flex-shrink","font-family","align-items","justify-content","column-gap","row-gap","flex-wrap","align-content","overflow","text-align","width","max-width","min-width","height","max-height","min-height","font-size","line-height","padding","padding-top","padding-left","padding-right","padding-bottom","margin","margin-top","margin-left","margin-right","margin-bottom","border-radius","font-family","font-weight","object-fit","background-clip","border-left-style","border-top-style","border-right-style","border-bottom-style","border-left-width","border-top-width","border-right-width","border-bottom-width","border-left-color","border-top-color","border-right-color","border-bottom-color","background-color","color","transform","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","letter-spacing","opacity","font-style","text-decoration-line","top","left","right","bottom","position","background-image"]),t.CSS_VALUES_TO_COLLECT_FOR_PARENT=new Set(["display","flex-direction"]),t.INHERITABLE_CSS_PROPS={azimuth:!0,"border-collapse":!0,"border-spacing":!0,"caption-side":!0,color:!0,cursor:!0,direction:!0,"empty-cells":!0,"font-family":!0,"font-size":!0,"font-style":!0,"font-variant":!0,"font-weight":!0,font:!0,"letter-spacing":!0,"line-height":!0,"list-style-image":!0,"list-style-position":!0,"list-style-type":!0,"list-style":!0,orphans:!0,quotes:!0,"text-align":!0,"text-indent":!0,"text-transform":!0,visibility:!0,"white-space":!0,widows:!0,"word-spacing":!0},(a=n||(t.FIXED_IFRAME_MESSAGE_IDS=n={})).HOVERED_ELEMENT_KEY="HOVERED_ELEMENT_KEY",a.SELECTED_ELEMENT_KEY="SELECTED_ELEMENT_KEY",a.MULTI_SELECTED_ELEMENT_KEYS="MULTI_SELECTED_ELEMENT_KEYS",a.CONTEXT_REQUESTED="CONTEXT_REQUESTED",a.WHEEL_EVENT="WHEEL_EVENT",a.NAV_TREE="NAV_TREE",a.PROCESSED_CSS_RULES_FOR_ELEMENT="PROCESSED_CSS_RULES_FOR_ELEMENT",a.CSS_EVALS_FOR_ELEMENT="CSS_EVALS_FOR_ELEMENT",a.ELEMENT_CLASS_LIST="ELEMENT_CLASS_LIST",a.KEY_DOWN_EVENT="KEY_DOWN_EVENT",a.KEY_UP_EVENT="KEY_UP_EVENT",a.MOUSE_MOVE_EVENT="MOUSE_MOVE_EVENT",a.DRAG_START_EVENT="DRAG_START_EVENT",a.DRAG_END_EVENT="DRAG_END_EVENT",a.DRAG_CANCEL_EVENT="DRAG_CANCEL_EVENT",a.LATEST_HREF="LATEST_HREF",a.LATEST_HYDRATION_ERROR_STATUS="LATEST_HYDRATION_ERROR_STATUS",a.START_EDITING_TEXT="START_EDITING_TEXT",a.EDITED_TEXT="EDITED_TEXT",a.INSTANT_UPDATE_DONE="INSTANT_UPDATE_DONE",a.EDIT_DYNAMIC_TEXT="EDIT_DYNAMIC_TEXT",t.DELETE_STYLE_CONSTANT=null,t.SELECT_OR_HOVER_STORYBOARD="SELECT_OR_HOVER_STORYBOARD",(o=r||(t.STORYBOARD_HYDRATION_STATUS=r={})).OTHER_ERROR="other_error",o.ERROR="error",o.NO_ERROR="no_error"},7321:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ruleMatchesElement=t.getElementClassList=t.getCssEvals=t.cssEval=t.processRulesForSelectedElement=t.setModifiersForSelectedElement=t.parse=void 0;let a=r(n(5943)),o=n(2802),s=n(9903),l=n(6590),i=n(3465),d=n(8731),u=n(7928),c=n(4203),p=n(9538),E=n(6227);t.parse=(0,c.createParser)({syntax:{baseSyntax:"latest",pseudoClasses:{unknown:"accept",definitions:{Selector:["has"]}},pseudoElements:{unknown:"accept"},combinators:[">","+","~"],attributes:{operators:["^=","$=","*=","~="]},classNames:!0,namespace:{wildcard:!0},tag:{wildcard:!0}},substitutes:!0});let f=(e,t,n,r)=>{try{e.insertRule?e.insertRule(`${t} { ${n} }`,r):e.addRule(t,n,r)}catch(e){console.log("Error adding rule: ",e)}},m=e=>{let n=[];if(e instanceof CSSMediaRule){for(let r of e.cssRules)if(e.media.mediaText.includes("min-width")&&r instanceof CSSStyleRule){let e=(0,t.parse)(r.selectorText);if("Selector"!==e.type)continue;let a=e.rules[0].items.filter(e=>"ClassName"===e.type).map(e=>e.name);if(1!==a.length)continue;n.push({class:a[0],pseudos:h(a[0]),cssText:r.style.cssText,style:r.style})}}return n},h=e=>[...new Set((e.match(/(?:\b|(?<=[:.]))(sm|md|lg|xl|2xl)\\?:[\w-]+/g)||[]).map(e=>{let t=e.indexOf(e.includes("\\:")?"\\:":":");return e.substring(0,t)}))],_=e=>{if("Selector"!==e.type)return;let t=e.rules[0],n=t.items.filter(e=>"ClassName"===e.type).map(e=>e.name);if(0===n.length||"dark"!==n[0])return;let r=t.nestedRule;if(!r)return;let a=[],o=r.items.filter(e=>"ClassName"===e.type).map(e=>e.name);if(o.length>1){console.log("Skipping is selector with multiple classes",t);return}return a.push({class:o[0],pseudos:["dark",...r.items.filter(e=>"PseudoClass"===e.type).map(e=>e.name)]}),a};t.setModifiersForSelectedElement=(e,t,n)=>{document.querySelectorAll('[class*="tempo-force-"]').forEach(e=>{Array.from(e.classList).forEach(t=>{t.startsWith("tempo-force-")&&e.classList.remove(t)})});let r=u.TempoElement.fromKey(n);if(r.isEmpty())return;let a=(0,o.getNodeForElementKey)(r.getKey());a&&t.forEach(e=>{a.classList.add("tempo-force-"+e)})},t.processRulesForSelectedElement=(e,n,r)=>{var a,c,h,g,T;if(!n)return;let y=u.TempoElement.fromKey(r);if(y.isEmpty())return;let S=(0,o.getNodeForElementKey)(y.getKey()),v=(0,p.getMemoryStorageItem)(p.MULTI_SELECTED_ELEMENT_KEYS)||[];if(!S){(0,E.addNavTreeBuiltCallback)({callbackFn:()=>{(0,t.processRulesForSelectedElement)(e,n,r)},state:{selectedElementKey:r,multiSelectedElementKeys:v}});return}let I=[],A=new Set,N=new Set,C={filename:"",selector:"element.style",source:{},styles:{},applied:!0,codebaseId:"element.style",removable:!1,allowChanges:!0};for(let e=0;e<(null===(a=null==S?void 0:S.style)||void 0===a?void 0:a.length);e++){let t=S.style[e];C.styles[t]=S.style[t]}I.push(C);let O=!1,b=[],R=[];Object.keys(n).forEach(e=>{var t;let r=n[e];if(N.add(r.selector),!(0,s.isCssSelectorValid)(r.selector))return;if((0,s.getAllClassesFromSelector)(r.selector).forEach(e=>{A.add(e)}),(0,s.isCssSelectorValid)(r.selector)&&(null==S?void 0:S.matches(r.selector))){b.push(Object.assign(Object.assign({},r),{applied:!0,allowChanges:!0,removable:(0,s.canRemoveCssClassFromElement)(r.selector,S)}));return}let a=0,o=null==S?void 0:S.parentElement,i={};for(;o;){if(!O){let e={};for(let n=0;n<(null===(t=null==o?void 0:o.style)||void 0===t?void 0:t.length);n++){let t=o.style[n];l.INHERITABLE_CSS_PROPS[t]&&(e[t]=o.style[t])}0!==Object.keys(e).length&&R.push({filename:"",selector:`parentElement${a}.style`,inherited:!0,source:{},styles:e,applied:!0,codebaseId:`parentElement${a}.style`,removable:!1,allowChanges:!1})}if((0,s.isCssSelectorValid)(r.selector)&&!(null==o?void 0:o.matches(r.selector))){o=o.parentElement;continue}Object.keys((null==r?void 0:r.styles)||{}).forEach(e=>{l.INHERITABLE_CSS_PROPS[e]&&null!==i[e]&&(i[e]=r.styles[e])}),o=o.parentElement,a+=1}O=!0,0!==Object.keys(i).length&&R.push(Object.assign(Object.assign({},r),{inherited:!0,styles:i,applied:!0,removable:!1,allowChanges:!1})),R.push(Object.assign(Object.assign({},r),{applied:!1,allowChanges:!1,eligibleToApply:(0,s.canApplyCssRuleToElement)(r.selector,S)}))});let L=document.styleSheets[0];for(let e=0;e<document.styleSheets.length;e+=1){let n=document.styleSheets[e],r=null;try{r=n.cssRules}catch(e){console.log(e);try{r=n.rules}catch(e){console.log(e)}}if(r)for(let e=0;e<r.length;e+=1){let n=r[e],a=m(n);if(a.length>0)for(let e=0;e<a.length;e++){let t=a[e];if(!(null==S?void 0:S.matches("."+CSS.escape(t.class))))continue;let n={};for(let e=0;e<(null===(c=null==t?void 0:t.style)||void 0===c?void 0:c.length);e+=1){let r=null==t?void 0:t.style[e];n[r]=null==t?void 0:t.style[r]}let r={filename:void 0,selector:CSS.escape("."+t.class),classParsed:t.class,source:{},styles:n,applied:!0,modifiers:Object.assign({},t.pseudos.reduce((e,t)=>(e[t]=!0,e),{})),codebaseId:`${t.class} ${(0,i.v4)().toString()}`,removable:!1,allowChanges:!1,cssText:t.cssText};b.push(r)}if(!n.selectorText||N.has(n.selectorText))continue;let o=(0,t.parse)(n.selectorText);if("Selector"!==o.type)continue;let s=o.rules[0];if(!s)continue;let l=s.items.filter(e=>"ClassName"===e.type).map(e=>e.name),d=s.items.filter(e=>"PseudoClass"===e.type);if(0===l.length&&1===d.length&&"is"===d[0].name){let e=d[0];if(e&&(null===(h=e.argument)||void 0===h?void 0:h.type)==="Selector"){let t=_(e.argument);if(t)for(let e of t){if(!(null==S?void 0:S.matches("."+CSS.escape(e.class))))continue;let t={};for(let e=0;e<(null===(g=null==n?void 0:n.style)||void 0===g?void 0:g.length);e+=1){let r=n.style[e];t[r]=n.style[r]}let r={filename:void 0,selector:CSS.escape("."+e.class),classParsed:e.class,source:{},styles:t,applied:!0,modifiers:Object.assign({},e.pseudos.reduce((e,t)=>(e[t]=!0,e),{})),codebaseId:`${n.selectorText} ${(0,i.v4)().toString()}`,removable:!1,allowChanges:!1,cssText:n.style.cssText};b.push(r)}}}if(0===l.length||l.length>1)continue;let u=l[0],p=s.items.filter(e=>"PseudoClass"===e.type).map(e=>e.name);try{if(null==S?void 0:S.matches("."+CSS.escape(u))){let e={};for(let t=0;t<(null===(T=null==n?void 0:n.style)||void 0===T?void 0:T.length);t+=1){let r=n.style[t];e[r]=n.style[r]}b.push({filename:void 0,selector:n.selectorText,classParsed:u,source:{},styles:e,applied:!0,modifiers:Object.assign({},p.reduce((e,t)=>(e[t.name]=!0,e),{})),codebaseId:`${n.selectorText} ${(0,i.v4)().toString()}`,removable:!1,allowChanges:!1,cssText:n.style.cssText})}}catch(e){}}}for(let e=0;e<b.length;e++){let t=b[e];if(!t.modifiers)continue;let n=Object.keys(t.modifiers);if(n.length<1)continue;let r=t.classParsed;if(!r)continue;let a=t.cssText;if(!a)continue;let o=n.map(e=>".tempo-force-"+e).join("");f(L,"."+CSS.escape(r)+o,a,L.cssRules.length)}let P=I.concat(b.sort((e,t)=>{try{return-(0,d.compare)(e.selector,t.selector)}catch(a){let n=!0;try{(0,d.compare)(e.selector,"body")}catch(e){n=!1}let r=!0;try{(0,d.compare)(t.selector,"body")}catch(e){r=!1}if(n&&!r)return -1;if(!n&&r)return 1;return 0}})).concat(R);e.postMessage({id:l.FIXED_IFRAME_MESSAGE_IDS.PROCESSED_CSS_RULES_FOR_ELEMENT,processedCssRules:P})},t.cssEval=(e,t)=>window.getComputedStyle(e,null).getPropertyValue(t),t.getCssEvals=(e,n)=>{var r;let s={},i=u.TempoElement.fromKey(n);if(i.isEmpty())return;let d=(0,o.getNodeForElementKey)(i.getKey());if(!d)return;l.CSS_VALUES_TO_COLLECT.forEach(e=>{s[e]=(0,t.cssEval)(d,e)});let c={};if(d.parentElement){l.CSS_VALUES_TO_COLLECT_FOR_PARENT.forEach(e=>{c[e]=(0,t.cssEval)(d.parentElement,e)});let e=(null===(r=(0,a.default)((0,o.getNodeForElementKey)(i.getKey())))||void 0===r?void 0:r.closest(".dark").length)>0;c.darkEnabledInParent=e}s.parent=c,e.postMessage({id:l.FIXED_IFRAME_MESSAGE_IDS.CSS_EVALS_FOR_ELEMENT,cssEvals:s})},t.getElementClassList=(e,t)=>{let n=u.TempoElement.fromKey(t);if(n.isEmpty())return;let r=(0,o.getNodeForElementKey)(n.getKey());r&&e.postMessage({id:l.FIXED_IFRAME_MESSAGE_IDS.ELEMENT_CLASS_LIST,classList:Array.from(r.classList)})},t.ruleMatchesElement=(e,t,n,r)=>{if(!n)return;let a=u.TempoElement.fromKey(r);if(a.isEmpty())return;let s=(0,o.getNodeForElementKey)(a.getKey());s&&e.postMessage({id:t,matches:null==s?void 0:s.matches(n)})}},9903:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelToSnakeCase=t.isCssSelectorValid=t.getAllClassesFromSelector=t.canRemoveCssClassFromElement=t.canApplyCssRuleToElement=void 0;let r=n(7321);t.canApplyCssRuleToElement=(e,n)=>{var a;try{if(!n||!(0,t.isCssSelectorValid)(e)||n.matches(e))return!1;let o=(0,r.parse)(e);for(;o.nestedRule;)o=o.nestedRule;let s=[],l=new Set(n.classList);null===(a=o.items)||void 0===a||a.forEach(e=>{if("ClassName"===e.type){let t=e.name;l.has(t)||(n.classList.add(t),s.push(t))}});let i=n.matches(e);return s.forEach(e=>{n.classList.remove(e)}),i}catch(e){return console.error(e),!1}},t.canRemoveCssClassFromElement=(e,n)=>{var a;try{if(!(0,t.isCssSelectorValid)(e)||!n.matches(e))return!1;let o=(0,r.parse)(e);for(;o.nestedRule;)o=o.nestedRule;let s=[],l=new Set(n.classList);null===(a=o.items)||void 0===a||a.forEach(e=>{if("ClassName"===e.type){let t=e.name;l.has(t)&&(n.classList.remove(t),s.push(t))}});let i=!n.matches(e);return s.forEach(e=>{n.classList.add(e)}),i}catch(e){return console.error(e),!1}},t.getAllClassesFromSelector=e=>{try{if(!(0,t.isCssSelectorValid)(e))return new Set;let n=(0,r.parse)(e),a=new Set;for(;n;)(n.items||[]).forEach(e=>{"ClassName"===e.type&&a.add(e.name)}),n=n.nestedRule;return a}catch(t){return console.log("Failed to parse classes from selector "+e+", "+t),new Set}};let a=e=>document.createDocumentFragment().querySelector(e);t.isCssSelectorValid=e=>{try{return a(e),(0,r.parse)(e),!0}catch(e){return!1}},t.camelToSnakeCase=e=>e?e.charAt(0).toLowerCase()+e.substring(1).replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`):e},1161:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sleep=t.defaultUIUpdateRunner=void 0,t.defaultUIUpdateRunner=e=>{e()},t.sleep=e=>new Promise(t=>setTimeout(t,e))},5427:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.teardownEditableText=t.setupEditableText=t.getEditingInfo=t.currentlyEditing=t.hasTextContents=t.canEditText=void 0;let a=n(2802),o=n(9538),s=n(6590),l=r(n(5943));t.canEditText=e=>{let t=((0,o.getMemoryStorageItem)(o.TREE_ELEMENT_LOOKUP)||{})[e.codebaseId];return!!t&&t.staticTextContents},t.hasTextContents=e=>{if(!e)return!1;let t=!1,n=!1;return e.childNodes.forEach(e=>{if(e.nodeType===Node.TEXT_NODE){t=!0;return}n=!0}),t&&!n},t.currentlyEditing=()=>null!=(0,o.getMemoryStorageItem)(o.TEXT_EDIT);let i=e=>{(0,o.setMemoryStorageItem)(o.TEXT_EDIT,e)};t.getEditingInfo=()=>(0,o.getMemoryStorageItem)(o.TEXT_EDIT);let d=()=>{(0,o.setMemoryStorageItem)(o.TEXT_EDIT,null)};t.setupEditableText=(e,n,r)=>{let o=(0,a.getNodeForElementKey)(e.getKey());if(!o)return;let d=(0,l.default)(o).text();i({key:e.getKey(),originalText:d}),n.postMessage({id:s.FIXED_IFRAME_MESSAGE_IDS.START_EDITING_TEXT,data:{key:e.getKey(),oldText:d}}),(0,l.default)(o).attr("contenteditable","plaintext-only").trigger("focus"),(0,l.default)(o).css({cursor:"text",outline:"none",border:"none"}),(0,l.default)(o).on("blur",()=>(0,t.teardownEditableText)(n,r))},t.teardownEditableText=(e,n)=>{var r;let o=(0,t.getEditingInfo)();if(!(0,t.currentlyEditing)()||(d(),!o))return;let i=(0,a.getNodeForElementKey)(o.key);if(!i)return;let u=(0,l.default)(i).text();e.postMessage({id:s.FIXED_IFRAME_MESSAGE_IDS.EDITED_TEXT,data:{key:o.key,newText:u,oldText:o.originalText}}),null===(r=window.getSelection())||void 0===r||r.removeAllRanges(),(0,l.default)(i).removeAttr("contenteditable").off("blur").css({cursor:"",outline:"",border:""}),d()}},2802:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isElementInSvg=t.isSkipNavTreeNode=t.isMovingElement=t.getAllUnknownClasses=t.getAllUnknownClasesFromList=t.isOutline=t.hasClass=t.getElementKeyFromNode=t.getUniquePathFromNode=t.getCodebaseIdFromNode=t.getCodebaseIdFromClassName=t.validateUuid=t.clearElementKeyForUniquePath=t.setElementKeyForUniquePath=t.getElementKeyForUniquePath=t.clearNodeForElementKey=t.setNodeForElementKey=t.getNodeForElementKey=t.KNOWN_ATTRIBUTES=t.TEMPO_QUEUE_DELETE_AFTER_HOT_RELOAD=t.TEMPO_TEST_ID=t.TEMPO_ELEMENT_ID=t.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH=t.TEMPO_OUTLINE_UNTIL_REFESH=t.TEMPO_DELETE_AFTER_REFRESH=t.TEMPO_INSTANT_UPDATE=t.TEMPO_DELETE_AFTER_INSTANT_UPDATE=t.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS=t.TEMPO_INSTANT_UPDATE_STYLING_PREFIX=t.TEMPO_MOVE_BETWEEN_PARENTS_OUTLINE=t.TEMPO_INSTANT_DIV_DRAW_CLASS=t.EDIT_TEXT_BUTTON=t.OUTLINE_CLASS=void 0;let r=n(84);t.OUTLINE_CLASS="arb89-outline",t.EDIT_TEXT_BUTTON="arb89-edit-text-button",t.TEMPO_INSTANT_DIV_DRAW_CLASS="arb89-instant-div-draw",t.TEMPO_MOVE_BETWEEN_PARENTS_OUTLINE="arb89-move-between-parents-outline",t.TEMPO_INSTANT_UPDATE_STYLING_PREFIX="arb89-styling-",t.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS="arb89-display-none-until-refresh",t.TEMPO_DELETE_AFTER_INSTANT_UPDATE="arb89-delete-after-instant-update";let a=new Set([t.OUTLINE_CLASS,t.TEMPO_INSTANT_DIV_DRAW_CLASS,t.TEMPO_MOVE_BETWEEN_PARENTS_OUTLINE,t.TEMPO_DISPLAY_NONE_UNTIL_REFRESH_CLASS,r.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID,r.TEMPORARY_STYLING_CLASS_NAME,t.EDIT_TEXT_BUTTON]),o=[t.TEMPO_INSTANT_UPDATE_STYLING_PREFIX,r.DUPLICATE_PLACEHOLDER_PREFIX,r.ADD_JSX_PREFIX];t.TEMPO_INSTANT_UPDATE="arb89-instant-update",t.TEMPO_DELETE_AFTER_REFRESH="arb89-delete-after-refresh",t.TEMPO_OUTLINE_UNTIL_REFESH="arb89-outline-until-refresh",t.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH="arb89-do-not-show-in-nav",t.TEMPO_ELEMENT_ID="tempoelementid",t.TEMPO_TEST_ID="data-testid",t.TEMPO_QUEUE_DELETE_AFTER_HOT_RELOAD="arb89-queue-delete-after-hot-reload",t.KNOWN_ATTRIBUTES=new Set([t.TEMPO_INSTANT_UPDATE,t.TEMPO_DELETE_AFTER_REFRESH,t.TEMPO_DELETE_AFTER_INSTANT_UPDATE,t.TEMPO_OUTLINE_UNTIL_REFESH,t.TEMPO_QUEUE_DELETE_AFTER_HOT_RELOAD,t.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH,t.TEMPO_ELEMENT_ID,t.TEMPO_TEST_ID]);let s={};t.getNodeForElementKey=e=>e?s[e]:null,t.setNodeForElementKey=(e,t)=>{s[e]=t},t.clearNodeForElementKey=()=>{s={}};let l={};t.getElementKeyForUniquePath=e=>l[e]||null,t.setElementKeyForUniquePath=(e,t)=>{l[e]=t},t.clearElementKeyForUniquePath=()=>{l={}},t.validateUuid=e=>RegExp("^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$","i").test(e),t.getCodebaseIdFromClassName=e=>e&&e.startsWith("tempo-")&&((0,t.validateUuid)(e.substring(6))||e===r.WRAP_IN_DIV_PLACEHOLDER_CODEBASE_ID||e.startsWith(r.DUPLICATE_PLACEHOLDER_PREFIX))?e:null,t.getCodebaseIdFromNode=e=>{var n;if(!(null==e?void 0:e.classList))return null;let r=null;return(e.classList.forEach(e=>{if(r||!e)return;let n=(0,t.getCodebaseIdFromClassName)(e);n&&(r=n)}),r)?r:(null===(n=null==e?void 0:e.tagName)||void 0===n?void 0:n.toLowerCase())=="body"?"body":(null==e?void 0:e.id)=="root"?"root":(null==e?void 0:e.id)=="__next"?"__next":null},t.getUniquePathFromNode=e=>{var t,n;let r=[],a=e;for(;a&&(null===(t=a.tagName)||void 0===t?void 0:t.toLowerCase())!=="body";){let e=Array.from((null===(n=a.parentElement)||void 0===n?void 0:n.children)||[]).indexOf(a);-1===e?r.push("0"):r.push(e.toString()),a=a.parentElement}return"0-"+r.reverse().join("-")},t.getElementKeyFromNode=e=>{let n=(0,t.getUniquePathFromNode)(e);return(0,t.getElementKeyForUniquePath)(n)},t.hasClass=(e,t)=>{if(!(null==e?void 0:e.classList))return!1;let n=!1;return e.classList.forEach(e=>{e==t&&(n=!0)}),n},t.isOutline=e=>(0,t.hasClass)(e,t.OUTLINE_CLASS),t.getAllUnknownClasesFromList=e=>e.filter(e=>{if(!e)return!1;let n=null!==(0,t.getCodebaseIdFromClassName)(e);return!(o.some(t=>e.startsWith(t))||a.has(e))&&!n}),t.getAllUnknownClasses=e=>(null==e?void 0:e.classList)?(0,t.getAllUnknownClasesFromList)(Array.from(e.classList)):[],t.isMovingElement=e=>!!e&&"function"==typeof e.getAttribute&&"true"===e.getAttribute(t.TEMPO_INSTANT_UPDATE),t.isSkipNavTreeNode=e=>{if(e)return"true"===e.getAttribute(t.TEMPO_DO_NOT_SHOW_IN_NAV_UNTIL_REFRESH)},t.isElementInSvg=(e,n)=>{var r;return!!e&&(!!n&&(null===(r=e.tagName)||void 0===r?void 0:r.toLowerCase())==="svg"||!!e.parentNode&&(0,t.isElementInSvg)(e.parentNode,!0))}},3859:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))(function(a,o){function s(e){try{i(r.next(e))}catch(e){o(e)}}function l(e){try{i(r.throw(e))}catch(e){o(e)}}function i(e){var t;e.done?a(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,l)}i((r=r.apply(e,t||[])).next())})},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.initChannelMessaging=void 0;let o=n(9557),s=a(n(2667)),l=a(n(5983)),i=n(44);if("undefined"!=typeof window&&window.location.href.includes("framework=VITE")){let e=e=>{let t=customElements.get("vite-error-overlay");if(!t)return;let n=new t(e);document.body.appendChild(n)};window.addEventListener("error",e),window.addEventListener("unhandledrejection",({reason:t})=>e(t))}let d=(...e)=>{var t;(null===(t=window.location.search)||void 0===t?void 0:t.includes("debugLog=true"))&&console.debug(...e)};t.initChannelMessaging=function(){var e;if("undefined"!=typeof window&&((0,i.initChannelMessagingFunctions)(),window.location.hostname.endsWith("dev.tempolabs.ai")&&!window.location.hostname.endsWith("staging-dev.tempolabs.ai")&&l.default&&l.default.init()),"undefined"!=typeof window&&window.addEventListener("message",e=>{let{data:t}=e;if("GET_STATE_AND_PROPS"===t.type){let{componentName:e}=t,n="#root";document.querySelector(n)||(n="#__next");let r=(0,o.getRootReactElement)(),a=(0,o.buildNodeTree)(r,null),s=(0,o.findElementInTree)(a,t=>!!e&&t.name==e);if(!(null==s?void 0:s.length)){console.log("STATE_AND_PROPS ",JSON.stringify({error:"No component found"}));return}s.length>1&&(console.log(s),console.log("Warning: more than 1 component found")),(e=>{d("NODE FOUND: ",e);let n={tempoelementid:!0,"data-testid":!0},r={};e.props&&Object.keys(e.props).forEach(t=>{n[t]||("object"==typeof e.props[t]?r[t]="TEMPO_OBJECT_TYPE":"function"==typeof e.props[t]?r[t]="TEMPO_FUNCTION_TYPE":r[t]=e.props[t])});let a={};e.state&&("string"==typeof e.state?a={state:e.state}:Object.keys(e.state).forEach(t=>{"object"==typeof e.state[t]?a[t]="TEMPO_OBJECT_TYPE":"function"==typeof e.state[t]?a[t]="TEMPO_FUNCTION_TYPE":a[t]=e.state[t]})),console.log("STATE_AND_PROPS ",JSON.stringify({id:t.id,props:r,state:a}))})(s[0])}}),"undefined"!=typeof window){if(null===(e=window.location.search)||void 0===e?void 0:e.includes("storyboard=true")){let e=document.getElementById("root");e||(e=document.getElementById("__next")),e&&(window.location.search.includes("type=STORY")||window.location.search.includes("type=COMPONENT")?[e,document.body,document.documentElement].forEach(e=>{e.style.backgroundColor="transparent",e.style.width="100vw",e.style.height="100vh",e.style.overflow="hidden"}):(e.style.width="100vw",e.style.height="100vh"))}!function(){let e=null,t=null;window.addEventListener("message",t=>{"init"===t.data?(e=t.ports[0]).onmessage=a:a({data:t.data})});let n=t=>r(this,void 0,void 0,function*(){if(!t.payload.componentName){console.log("NO COMPONENT NAME");let n={id:t.id,error:"No component name"};null==e||e.postMessage(n);return}let n=(0,o.getRootReactElement)(),r=(0,o.buildNodeTree)(n,null),{isComponent:a,componentName:s,tempoElementID:l,componentElementId:i}=t.payload;if(!a&&!l){console.log("No tempo element ID provided");let n={id:t.id,error:"Could not find element"};null==e||e.postMessage(n);return}if(a&&!l&&!s){console.log("No tempo element ID or component name provided");let n={id:t.id,error:"Could not find component"};null==e||e.postMessage(n);return}let u=(0,o.findElementInTree)(r,e=>{var t,n,r,o,d,u;if(a){if(l&&((null===(t=e.props)||void 0===t?void 0:t.tempoelementid)==l||(null===(n=e.props)||void 0===n?void 0:n["data-testid"])==l)||!l&&s&&e.name==s)return!0}else{if(l&&(null===(r=e.props)||void 0===r?void 0:r.tempoelementid)!==l&&(null===(o=e.props)||void 0===o?void 0:o["data-testid"])!==l)return!1;if(i){let t=e.parent,n=!1;for(;t;){if((null===(d=t.props)||void 0===d?void 0:d.tempoelementid)===i||(null===(u=t.props)||void 0===u?void 0:u["data-testid"])===i){n=!0;break}t=t.parent}if(!n)return!1}return!0}return!1});if(!(null==u?void 0:u.length)){d("NO COMPONENT FOUND");let n={id:t.id,error:"No component found"};null==e||e.postMessage(n);return}u.length>1&&(console.log(u),console.log("Warning: more than 1 component found")),(n=>{d("NODE FOUND: ",n);let r={};n.props&&Object.keys(n.props).forEach(e=>{"object"==typeof n.props[e]?r[e]="TEMPO_OBJECT_TYPE":"function"==typeof n.props[e]?r[e]="TEMPO_FUNCTION_TYPE":r[e]=n.props[e]});let a={};n.state&&("string"==typeof n.state?a={state:n.state}:Object.keys(n.state).forEach(e=>{"object"==typeof n.state[e]?a[e]="TEMPO_OBJECT_TYPE":"function"==typeof n.state[e]?a[e]="TEMPO_FUNCTION_TYPE":a[e]=n.state[e]}));let o={id:t.id,props:r,state:a};d("RESPONDING WITH: ",o),null==e||e.postMessage(o)})(u[0])}),a=a=>r(this,void 0,void 0,function*(){var r,o;try{let l=a.data,i=Object.assign({},l);if((null===(r=null==l?void 0:l.payload)||void 0===r?void 0:r.compressedArgs)&&(i.payload=Object.assign(Object.assign({},l.payload),{compressedArgs:"COMPRESSED"})),(null===(o=null==l?void 0:l.payload)||void 0===o?void 0:o.functionName)&&["initProject","setNewLookups","processRulesForSelectedElement"].includes(l.payload.functionName)&&(i.payload=Object.assign(Object.assign({},l.payload),{args:"ARGS_SKIPPED"})),d("INNER FRAME: Received message from parent: ",JSON.stringify(i)),!l||!l.payload){d("NO PAYLOAD");return}if(!l.id){d("NO ID");return}if("inspectElement"===l.type)n(l);else if("executeFunction"===l.type){let n=window[l.payload.functionName];if("function"==typeof n){let r=l.payload.args;if(l.payload.compressedArgs&&(r=JSON.parse(s.default.decompress(l.payload.compressedArgs))),"initProject"===l.payload.functionName&&(t=r[0],r=r.slice(1)),l.payload.args){let a=n(e,t,...r);a instanceof Promise&&(yield a)}else{let r=n(e,t);r instanceof Promise&&(yield r)}}else console.log("INNER FRAME ERROR: Function to execute not found")}}catch(e){console.log("INNER FRAME ERROR: ",e)}})}()}}},6227:function(e,t,n){"use strict";var r,a,o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.runNavTreeBuiltCallbacks=t.addNavTreeBuiltCallback=t.buildNavForNodeNonBlocking=t.getNavNodeForVirtualComponent=t.ExtractedPropType=t.SKIP_ROOT_CODEBASE_ID=t.EMPTY_TREE_CODEBASE_ID=t.TOP_LEVEL_PARENT_COMPONENT_TO_SKIP=t.UNKNOWN_PARENT_COMPONENT=void 0;let s=n(2802),l=n(8372),i=o(n(5943)),d=n(9538),u=n(7928),c=n(9557);t.UNKNOWN_PARENT_COMPONENT="UnknownComponent",t.TOP_LEVEL_PARENT_COMPONENT_TO_SKIP="TOP_LEVEL_PARENT_COMPONENT_TO_SKIP",t.EMPTY_TREE_CODEBASE_ID="EMPTY-TREE",t.SKIP_ROOT_CODEBASE_ID="SKIP-ROOT",(r=a||(t.ExtractedPropType=a={})).LITERAL="LITERAL",r.FUNCTION="FUNCTION",r.JSON_OBJECT="JSON_OBJECT";let p=e=>{if(!(null==e?void 0:e.memoizedProps))return{};let t={};return Object.keys(e.memoizedProps).forEach(n=>{if("children"===n||s.KNOWN_ATTRIBUTES.has(n.toLowerCase()))return;let r=e.memoizedProps[n];if("className"===n&&"string"==typeof r&&(r=(0,s.getAllUnknownClasesFromList)(r.split(" ")).join(" ")),"function"==typeof r)t[n]={value:n,type:a.FUNCTION};else if("object"==typeof r)try{t[n]={value:JSON.stringify(r),type:a.JSON_OBJECT}}catch(e){}else t[n]={value:r,type:a.LITERAL}}),t},E=e=>{var t;if(!(null===(t=null==e?void 0:e.memoizedProps)||void 0===t?void 0:t.children))return[];let n=[];return Array.from(e.memoizedProps.children||[]).forEach((e,t)=>{"object"!=typeof e&&n.push({index:t,value:e})}),n};function f(e){let t={"!":"_exclamation_","@":"_at_","#":"_hash_",$:"_dollar_","%":"_percent_","^":"_caret_","&":"_and_","*":"_asterisk_","(":"_openParen_",")":"_closeParen_","+":"_plus_","=":"_equals_","[":"_openBracket_","]":"_closeBracket_","{":"_openBrace_","}":"_closeBrace_","|":"_pipe_",";":"_semicolon_",":":"_colon_",",":"_comma_",".":"_period_","<":"_lessThan_",">":"_greaterThan_","/":"_slash_","?":"_question_","\\":"_backslash_"," ":"_space_"};return Object.keys(t).forEach(n=>{let r=RegExp("\\"+n,"g");e=e.replace(r,t[n])}),(e=e.replace(/^[0-9-]/,"_startNumOrHyphen_")).replace(/[^a-zA-Z0-9_-]/g,"_")}t.getNavNodeForVirtualComponent=(e,t,n,r,a,o,s)=>{let l={parent:e,children:[],classList:[],directlySetClassList:[],name:"",tempoElement:u.TempoElement.empty()};return l.name=t,l.isComponent=!0,l.tempoElement=new u.TempoElement(n,o,r),l.props=p(s),l.literalChildren=E(s),Object.keys(a).forEach(e=>{var t;!l.scope&&(null===(t=a[e].codebaseIds)||void 0===t?void 0:t.indexOf(n))>-1&&(l.scope=a[e])}),l},t.buildNavForNodeNonBlocking=(e,t)=>{(0,s.clearNodeForElementKey)(),(0,s.clearElementKeyForUniquePath)();let n=[{params:e}],r=null;requestAnimationFrame(function a(){var o;let s=performance.now();for(;n.length>0&&performance.now()-s<10;){let e=n.shift();if(!e)continue;let{params:t}=e,a=m(t);(null===(o=null==a?void 0:a.callsToAddToQueue)||void 0===o?void 0:o.length)&&n.push(...a.callsToAddToQueue),!r&&(null==a?void 0:a.result)&&(r=null==a?void 0:a.result)}n.length>0?requestAnimationFrame(a):r?t(h(r,e.elementKeyToNavNode,e.treeElements,e.storyboardId)):t(null)})};let m=({storyboardId:e,parent:n,node:r,uniquePathBase:a,uniquePathAddon:o,scopeLookup:d,treeElements:m,knownComponentNames:h,knownComponentInstanceNames:_,elementKeyToLookupList:g,elementKeyToNavNode:T,domUniquePath:y})=>{var S,v,I,A,N,C,O,b,R;let L,P;if((0,l.isNodeOutline)(r)||(0,s.isSkipNavTreeNode)(r)||["noscript","script"].includes(null===(S=null==r?void 0:r.tagName)||void 0===S?void 0:S.toLowerCase()))return null;let M=(0,s.getCodebaseIdFromNode)(r);if((null===(v=null==r?void 0:r.tagName)||void 0===v?void 0:v.toLowerCase())==="iframe"&&!M)return r.remove(),null;let D=(0,c.findReactInstance)(r),U=r.getBoundingClientRect(),{top:w,left:F}=(0,i.default)(r).offset()||{top:0,left:0},x=n,K=a,j=[];if(D&&(null==n?void 0:n.reactFiberNode)){let r=D.return,a=[];for(;r&&r!==n.reactFiberNode;){let e=(null===(I=null==r?void 0:r._debugSource)||void 0===I?void 0:I.fileName)&&!(null===(N=null===(A=null==r?void 0:r._debugSource)||void 0===A?void 0:A.fileName)||void 0===N?void 0:N.includes("node_modules")),t=(0,c.getElementNameString)(r);((null===(C=r.memoizedProps)||void 0===C?void 0:C.tempoelementid)||(null===(O=r.memoizedProps)||void 0===O?void 0:O["data-testid"]))&&((null==h?void 0:h.has(t))||(null==_?void 0:_.has(t))||e)&&a.push(r),r=r.return}if(r&&a.length){let r=n;Array.from(a).reverse().forEach(n=>{var a,o,s,l,i,u,p,E;let h=(0,c.getElementNameString)(n);if((null===(o=null===(a=null==n?void 0:n.elementType)||void 0===a?void 0:a.$$typeof)||void 0===o?void 0:o.toString())==="Symbol(react.forward_ref)"){if(!L&&!P){let e=m[(P=(null===(s=n.memoizedProps)||void 0===s?void 0:s.tempoelementid)||(null===(l=n.memoizedProps)||void 0===l?void 0:l["data-testid"]))||""];L=(null==e?void 0:e.type)==="component-instance"?e.componentName:h}return}let _=r?null===(i=r.children)||void 0===i?void 0:i.find(e=>e.reactFiberNode===n):null;if(_){if((r=_).tempoElement&&j.push(r.tempoElement.getKey()),_.pageBoundingBox){let e=Math.max(_.pageBoundingBox.pageX+_.pageBoundingBox.width,F+U.width),t=Math.min(_.pageBoundingBox.pageX,U.left),n=Math.min(_.pageBoundingBox.pageY,U.top),r=Math.max(_.pageBoundingBox.pageY+_.pageBoundingBox.height,w+U.height);_.pageBoundingBox.pageX=t,_.pageBoundingBox.pageY=n,_.pageBoundingBox.width=e-t,_.pageBoundingBox.height=r-n}else _.pageBoundingBox={pageX:F,pageY:w,width:U.width,height:U.height};return}{let a;L?(a=P,L=void 0,P=void 0):a=(null===(u=n.memoizedProps)||void 0===u?void 0:u.tempoelementid)||(null===(p=n.memoizedProps)||void 0===p?void 0:p["data-testid"]),K=f(`${K}-${(null===(E=null==r?void 0:r.children)||void 0===E?void 0:E.length)||0}`);let o=(0,t.getNavNodeForVirtualComponent)(r,h,a,K,d,e,n);r.children.push(o),r=o,j.push(o.tempoElement.getKey()),T[o.tempoElement.getKey()]=o,o.pageBoundingBox={pageX:F,pageY:w,width:U.width,height:U.height}}}),x=r}}let Y={parent:x,children:[],classList:(0,s.getAllUnknownClasses)(r),directlySetClassList:[],name:"",tempoElement:u.TempoElement.empty()};null===(b=null==x?void 0:x.children)||void 0===b||b.push(Y),Y.name=L||r.tagName,Y.elementTagName=r.tagName,Y.isComponent=!!P;let k=f(`${K}${o}`),H=P||M||void 0;Y.tempoElement=new u.TempoElement(H,e,k);let $=Y.tempoElement.getKey();j.forEach(e=>{g[e]?g[e].push($):g[e]=[$]}),g[$]=[$],(0,s.setNodeForElementKey)($,r),(0,s.setElementKeyForUniquePath)(y,$);let B=m[Y.tempoElement.codebaseId];if(B){let e=new Set((null==B?void 0:B.removableClasses)||[]);Y.directlySetClassList=null===(R=Y.classList)||void 0===R?void 0:R.filter(t=>e.has(t))}Y.reactFiberNode=D,Y.props=p(D),Y.literalChildren=E(D),Y.pageBoundingBox={pageX:F,pageY:w,width:U.width,height:U.height},Y.tempoElement.codebaseId&&Object.keys(d).forEach(e=>{var t;!Y.scope&&(null===(t=d[e].codebaseIds)||void 0===t?void 0:t.indexOf(Y.tempoElement.codebaseId))>-1&&(Y.scope=d[e])});let V=[];return r.children&&"svg"!==r.tagName&&Array.from(r.children).forEach((t,n)=>{V.push({params:{storyboardId:e,parent:Y,node:t,uniquePathBase:k,uniquePathAddon:`-${n}`,scopeLookup:d,treeElements:m,knownComponentNames:h,knownComponentInstanceNames:_,elementKeyToLookupList:g,elementKeyToNavNode:T,domUniquePath:`${y}-${n}`}})}),T[$]=Y,{result:Y,callsToAddToQueue:V}},h=(e,n,r,a)=>{let o=e,s=(0,d.getMemoryStorageItem)(d.STORYBOARD_TYPE)||"APPLICATION",l=(0,d.getMemoryStorageItem)(d.SAVED_STORYBOARD_COMPONENT_FILENAME),i=(0,d.getMemoryStorageItem)(d.ORIGINAL_STORYBOARD_URL),c=i&&!window.location.href.includes(i),p=e=>{var t,n,a,o,i,d;let u=null===(t=r[e.tempoElement.codebaseId])||void 0===t?void 0:t.filename;if("STORY"===s&&u&&!u.includes("_app")&&!u.includes("_document"))return!0;if((null===(n=e.parent)||void 0===n?void 0:n.name)==="BODY"){let t=null===(a=e.reactFiberNode)||void 0===a?void 0:a.return;for(;t;){let e=(null===(o=null==t?void 0:t.props)||void 0===o?void 0:o.tempoelementid)||(null===(i=null==t?void 0:t.props)||void 0===i?void 0:i["data-testid"])||"";if(e){let t=null===(d=r[e])||void 0===d?void 0:d.filename;if((null==t?void 0:t.includes("tempobook/storyboards"))||t&&t===l)return!0}t=null==t?void 0:t.return}}return!!(null==u?void 0:u.includes("tempobook/storyboards"))||!!(u&&u===l)},E=(e,r)=>{var l,i;for(let t=e.children.length-1;t>=0;t--)E(e.children[t],r||p(e));let d="APPLICATION"!==s&&!c&&!r&&!p(e);if(!(null===(l=e.tempoElement.codebaseId)||void 0===l?void 0:l.startsWith("tempo-"))||d){if(e.parent){let t=e.children,r=null===(i=e.parent.children)||void 0===i?void 0:i.indexOf(e);e.parent.children.splice(r,1,...t),t.forEach(t=>{t.parent=e.parent}),delete n[e.tempoElement.getKey()]}else 1===e.children.length?(o=e.children[0],delete n[e.tempoElement.getKey()],o.parent=void 0):(0===e.children.length?o={children:[],tempoElement:new u.TempoElement(t.EMPTY_TREE_CODEBASE_ID,a,"1"),name:""}:e.tempoElement=new u.TempoElement(t.SKIP_ROOT_CODEBASE_ID,e.tempoElement.storyboardId,e.tempoElement.uniquePath),delete n[e.tempoElement.getKey()])}};E(e,!1);let f=(e,n)=>{delete e.reactFiberNode,e.level=n,e.children.forEach(r=>{f(r,e.tempoElement.codebaseId===t.SKIP_ROOT_CODEBASE_ID?n:n+1)})};return f(o,0),o};t.addNavTreeBuiltCallback=e=>{let{callbackFn:t,state:n}=e,r=(0,d.getMemoryStorageItem)(d.NAV_TREE_CALLBACKS)||[];n.multiSelectedElementKeys=(n.multiSelectedElementKeys||[]).sort(),r.find(e=>e.callbackFn.toString()===t.toString()&&e.state.selectedElementKey===n.selectedElementKey&&e.state.multiSelectedElementKeys.join(",")===n.multiSelectedElementKeys.join(","))||(r.push(e),(0,d.setMemoryStorageItem)(d.NAV_TREE_CALLBACKS,r))},t.runNavTreeBuiltCallbacks=()=>{let e=(0,d.getMemoryStorageItem)(d.NAV_TREE_CALLBACKS)||[];if(!e.length)return;let t=(0,d.getMemoryStorageItem)(d.SELECTED_ELEMENT_KEY),n=((0,d.getMemoryStorageItem)(d.MULTI_SELECTED_ELEMENT_KEYS)||[]).sort();e.forEach(e=>{let{callbackFn:r,state:a}=e;a.selectedElementKey===t&&a.multiSelectedElementKeys.join(",")===n.join(",")&&r()}),(0,d.removeMemoryStorageItem)(d.NAV_TREE_CALLBACKS)}},8372:function(e,t,n){"use strict";var r,a,o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.isNodeOutline=t.updateOutlines=t.clearAllOutlines=t.getOutlineElement=t.OutlineType=t.PRIMARY_COMPONENT_OUTLINE_COLOR=t.SECONDARY_OUTLINE_COLOUR=t.PRIMARY_OUTLINE_COLOUR=void 0;let s=n(2802),l=n(9538),i=o(n(5943)),d=n(7928),u=n(5427),c=n(6590);t.PRIMARY_OUTLINE_COLOUR="#4597F7",t.SECONDARY_OUTLINE_COLOUR="#4597F7",t.PRIMARY_COMPONENT_OUTLINE_COLOR="#6183e4",(r=a||(t.OutlineType=a={}))[r.PRIMARY=0]="PRIMARY",r[r.SECONDARY=1]="SECONDARY",r[r.CHILD=2]="CHILD",r[r.MOVE=3]="MOVE";let p=()=>(0,l.getMemoryStorageItem)("aiContext")?{primary:"#6858f5",secondary:"#6858f5",component:"#5246C2"}:{primary:t.PRIMARY_OUTLINE_COLOUR,secondary:t.SECONDARY_OUTLINE_COLOUR,component:t.PRIMARY_COMPONENT_OUTLINE_COLOR},E=(e,t,n)=>`url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='${e.replace("#","%23")}' stroke-width='${t}' stroke-dasharray='1%2c ${n}' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e")`,f=e=>e.charAt(0).toUpperCase()+e.slice(1),m=()=>'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pencil"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/></svg>',h=(e,t,n)=>{let r=document.createElement("div"),a=document.createElement("div");a.innerText="Edit Dynamic Text",a.classList.add(s.EDIT_TEXT_BUTTON),a.classList.add(s.OUTLINE_CLASS);let o=document.createElement("div");return o.innerHTML=m(),o.style.width="22px",o.style.height="22px",o.classList.add(s.EDIT_TEXT_BUTTON),o.classList.add(s.OUTLINE_CLASS),r.appendChild(o),r.appendChild(a),r.classList.add(s.OUTLINE_CLASS),r.classList.add(s.EDIT_TEXT_BUTTON),r.style.color="white",r.style.cursor="pointer",r.style.backgroundColor=t,r.style.padding="4px 12px 4px 12px",r.style.borderRadius="8px",r.style.fontSize="20px",r.style.pointerEvents="auto",r.style.display="flex",r.style.flexDirection="row",r.style.alignItems="center",r.style.justifyContent="center",r.style.gap="8px",r.addEventListener("pointerdown",t=>{t.preventDefault(),t.stopPropagation(),e.postMessage({id:c.FIXED_IFRAME_MESSAGE_IDS.EDIT_DYNAMIC_TEXT,elementKey:n})}),r.addEventListener("pointerup",e=>{e.preventDefault(),e.stopPropagation()}),r};t.getOutlineElement=(e,t,n,r,o,i,c,m,_,g)=>{let T=p(),y=(0,l.getMemoryStorageItem)("zoomPerc"),S=y?1/Number(y):1,v=document.createElement("div");if(v.classList.add(s.OUTLINE_CLASS),t===a.CHILD||t===a.MOVE){let e=5*S;v.style.backgroundImage=E(_?T.component:T.primary,Math.max(1,Math.round(e)),Math.max(3,Math.round(3*e)))}else{let e=t===a.SECONDARY?.5*S:1*S;e>=.5&&(v.style.outline=`${e}px solid ${t===a.SECONDARY?T.secondary:_?T.component:T.primary}`),v.style.border=`${e>=.5?e:2*e}px solid ${t===a.SECONDARY?T.secondary:_?T.component:T.primary}`}switch(v.style.position="fixed",v.style.pointerEvents="none",t){case a.PRIMARY:v.style.zIndex="2000000002";break;case a.SECONDARY:v.style.zIndex="2000000001";break;case a.CHILD:v.style.zIndex="2000000000";break;case a.MOVE:v.style.zIndex="2000000003"}v.style.boxSizing="border-box",v.style.left=n+"px",v.style.top=r+"px",v.style.width=o+"px",v.style.height=i+"px",v.style.cursor="default !important";let I=Math.min(2,S);if(t===a.PRIMARY&&c){let e=document.createElement("div");v.appendChild(e),e.classList.add(s.OUTLINE_CLASS),e.innerHTML=`${Math.round(o)} x ${Math.round(i)}`,e.style.color="white",e.style.backgroundColor=_?T.component:T.primary,e.style.padding="4px 12px 4px 12px",e.style.height="38px",e.style.borderRadius="8px",e.style.position="absolute",e.style.left=`calc(${o}px / 2)`,e.style.fontSize="20px",e.style.whiteSpace="nowrap",e.style.bottom=`${-Math.max(22,45+(52*I-52)/2)}px`,e.style.transform=`scale(${I}) translateX(${-50/I}%)`}if(c&&m){let n=document.createElement("div");v.appendChild(n),n.style.display="flex",n.style.width=o/I+"px",n.style.justifyContent="space-between",n.style.flexDirection="row",n.style.gap="4px",n.style.position="absolute",n.style.left="0px",n.style.transform=`scale(${I}) translateX(${50-50/I}%) translateY(${-70-50/I}%)`;let r=document.createElement("div");if(n.appendChild(r),r.classList.add(s.OUTLINE_CLASS),r.innerHTML=m?_?f(m):m.toLowerCase():"",r.style.color="white",r.style.backgroundColor=_?T.component:T.primary,r.style.padding="4px 12px 4px 12px",r.style.height="38px",r.style.borderRadius="8px",r.style.fontSize="20px",t===a.PRIMARY){let t=(0,s.getNodeForElementKey)(g),r=d.TempoElement.fromKey(g||"");if(t&&(0,u.hasTextContents)(t)&&!(0,u.canEditText)(r)){let t=h(e,_?T.component:T.primary,g);n.appendChild(t)}}}return v},t.clearAllOutlines=()=>{(0,i.default)(`.${s.OUTLINE_CLASS}`).remove()},t.updateOutlines=(e,n)=>{if((0,t.clearAllOutlines)(),(0,l.getSessionStorageItem)("driveModeEnabled",n))return;let r=(0,l.getMemoryStorageItem)(l.HOVERED_ELEMENT_KEY),o=(0,l.getMemoryStorageItem)(l.SELECTED_ELEMENT_KEY),u=(0,l.getMemoryStorageItem)(l.MULTI_SELECTED_ELEMENT_KEYS),c=d.TempoElement.fromKey(o),p=document.getElementsByTagName("body")[0],E=document.createDocumentFragment(),f=(0,l.getMemoryStorageItem)(l.ELEMENT_KEY_TO_NAV_NODE)||{},m={},h=(0,i.default)("body"),_=(0,i.default)(`.${s.TEMPO_INSTANT_DIV_DRAW_CLASS}`),g=(0,i.default)(`*[${s.TEMPO_OUTLINE_UNTIL_REFESH}=true]`),T={},y=e=>{if(!T[e]){let t=(0,s.getNodeForElementKey)(e);t&&(T[e]=(0,i.default)(t))}return T[e]},S=e=>{var t;if(void 0!==m[e])return m[e];let n=f[e],r=null;if(null==n?void 0:n.pageBoundingBox)r={left:n.pageBoundingBox.pageX,top:n.pageBoundingBox.pageY,width:n.pageBoundingBox.width,height:n.pageBoundingBox.height};else{let n=y(e).get(0),a=null===(t=null==n?void 0:n.getBoundingClientRect)||void 0===t?void 0:t.call(n);a&&(r={left:a.left,top:a.top,width:a.width,height:a.height})}return m[e]=r,r},v=(n,r,o,s)=>{var i,d;let u=f[n];if(!u)return;let c=null==u?void 0:u.name,p=S(n);if(p){E.appendChild((0,t.getOutlineElement)(e,o?a.CHILD:a.PRIMARY,p.left,p.top,p.width,p.height,r,c,null==u?void 0:u.isComponent,n));let s=(0,l.getMemoryStorageItem)("mouseDragContext"),i=(0,l.getMemoryStorageItem)("mousePos");r&&(null==s?void 0:s.dragging)&&i&&E.appendChild((0,t.getOutlineElement)(e,a.MOVE,i.pageX-p.width/2+s.offsetX,i.pageY-p.height/2+s.offsetY,p.width,p.height,void 0,void 0,null==u?void 0:u.isComponent,n))}s&&(null===(d=null===(i=null==u?void 0:u.children)||void 0===i?void 0:i.forEach)||void 0===d||d.call(i,e=>{v(e.tempoElement.getKey(),!1,!0,!1)}))};if(r&&v(r,!1,!1,!0),null==u?void 0:u.length){let n=1/0,r=1/0,o=-1/0,s=-1/0,l=!1;for(let e of u){let t=S(e);if(t){l=!0;let e=t.left+t.width,a=t.top+t.height;n=Math.min(n,t.left),r=Math.min(r,t.top),o=Math.max(o,e),s=Math.max(s,a)}}if(l){let l={left:n,top:r,width:o-n,height:s-r};E.appendChild((0,t.getOutlineElement)(e,a.PRIMARY,l.left,l.top,l.width,l.height,!0,`${u.length} Elements`,!1))}u.forEach(e=>{v(e,!1,!1,!1)})}else o&&v(o,!0,!1,!1);if(_.each((n,r)=>{let o=r.getBoundingClientRect();E.appendChild((0,t.getOutlineElement)(e,a.PRIMARY,o.left,o.top,o.width,o.height))}),g.each((n,r)=>{let o=r.getBoundingClientRect();E.appendChild((0,t.getOutlineElement)(e,a.PRIMARY,o.left,o.top,o.width,o.height))}),null==c?void 0:c.codebaseId){let n=h.find(`.${null==c?void 0:c.codebaseId}`);if(o){let e=(0,s.getNodeForElementKey)(o);e&&(n=n.not(e))}if(r){let e=(0,s.getNodeForElementKey)(r);e&&(n=n.not(e))}n.each((n,r)=>{let o=r.getBoundingClientRect();E.appendChild((0,t.getOutlineElement)(e,a.SECONDARY,o.left,o.top,o.width,o.height))})}p.appendChild(E)},t.isNodeOutline=e=>{if(!(null==e?void 0:e.classList))return!1;let t=!1;return e.classList.forEach(e=>{e===s.OUTLINE_CLASS&&(t=!0)}),t}},9557:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.findElementInTree=t.getElementName=t.getDomElementForReactNode=t.buildNodeTree=t.getElementNameString=t.getRootReactElement=t.findReactInstance=void 0,t.findReactInstance=e=>{if(e.hasOwnProperty("_reactRootContainer"))return e._reactRootContainer._internalRoot?e._reactRootContainer._internalRoot.current:e._reactRootContainer.current;let t=Object.keys(e).find(e=>e.startsWith("__reactInternalInstance")||e.startsWith("__reactFiber")||e.startsWith("__reactContainer"));if(t)return e[t]},t.getRootReactElement=()=>{var e;let n="#root";document.querySelector(n)||(n="#__next");let r=document.querySelector(n),a=null;return r?a=(0,t.findReactInstance)(r):function e(n){var r;a||(a=(0,t.findReactInstance)(n))||null===(r=n.childNodes)||void 0===r||r.forEach(t=>{e(t)})}(document.getElementsByTagName("body")[0]),a&&!a.child&&(null===(e=a.alternate)||void 0===e?void 0:e.child)&&(a=a.alternate),a};let n=e=>{if(!e||"string"==typeof e)return e;let t=Object.assign({},e);return delete t.children,t},r=e=>{if(!e)return;let{baseState:t}=e;return t||e};t.getElementNameString=e=>{var n;let r=(0,t.getElementName)(e.type);return"string"!=typeof r?null===(n=null==r?void 0:r.toString)||void 0===n?void 0:n.call(r):r},t.buildNodeTree=(e,a)=>{let o={children:[]};if(o.element=e,o.parent=a,!e)return o;o.name=(0,t.getElementNameString)(e),o.props=n(e.memoizedProps),o.state=r(e.memoizedState);let{child:s}=e;if(s)for(o.children.push(s);s.sibling;)o.children.push(s.sibling),s=s.sibling;return o.children=o.children.map(e=>(0,t.buildNodeTree)(e,o)),o},t.getDomElementForReactNode=e=>{var t,n,r;let a=null===(t=null==e?void 0:e.element)||void 0===t?void 0:t.stateNode;return(a&&(null===(n=null==a?void 0:a.constructor)||void 0===n?void 0:n.name)==="FiberRootNode"&&(a=a.containerInfo),r=a,"object"==typeof HTMLElement?r instanceof HTMLElement:r&&"object"==typeof r&&null!==r&&1===r.nodeType&&"string"==typeof r.nodeName)?a:null};let a=e=>"function"==typeof e,o=e=>"object"==typeof e;t.getElementName=e=>{var t;return e?a(e)||o(e)?e.displayName?a(e.displayName)?e.displayName():e.displayName:e.name?a(e.name)?e.name():e.name:(null===(t=e.render)||void 0===t?void 0:t.name)?e.render.name:null:e:e},t.findElementInTree=(e,t,n)=>{let r=[e],a=[];for(;r.length>0;){let e=r.shift();if(t(e)&&(a.push(e),n))break;r=r.concat(e.children||[])}return a}},9538:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.removeSessionStorageItem=t.setSessionStorageItem=t.getSessionStorageItem=t.removeMemoryStorageItem=t.setMemoryStorageItem=t.getMemoryStorageItem=t.CURRENT_NAV_TREE=t.ELEMENT_KEY_TO_NAV_NODE=t.ELEMENT_KEY_TO_LOOKUP_LIST=t.NAV_TREE_CALLBACKS=t.IS_FLUSHING=t.HOT_RELOADING=t.TEXT_EDIT=t.HOVERED_ELEMENT_KEY=t.MULTI_SELECTED_ELEMENT_KEYS=t.SELECTED_ELEMENT_KEY=t.SAVED_STORYBOARD_COMPONENT_FILENAME=t.ORIGINAL_STORYBOARD_URL=t.STORYBOARD_TYPE=t.STORYBOARD_COMPONENT=t.SCOPE_LOOKUP=t.TREE_ELEMENT_LOOKUP=void 0,t.TREE_ELEMENT_LOOKUP="TREE_ELEMENT_LOOKUP",t.SCOPE_LOOKUP="SCOPE_LOOKUP",t.STORYBOARD_COMPONENT="STORYBOARD_COMPONENT",t.STORYBOARD_TYPE="STORYBOARD_TYPE",t.ORIGINAL_STORYBOARD_URL="ORIGINAL_STORYBOARD_URL",t.SAVED_STORYBOARD_COMPONENT_FILENAME="SAVED_STORYBOARD_COMPONENT_FILENAME",t.SELECTED_ELEMENT_KEY="SELECTED_ELEMENT_KEY",t.MULTI_SELECTED_ELEMENT_KEYS="MULTI_SELECTED_ELEMENT_KEYS",t.HOVERED_ELEMENT_KEY="HOVERED_ELEMENT_KEY",t.TEXT_EDIT="TEXT_EDIT",t.HOT_RELOADING="HOT_RELOADING",t.IS_FLUSHING="IS_FLUSHING",t.NAV_TREE_CALLBACKS="NAV_TREE_CALLBACKS",t.ELEMENT_KEY_TO_LOOKUP_LIST="ELEMENT_KEY_TO_LOOKUP_LIST",t.ELEMENT_KEY_TO_NAV_NODE="ELEMENT_KEY_TO_NAV_NODE",t.CURRENT_NAV_TREE="CURRENT_NAV_TREE";let n={};t.getMemoryStorageItem=e=>n[e],t.setMemoryStorageItem=(e,t)=>{n[e]=t,t||delete n[e]},t.removeMemoryStorageItem=e=>{delete n[e]},t.getSessionStorageItem=(e,t)=>sessionStorage.getItem(`${t}_${e}`),t.setSessionStorageItem=(e,n,r)=>{if(!n){(0,t.removeSessionStorageItem)(e,r);return}sessionStorage.setItem(`${r}_${e}`,n)},t.removeSessionStorageItem=(e,t)=>{sessionStorage.removeItem(`${t}_${e}`)}},7928:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TempoElement=void 0;let n="STORYBOARD-TOP-CONSTANT";class r{constructor(e,t,n){if(e&&/[^0-9a-zA-Z-_]/.test(e))throw Error("Codebase ID contains invalid chars :"+e);if(t&&/[^0-9a-zA-Z-_]/.test(t))throw Error("Storyboard ID contains invalid chars :"+e);this.codebaseId=e||"",this.storyboardId=t,this.uniquePath=n,this.cachedKey=null}isEqual(e){return this.codebaseId===e.codebaseId&&this.storyboardId===e.storyboardId&&this.uniquePath===e.uniquePath}getKey(){return this.cachedKey||(this.cachedKey=`TE_${this.codebaseId}_${this.storyboardId}_${this.uniquePath}`),this.cachedKey}isEmpty(){return!this.storyboardId||!this.uniquePath}static fromKey(e){if(!e)return r.empty();let[t,n,a,o]=e.split("_");return a&&o?new r(n,a,o):r.empty()}static fromOtherElement(e){return new r(e.codebaseId,e.storyboardId,e.uniquePath)}static empty(){return new r("","","")}static forStoryboard(e){return new r(n,e,"0")}isStoryboard(e){return this.codebaseId===n&&(!e||this.storyboardId===e)}isKnownElement(){return!this.isEmpty()&&!!this.codebaseId&&this.codebaseId!==n}isParentOf(e){return!!e&&(this.isStoryboard()?this.storyboardId===e.storyboardId&&this.uniquePath!==e.uniquePath:this.storyboardId===e.storyboardId&&this.uniquePath!==e.uniquePath&&!!this.uniquePath&&!!e.uniquePath&&e.uniquePath.startsWith(this.uniquePath))}isSiblingOf(e){if(!e||!this.uniquePath||!e.uniquePath||this.isEqual(e))return!1;if(this.isStoryboard())return e.isStoryboard();let t=this.uniquePath.split("-").slice(0,-1).join("-"),n=e.uniquePath.split("-").slice(0,-1).join("-");return this.storyboardId===e.storyboardId&&this.uniquePath!==e.uniquePath&&!!t&&!!n&&t===n}}t.TempoElement=r},8022:function(e,t,n){"use strict";t.l=void 0;var r=n(8425);Object.defineProperty(t,"l",{enumerable:!0,get:function(){return r.TempoDevtools}})},5983:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={init:()=>{var e,t,n,r,a,o;e=document,(t=window.posthog||[]).__SV||(window.posthog=t,t._i=[],t.init=function(s,l,i){(a=e.createElement("script")).type="text/javascript",a.async=!0,a.src=l.api_host+"/static/array.js",(o=e.getElementsByTagName("script")[0]).parentNode.insertBefore(a,o);var d=t;for(void 0!==i?d=t[i]=[]:i="posthog",d.people=d.people||[],d.toString=function(e){var t="posthog";return"posthog"!==i&&(t+="."+i),e||(t+=" (stub)"),t},d.people.toString=function(){return d.toString(1)+".people (stub)"},n="capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys getNextSurveyStep onSessionId".split(" "),r=0;r<n.length;r++)(function(e,t){var n=t.split(".");2==n.length&&(e=e[n[0]],t=n[1]),e[t]=function(){e.push([t].concat(Array.prototype.slice.call(arguments,0)))}})(d,n[r]);t._i.push([s,l,i])},t.__SV=1),posthog.init("phc_jjpEvBVV0R2mp44ePAL8Yt4jdtX5HW1lc493rkpUwwa",{api_host:"https://us.i.posthog.com",person_profiles:"identified_only",session_recording:{recordCrossOriginIframes:!0,maskAllInputs:!1,maskInputOptions:{password:!0}}})}}},71:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DebounceExecutor=void 0;class n{constructor(){this.animationFrameId=null}schedule(e){null!==this.animationFrameId&&cancelAnimationFrame(this.animationFrameId),this.animationFrameId=requestAnimationFrame(()=>{let t=performance.now();e();let n=performance.now()-t;n>16&&console.warn(`Took ${n.toFixed(2)}ms to execute, which may affect app responsiveness`),this.animationFrameId=null})}}t.DebounceExecutor=n},3465:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NIL",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(t,"parse",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(t,"v1",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(t,"v3",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(t,"v4",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"v5",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(t,"validate",{enumerable:!0,get:function(){return d.default}}),Object.defineProperty(t,"version",{enumerable:!0,get:function(){return i.default}});var r=p(n(4264)),a=p(n(7505)),o=p(n(4697)),s=p(n(9182)),l=p(n(310)),i=p(n(330)),d=p(n(4997)),u=p(n(5627)),c=p(n(1284));function p(e){return e&&e.__esModule?e:{default:e}}},6376:function(e,t){"use strict";function n(e){return(e+64>>>9<<4)+14+1}function r(e,t){let n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function a(e,t,n,a,o,s){var l;return r((l=r(r(t,e),r(a,s)))<<o|l>>>32-o,n)}function o(e,t,n,r,o,s,l){return a(t&n|~t&r,e,t,o,s,l)}function s(e,t,n,r,o,s,l){return a(t&r|n&~r,e,t,o,s,l)}function l(e,t,n,r,o,s,l){return a(t^n^r,e,t,o,s,l)}function i(e,t,n,r,o,s,l){return a(n^(t|~r),e,t,o,s,l)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default=function(e){if("string"==typeof e){let t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(let n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return function(e){let t=[],n=32*e.length,r="0123456789abcdef";for(let a=0;a<n;a+=8){let n=e[a>>5]>>>a%32&255,o=parseInt(r.charAt(n>>>4&15)+r.charAt(15&n),16);t.push(o)}return t}(function(e,t){e[t>>5]|=128<<t%32,e[n(t)-1]=t;let a=1732584193,d=-271733879,u=-1732584194,c=271733878;for(let t=0;t<e.length;t+=16){let n=a,p=d,E=u,f=c;a=o(a,d,u,c,e[t],7,-680876936),c=o(c,a,d,u,e[t+1],12,-389564586),u=o(u,c,a,d,e[t+2],17,606105819),d=o(d,u,c,a,e[t+3],22,-1044525330),a=o(a,d,u,c,e[t+4],7,-176418897),c=o(c,a,d,u,e[t+5],12,1200080426),u=o(u,c,a,d,e[t+6],17,-1473231341),d=o(d,u,c,a,e[t+7],22,-45705983),a=o(a,d,u,c,e[t+8],7,1770035416),c=o(c,a,d,u,e[t+9],12,-1958414417),u=o(u,c,a,d,e[t+10],17,-42063),d=o(d,u,c,a,e[t+11],22,-1990404162),a=o(a,d,u,c,e[t+12],7,1804603682),c=o(c,a,d,u,e[t+13],12,-40341101),u=o(u,c,a,d,e[t+14],17,-1502002290),d=o(d,u,c,a,e[t+15],22,1236535329),a=s(a,d,u,c,e[t+1],5,-165796510),c=s(c,a,d,u,e[t+6],9,-1069501632),u=s(u,c,a,d,e[t+11],14,643717713),d=s(d,u,c,a,e[t],20,-373897302),a=s(a,d,u,c,e[t+5],5,-701558691),c=s(c,a,d,u,e[t+10],9,38016083),u=s(u,c,a,d,e[t+15],14,-660478335),d=s(d,u,c,a,e[t+4],20,-405537848),a=s(a,d,u,c,e[t+9],5,568446438),c=s(c,a,d,u,e[t+14],9,-1019803690),u=s(u,c,a,d,e[t+3],14,-187363961),d=s(d,u,c,a,e[t+8],20,1163531501),a=s(a,d,u,c,e[t+13],5,-1444681467),c=s(c,a,d,u,e[t+2],9,-51403784),u=s(u,c,a,d,e[t+7],14,1735328473),d=s(d,u,c,a,e[t+12],20,-1926607734),a=l(a,d,u,c,e[t+5],4,-378558),c=l(c,a,d,u,e[t+8],11,-2022574463),u=l(u,c,a,d,e[t+11],16,1839030562),d=l(d,u,c,a,e[t+14],23,-35309556),a=l(a,d,u,c,e[t+1],4,-1530992060),c=l(c,a,d,u,e[t+4],11,1272893353),u=l(u,c,a,d,e[t+7],16,-155497632),d=l(d,u,c,a,e[t+10],23,-1094730640),a=l(a,d,u,c,e[t+13],4,681279174),c=l(c,a,d,u,e[t],11,-358537222),u=l(u,c,a,d,e[t+3],16,-722521979),d=l(d,u,c,a,e[t+6],23,76029189),a=l(a,d,u,c,e[t+9],4,-640364487),c=l(c,a,d,u,e[t+12],11,-421815835),u=l(u,c,a,d,e[t+15],16,530742520),d=l(d,u,c,a,e[t+2],23,-995338651),a=i(a,d,u,c,e[t],6,-198630844),c=i(c,a,d,u,e[t+7],10,1126891415),u=i(u,c,a,d,e[t+14],15,-1416354905),d=i(d,u,c,a,e[t+5],21,-57434055),a=i(a,d,u,c,e[t+12],6,1700485571),c=i(c,a,d,u,e[t+3],10,-1894986606),u=i(u,c,a,d,e[t+10],15,-1051523),d=i(d,u,c,a,e[t+1],21,-2054922799),a=i(a,d,u,c,e[t+8],6,1873313359),c=i(c,a,d,u,e[t+15],10,-30611744),u=i(u,c,a,d,e[t+6],15,-1560198380),d=i(d,u,c,a,e[t+13],21,1309151649),a=i(a,d,u,c,e[t+4],6,-145523070),c=i(c,a,d,u,e[t+11],10,-1120210379),u=i(u,c,a,d,e[t+2],15,718787259),d=i(d,u,c,a,e[t+9],21,-343485551),a=r(a,n),d=r(d,p),u=r(u,E),c=r(c,f)}return[a,d,u,c]}(function(e){if(0===e.length)return[];let t=8*e.length,r=new Uint32Array(n(t));for(let n=0;n<t;n+=8)r[n>>5]|=(255&e[n/8])<<n%32;return r}(e),8*e.length))}},7174:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;let n="undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto);t.default={randomUUID:n}},310:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default="00000000-0000-0000-0000-000000000000"},1284:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,a=(r=n(4997))&&r.__esModule?r:{default:r};t.default=function(e){let t;if(!(0,a.default)(e))throw TypeError("Invalid UUID");let n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n}},2935:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i},4994:function(e,t){"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){if(!n&&!(n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return n(r)};let r=new Uint8Array(16)},2066:function(e,t){"use strict";function n(e,t){return e<<t|e>>>32-t}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default=function(e){let t=[1518500249,1859775393,2400959708,3395469782],r=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof e){let t=unescape(encodeURIComponent(e));e=[];for(let n=0;n<t.length;++n)e.push(t.charCodeAt(n))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);let a=Math.ceil((e.length/4+2)/16),o=Array(a);for(let t=0;t<a;++t){let n=new Uint32Array(16);for(let r=0;r<16;++r)n[r]=e[64*t+4*r]<<24|e[64*t+4*r+1]<<16|e[64*t+4*r+2]<<8|e[64*t+4*r+3];o[t]=n}o[a-1][14]=(e.length-1)*8/4294967296,o[a-1][14]=Math.floor(o[a-1][14]),o[a-1][15]=(e.length-1)*8&4294967295;for(let e=0;e<a;++e){let a=new Uint32Array(80);for(let t=0;t<16;++t)a[t]=o[e][t];for(let e=16;e<80;++e)a[e]=n(a[e-3]^a[e-8]^a[e-14]^a[e-16],1);let s=r[0],l=r[1],i=r[2],d=r[3],u=r[4];for(let e=0;e<80;++e){let r=Math.floor(e/20),o=n(s,5)+function(e,t,n,r){switch(e){case 0:return t&n^~t&r;case 1:case 3:return t^n^r;case 2:return t&n^t&r^n&r}}(r,l,i,d)+u+t[r]+a[e]>>>0;u=d,d=i,i=n(l,30)>>>0,l=s,s=o}r[0]=r[0]+s>>>0,r[1]=r[1]+l>>>0,r[2]=r[2]+i>>>0,r[3]=r[3]+d>>>0,r[4]=r[4]+u>>>0}return[r[0]>>24&255,r[0]>>16&255,r[0]>>8&255,255&r[0],r[1]>>24&255,r[1]>>16&255,r[1]>>8&255,255&r[1],r[2]>>24&255,r[2]>>16&255,r[2]>>8&255,255&r[2],r[3]>>24&255,r[3]>>16&255,r[3]>>8&255,255&r[3],r[4]>>24&255,r[4]>>16&255,r[4]>>8&255,255&r[4]]}},5627:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.unsafeStringify=s;var r,a=(r=n(4997))&&r.__esModule?r:{default:r};let o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));function s(e,t=0){return o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]}t.default=function(e,t=0){let n=s(e,t);if(!(0,a.default)(n))throw TypeError("Stringified UUID is invalid");return n}},4264:function(e,t,n){"use strict";let r,a;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o,s=(o=n(4994))&&o.__esModule?o:{default:o},l=n(5627);let i=0,d=0;t.default=function(e,t,n){let o=t&&n||0,u=t||Array(16),c=(e=e||{}).node||r,p=void 0!==e.clockseq?e.clockseq:a;if(null==c||null==p){let t=e.random||(e.rng||s.default)();null==c&&(c=r=[1|t[0],t[1],t[2],t[3],t[4],t[5]]),null==p&&(p=a=(t[6]<<8|t[7])&16383)}let E=void 0!==e.msecs?e.msecs:Date.now(),f=void 0!==e.nsecs?e.nsecs:d+1,m=E-i+(f-d)/1e4;if(m<0&&void 0===e.clockseq&&(p=p+1&16383),(m<0||E>i)&&void 0===e.nsecs&&(f=0),f>=1e4)throw Error("uuid.v1(): Can't create more than 10M uuids/sec");i=E,d=f,a=p;let h=((268435455&(E+=122192928e5))*1e4+f)%4294967296;u[o++]=h>>>24&255,u[o++]=h>>>16&255,u[o++]=h>>>8&255,u[o++]=255&h;let _=E/4294967296*1e4&268435455;u[o++]=_>>>8&255,u[o++]=255&_,u[o++]=_>>>24&15|16,u[o++]=_>>>16&255,u[o++]=p>>>8|128,u[o++]=255&p;for(let e=0;e<6;++e)u[o+e]=c[e];return t||(0,l.unsafeStringify)(u)}},7505:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(8626)),a=o(n(6376));function o(e){return e&&e.__esModule?e:{default:e}}let s=(0,r.default)("v3",48,a.default);t.default=s},8626:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.URL=t.DNS=void 0,t.default=function(e,t,n){function r(e,r,s,l){var i;if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));let t=[];for(let n=0;n<e.length;++n)t.push(e.charCodeAt(n));return t}(e)),"string"==typeof r&&(r=(0,o.default)(r)),(null===(i=r)||void 0===i?void 0:i.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let d=new Uint8Array(16+e.length);if(d.set(r),d.set(e,r.length),(d=n(d))[6]=15&d[6]|t,d[8]=63&d[8]|128,s){l=l||0;for(let e=0;e<16;++e)s[l+e]=d[e];return s}return(0,a.unsafeStringify)(d)}try{r.name=e}catch(e){}return r.DNS=s,r.URL=l,r};var r,a=n(5627),o=(r=n(1284))&&r.__esModule?r:{default:r};let s="6ba7b810-9dad-11d1-80b4-00c04fd430c8";t.DNS=s;let l="6ba7b811-9dad-11d1-80b4-00c04fd430c8";t.URL=l},4697:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=s(n(7174)),a=s(n(4994)),o=n(5627);function s(e){return e&&e.__esModule?e:{default:e}}t.default=function(e,t,n){if(r.default.randomUUID&&!t&&!e)return r.default.randomUUID();let s=(e=e||{}).random||(e.rng||a.default)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t){n=n||0;for(let e=0;e<16;++e)t[n+e]=s[e];return t}return(0,o.unsafeStringify)(s)}},9182:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(8626)),a=o(n(2066));function o(e){return e&&e.__esModule?e:{default:e}}let s=(0,r.default)("v5",80,a.default);t.default=s},4997:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,a=(r=n(2935))&&r.__esModule?r:{default:r};t.default=function(e){return"string"==typeof e&&a.default.test(e)}},330:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,a=(r=n(4997))&&r.__esModule?r:{default:r};t.default=function(e){if(!(0,a.default)(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)}},9974:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},322:function(e,t){"use strict";var n=this&&this.__assign||function(){return(n=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)};function r(e){return function(t,r){var a;return(a={})[t]=function(t){return n({type:e},t)},a[r]=function(t){return"object"==typeof t&&null!==t&&t.type===e},a}}Object.defineProperty(t,"__esModule",{value:!0}),t.ast=void 0,t.ast=n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n({},r("Selector")("selector","isSelector")),r("Rule")("rule","isRule")),r("TagName")("tagName","isTagName")),r("Id")("id","isId")),r("ClassName")("className","isClassName")),r("WildcardTag")("wildcardTag","isWildcardTag")),r("NamespaceName")("namespaceName","isNamespaceName")),r("WildcardNamespace")("wildcardNamespace","isWildcardNamespace")),r("NoNamespace")("noNamespace","isNoNamespace")),r("Attribute")("attribute","isAttribute")),r("PseudoClass")("pseudoClass","isPseudoClass")),r("PseudoElement")("pseudoElement","isPseudoElement")),r("String")("string","isString")),r("Formula")("formula","isFormula")),r("FormulaOfSelector")("formulaOfSelector","isFormulaOfSelector")),r("Substitution")("substitution","isSubstitution"))},4203:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ast=t.render=t.createParser=void 0;var r=n(1619);Object.defineProperty(t,"createParser",{enumerable:!0,get:function(){return r.createParser}});var a=n(4970);Object.defineProperty(t,"render",{enumerable:!0,get:function(){return a.render}});var o=n(322);Object.defineProperty(t,"ast",{enumerable:!0,get:function(){return o.ast}})},1489:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createRegularIndex=t.createMulticharIndex=t.emptyRegularIndex=t.emptyMulticharIndex=void 0,t.emptyMulticharIndex={},t.emptyRegularIndex={},t.createMulticharIndex=function(e){if(0===e.length)return t.emptyMulticharIndex;for(var n={},r=0;r<e.length;r++)!function(e,t){for(var n=t,r=0;r<e.length;r++){var a=r===e.length-1,o=e.charAt(r),s=n[o]||(n[o]={chars:{}});a&&(s.self=e),n=s.chars}}(e[r],n);return n},t.createRegularIndex=function(e){if(0===e.length)return t.emptyRegularIndex;for(var n={},r=0;r<e.length;r++)n[e[r]]=!0;return n}},1619:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createParser=void 0;var r=n(1489),a=n(9822),o=n(3946),s=n(8471),l="css-selector-parser parse error: ";t.createParser=function(e){void 0===e&&(e={});var t=e.syntax,n=void 0===t?"latest":t,i=e.substitutes,d=e.strict,u=void 0===d||d,c="object"==typeof n?n:o.cssSyntaxDefinitions[n];c.baseSyntax&&(c=(0,o.extendSyntaxDefinition)(o.cssSyntaxDefinitions[c.baseSyntax],c));var p=c.tag?[!0,!!(0,o.getXmlOptions)(c.tag).wildcard]:[!1,!1],E=p[0],f=p[1],m=!!c.ids,h=!!c.classNames,_=!!c.namespace,g=c.namespace&&(!0===c.namespace||!0===c.namespace.wildcard);if(_&&!E)throw Error("".concat(l,"Namespaces cannot be enabled while tags are disabled."));var T=!!i,y=c.combinators?(0,r.createMulticharIndex)(c.combinators):r.emptyMulticharIndex,S=c.attributes?[!0,c.attributes.operators?(0,r.createMulticharIndex)(c.attributes.operators):r.emptyMulticharIndex,c.attributes.caseSensitivityModifiers?(0,r.createRegularIndex)(c.attributes.caseSensitivityModifiers):r.emptyRegularIndex,"accept"===c.attributes.unknownCaseSensitivityModifiers]:[!1,r.emptyMulticharIndex,r.emptyRegularIndex,!1],v=S[0],I=S[1],A=S[2],N=S[3],C=N||Object.keys(A).length>0,O=c.pseudoClasses?[!0,c.pseudoClasses.definitions?(0,a.calculatePseudoSignatures)(c.pseudoClasses.definitions):a.emptyPseudoSignatures,"accept"===c.pseudoClasses.unknown]:[!1,a.emptyPseudoSignatures,!1],b=O[0],R=O[1],L=O[2],P=c.pseudoElements?[!0,"singleColon"===c.pseudoElements.notation||"both"===c.pseudoElements.notation,!c.pseudoElements.notation||"doubleColon"===c.pseudoElements.notation||"both"===c.pseudoElements.notation,c.pseudoElements.definitions?(0,a.calculatePseudoSignatures)(Array.isArray(c.pseudoElements.definitions)?{NoArgument:c.pseudoElements.definitions}:c.pseudoElements.definitions):a.emptyPseudoSignatures,"accept"===c.pseudoElements.unknown]:[!1,!1,!1,a.emptyPseudoSignatures,!1],M=P[0],D=P[1],U=P[2],w=P[3],F=P[4],x="",K=x.length,j=0,Y="",k=function(e){return Y===e},H=function(){return k("*")||(0,s.isIdentStart)(Y)},$=function(e){j=e,Y=x.charAt(j)},B=function(){j++,Y=x.charAt(j)},V=function(){var e=Y;return j++,Y=x.charAt(j),e};function X(e){var t=Math.min(K-1,j),n=Error("".concat(l).concat(e," Pos: ").concat(t,"."));throw n.position=t,n.name="ParserError",n}function W(e,t){if(!e)return X(t)}var q=function(){W(j<K,"Unexpected end of input.")},G=function(){return j>=K},J=function(e){W(j<K,'Expected "'.concat(e,'" but end of input reached.')),W(Y===e,'Expected "'.concat(e,'" but "').concat(Y,'" found.')),j++,Y=x.charAt(j)};function z(e){var t=function e(t,n){var r=t[x.charAt(n)];if(r){var a=e(r.chars,n+1);if(a)return a;if(r.self)return r.self}}(e,j);if(t)return j+=t.length,Y=x.charAt(j),t}function Q(){for(var e=V(),t=1;(0,s.isHex)(Y)&&t<s.maxHexLength;)e+=V(),t++;return function(){if(" "===Y||"	"===Y||"\f"===Y||"\n"===Y){B();return}"\r"===Y&&B(),"\n"===Y&&B()}(),String.fromCharCode(parseInt(e,16))}function Z(){if(!(0,s.isIdentStart)(Y))return null;for(var e="";k("-");)e+=Y,B();for("-"!==e||(0,s.isIdent)(Y)||k("\\")||X("Identifiers cannot consist of a single hyphen."),u&&e.length>=2&&X("Identifiers cannot start with two hyphens with strict mode on."),s.digitsChars[Y]&&X("Identifiers cannot start with hyphens followed by digits.");j<K;)if((0,s.isIdent)(Y))e+=V();else if(k("\\"))B(),q(),(0,s.isHex)(Y)?e+=Q():e+=V();else break;return e}function ee(){for(;s.whitespaceChars[Y];)B()}function et(e){void 0===e&&(e=!1),ee();for(var t=[es(e)];k(",");)B(),ee(),t.push(es(e));return{type:"Selector",rules:t}}function en(){for(var e="";s.digitsChars[Y];)e+=V();return W(""!==e,"Formula parse error."),parseInt(e)}function er(e,t,n){var r;if(k("(")){if(B(),ee(),T&&k("$")){B();var a=Z();W(a,"Expected substitute name."),r={type:"Substitution",name:a}}else if("String"===n.type)W((r={type:"String",value:function(){for(var e="";j<K&&!k(")");)if(k("\\")){if(B(),G()&&!u)return(e+"\\").trim();q(),(0,s.isHex)(Y)?e+=Q():e+=V()}else e+=V();return e.trim()}()}).value,"Expected ".concat(t," argument value."));else if("Selector"===n.type)r=et(!0);else{if("Formula"!==n.type)return X("Invalid ".concat(t," signature."));var o=function(){if(k("e")||k("o")){var e,t=Z();if("even"===t)return ee(),[2,0];if("odd"===t)return ee(),[2,1]}var n=null,r=1;if(k("-")&&(B(),r=-1),(k("-")||k("+")||s.digitsChars[Y])&&(k("+")&&B(),n=en(),!k("\\")&&!k("n")))return[0,n*r];if(null===n&&(n=1),n*=r,k("\\")?(B(),e=(0,s.isHex)(Y)?Q():V()):e=V(),W("n"===e,'Formula parse error: expected "n".'),ee(),!(k("+")||k("-")))return[n,0];var a=k("+")?1:-1;return B(),ee(),[n,a*en()]}(),l=o[0],i=o[1];r={type:"Formula",a:l,b:i},n.ofSelector&&(ee(),(k("o")||k("\\"))&&(W("of"===Z(),"Formula of selector parse error."),ee(),r={type:"FormulaOfSelector",a:l,b:i,selector:es()}))}if(ee(),G()&&!u)return r;J(")")}else W(n.optional,"Argument is required for ".concat(t,' "').concat(e,'".'));return r}function ea(){if(k("*"))return W(f,"Wildcard tag name is not enabled."),B(),{type:"WildcardTag"};if(!(0,s.isIdentStart)(Y))return X("Expected tag name.");W(E,"Tag names are not enabled.");var e=Z();return W(e,"Expected tag name."),{type:"TagName",name:e}}function eo(){if(k("*")){var e=j;if(B(),!k("|")||(B(),!H()))return $(e),ea();W(_,"Namespaces are not enabled."),W(g,"Wildcard namespace is not enabled.");var t=ea();return t.namespace={type:"WildcardNamespace"},t}if(k("|")){W(_,"Namespaces are not enabled."),B();var t=ea();return t.namespace={type:"NoNamespace"},t}if(!(0,s.isIdentStart)(Y))return X("Expected tag name.");var n=Z();if(W(n,"Expected tag name."),!k("|"))return W(E,"Tag names are not enabled."),{type:"TagName",name:n};var e=j;if(B(),!H())return $(e),{type:"TagName",name:n};W(_,"Namespaces are not enabled.");var t=ea();return t.namespace={type:"NamespaceName",name:n},t}function es(e){void 0===e&&(e=!1);var t,n,r={type:"Rule",items:[]};if(e){var o=z(y);o&&(r.combinator=o,ee())}for(;j<K;)if(H())W(0===r.items.length,"Unexpected tag/namespace start."),r.items.push(eo());else if(k("|")){var l=j;if(B(),H())W(0===r.items.length,"Unexpected tag/namespace start."),$(l),r.items.push(eo());else{$(l);break}}else if(k(".")){W(h,"Class names are not enabled."),B();var i=Z();W(i,"Expected class name."),r.items.push({type:"ClassName",name:i})}else if(k("#")){W(m,"IDs are not enabled."),B();var d=Z();W(d,"Expected ID name."),r.items.push({type:"Id",name:d})}else if(k("["))W(v,"Attributes are not enabled."),r.items.push(function(){if(J("["),ee(),k("|")){W(_,"Namespaces are not enabled."),B();var e,t=Z();W(t,"Expected attribute name."),e={type:"Attribute",name:t,namespace:{type:"NoNamespace"}}}else if(k("*")){W(_,"Namespaces are not enabled."),W(g,"Wildcard namespace is not enabled."),B(),J("|");var n=Z();W(n,"Expected attribute name."),e={type:"Attribute",name:n,namespace:{type:"WildcardNamespace"}}}else{var r=Z();if(W(r,"Expected attribute name."),e={type:"Attribute",name:r},k("|")){var a=j;if(B(),(0,s.isIdentStart)(Y)){W(_,"Namespaces are not enabled.");var o=Z();W(o,"Expected attribute name."),e={type:"Attribute",name:o,namespace:{type:"NamespaceName",name:r}}}else $(a)}}if(W(e.name,"Expected attribute name."),ee(),G()&&!u)return e;if(k("]"))B();else{if(e.operator=z(I),W(e.operator,"Expected a valid attribute selector operator."),ee(),q(),s.quoteChars[Y])e.value={type:"String",value:function(e){var t="";for(J(e);j<K;){if(k(e)){B();break}k("\\")?(B(),k(e)?(t+=e,B()):"\n"===Y||"\f"===Y?B():"\r"===Y?(B(),k("\n")&&B()):(0,s.isHex)(Y)?t+=Q():(t+=Y,B())):(t+=Y,B())}return t}(Y)};else if(T&&k("$")){B();var l=Z();W(l,"Expected substitute name."),e.value={type:"Substitution",name:l}}else{var i=Z();W(i,"Expected attribute value."),e.value={type:"String",value:i}}if(ee(),G()&&!u)return e;if(!k("]")){var d=Z();if(W(d,"Expected end of attribute selector."),e.caseSensitivityModifier=d,W(C,"Attribute case sensitivity modifiers are not enabled."),W(N||A[e.caseSensitivityModifier],"Unknown attribute case sensitivity modifier."),ee(),G()&&!u)return e}J("]")}return e}());else if(k(":")){var c=!1;B(),k(":")&&(W(M,"Pseudo elements are not enabled."),W(U,"Pseudo elements double colon notation is not enabled."),c=!0,B());var p=Z();if(W(c||p,"Expected pseudo-class name."),W(!c||p,"Expected pseudo-element name."),W(p,"Expected pseudo-class name."),W(!c||F||Object.prototype.hasOwnProperty.call(w,p),'Unknown pseudo-element "'.concat(p,'".')),M&&(c||!c&&D&&Object.prototype.hasOwnProperty.call(w,p))){var E=null!==(t=w[p])&&void 0!==t?t:F&&a.defaultPseudoSignature,f={type:"PseudoElement",name:p},S=er(p,"pseudo-element",E);S&&(W("Formula"!==S.type&&"FormulaOfSelector"!==S.type,"Pseudo-elements cannot have formula argument."),f.argument=S),r.items.push(f)}else{W(b,"Pseudo-classes are not enabled.");var E=null!==(n=R[p])&&void 0!==n?n:L&&a.defaultPseudoSignature;W(E,'Unknown pseudo-class: "'.concat(p,'".'));var S=er(p,"pseudo-class",E),O={type:"PseudoClass",name:p};S&&(O.argument=S),r.items.push(O)}}else break;if(0===r.items.length)return G()?X("Expected rule but end of input reached."):X('Expected rule but "'.concat(Y,'" found.'));if(ee(),!G()&&!k(",")&&!k(")")){var o=z(y);ee(),r.nestedRule=es(),r.nestedRule.combinator=o}return r}return function(e){if("string"!=typeof e)throw Error("".concat(l,"Expected string input."));return K=(x=e).length,j=0,Y=x.charAt(0),et()}}},9822:function(e,t){"use strict";function n(e){for(var t={},n=0,r=Object.keys(e);n<r.length;n++){var a=r[n],o=e[a];if(o)for(var s=0;s<o.length;s++){var l=o[s];(t[l]||(t[l]=[])).push(a)}}return t}Object.defineProperty(t,"__esModule",{value:!0}),t.calculatePseudoSignatures=t.inverseCategories=t.defaultPseudoSignature=t.emptyPseudoSignatures=void 0,t.emptyPseudoSignatures={},t.defaultPseudoSignature={type:"String",optional:!0},t.inverseCategories=n,t.calculatePseudoSignatures=function(e){for(var t=n(e),r={},a=0,o=Object.keys(t);a<o.length;a++){var s=o[a],l=t[s];l&&(r[s]=function(e){var t={type:"NoArgument",optional:!1};function n(e){if(t.type&&t.type!==e&&"NoArgument"!==t.type)throw Error('Conflicting pseudo-class argument type: "'.concat(t.type,'" vs "').concat(e,'".'));t.type=e}for(var r=0;r<e.length;r++){var a=e[r];"NoArgument"===a&&(t.optional=!0),"Formula"===a&&n("Formula"),"FormulaOfSelector"===a&&(n("Formula"),t.ofSelector=!0),"String"===a&&n("String"),"Selector"===a&&n("Selector")}return t}(l))}return r}},4970:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.render=void 0;var r=n(8471),a="css-selector-parser render error: ";function o(e){if("WildcardNamespace"===e.type)return"*|";if("NamespaceName"===e.type)return"".concat((0,r.escapeIdentifier)(e.name),"|");if("NoNamespace"===e.type)return"|";throw Error("".concat(a,"Unknown namespace type: ").concat(e.type,"."))}function s(e,t){if(!e)return String(t);var n="".concat(1===e?"":-1===e?"-":e,"n");return t&&(n+="".concat(t>0?"+":"").concat(t)),n}t.render=function e(t){if("Selector"===t.type)return t.rules.map(e).join(", ");if("Rule"===t.type){var n="",l=t.items,i=t.combinator,d=t.nestedRule;i&&(n+="".concat(i," "));for(var u=0;u<l.length;u++)n+=e(l[u]);return d&&(n+=" ".concat(e(d))),n}if("TagName"===t.type||"WildcardTag"===t.type){var n="",c=t.namespace;return c&&(n+=o(c)),"TagName"===t.type?n+=(0,r.escapeIdentifier)(t.name):"WildcardTag"===t.type&&(n+="*"),n}if("Id"===t.type)return"#".concat((0,r.escapeIdentifier)(t.name));if("ClassName"===t.type)return".".concat((0,r.escapeIdentifier)(t.name));if("Attribute"===t.type){var p=t.name,c=t.namespace,E=t.operator,f=t.value,m=t.caseSensitivityModifier,n="[";if(c&&(n+=o(c)),n+=(0,r.escapeIdentifier)(p),E&&f){if(n+=E,"String"===f.type)n+=(0,r.escapeString)(f.value);else if("Substitution"===f.type)n+="$".concat((0,r.escapeIdentifier)(f.name));else throw Error("Unknown attribute value type: ".concat(f.type,"."));m&&(n+=" ".concat((0,r.escapeIdentifier)(m)))}return n+"]"}if("PseudoClass"===t.type){var h=t.name,_=t.argument,n=":".concat((0,r.escapeIdentifier)(h));return _&&(n+="(".concat("String"===_.type?(0,r.escapeIdentifier)(_.value):e(_),")")),n}if("PseudoElement"===t.type){var g=t.name,_=t.argument,n="::".concat((0,r.escapeIdentifier)(g));return _&&(n+="(".concat("String"===_.type?(0,r.escapeIdentifier)(_.value):e(_),")")),n}else if("String"===t.type)throw Error("".concat(a,"String cannot be rendered outside of context."));else if("Formula"===t.type)return s(t.a,t.b);else if("FormulaOfSelector"===t.type)return s(t.a,t.b)+" of "+e(t.selector);else if("Substitution"===t.type)return"$".concat((0,r.escapeIdentifier)(t.name));throw Error("Unknown type specified to render method: ".concat(t.type,"."))}},3946:function(e,t){"use strict";var n,r,a=this&&this.__assign||function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.cssSyntaxDefinitions=t.extendSyntaxDefinition=t.getXmlOptions=void 0;var o={},s={wildcard:!0};function l(e,t){return function(n,r){return!0===r?e:t(!0===n?e:n,r)}}function i(e){return function(t,n){if(!n||!t)return n;if("object"!=typeof n||null===n)throw Error("Unexpected syntax definition extension type: ".concat(n,"."));for(var r=a({},t),o=0,s=Object.entries(n);o<s.length;o++){var l=s[o],i=l[0],d=l[1],u=e[i];r[i]=u(t[i],d)}return r}}function d(e,t){return void 0!==t?t:e}function u(e,t){return t?e?e.concat(t):t:e}function c(e,t){if(!t)return e;if(!e)return t;for(var n=a({},e),r=0,o=Object.entries(t);r<o.length;r++){var s=o[r],l=s[0],i=s[1];if(!i){delete n[l];continue}var d=e[l];if(!d){n[l]=i;continue}n[l]=d.concat(i)}return n}t.getXmlOptions=function(e){return e?"boolean"==typeof e?s:e:o},t.extendSyntaxDefinition=(r=i({baseSyntax:d,tag:l(s,i({wildcard:d})),ids:d,classNames:d,namespace:l(s,i({wildcard:d})),combinators:u,attributes:i({operators:u,caseSensitivityModifiers:u,unknownCaseSensitivityModifiers:d}),pseudoClasses:i({unknown:d,definitions:c}),pseudoElements:i({unknown:d,notation:d,definitions:(n=function(e){return Array.isArray(e)?{NoArgument:e}:e},function(e,t){return c(n(e),n(t))})})}),function(e,t){var n=r(e,t);if(!n)throw Error("Syntax definition cannot be null or undefined.");return n});var p={tag:{},ids:!0,classNames:!0,combinators:[],pseudoElements:{unknown:"reject",notation:"singleColon",definitions:["first-letter","first-line"]},pseudoClasses:{unknown:"reject",definitions:{NoArgument:["link","visited","active"]}}},E=(0,t.extendSyntaxDefinition)(p,{tag:{wildcard:!0},combinators:[">","+"],attributes:{unknownCaseSensitivityModifiers:"reject",operators:["=","~=","|="]},pseudoElements:{definitions:["before","after"]},pseudoClasses:{unknown:"reject",definitions:{NoArgument:["hover","focus","first-child"],String:["lang"]}}}),f=(0,t.extendSyntaxDefinition)(E,{namespace:{wildcard:!0},combinators:["~"],attributes:{operators:["^=","$=","*="]},pseudoElements:{notation:"both"},pseudoClasses:{definitions:{NoArgument:["root","last-child","first-of-type","last-of-type","only-child","only-of-type","empty","target","enabled","disabled","checked","indeterminate"],Formula:["nth-child","nth-last-child","nth-of-type","nth-last-of-type"],Selector:["not"]}}}),m=(0,t.extendSyntaxDefinition)(f,{combinators:["||"],attributes:{caseSensitivityModifiers:["i","I","s","S"]},pseudoClasses:{definitions:{NoArgument:["any-link","local-link","target-within","scope","current","past","future","focus-within","focus-visible","read-write","read-only","placeholder-shown","default","valid","invalid","in-range","out-of-range","required","optional","blank","user-invalid"],Formula:["nth-col","nth-last-col"],String:["dir"],FormulaOfSelector:["nth-child","nth-last-child"],Selector:["current","is","where","has"]}}}),h=(0,t.extendSyntaxDefinition)(m,{pseudoElements:{unknown:"accept"},pseudoClasses:{unknown:"accept"},attributes:{unknownCaseSensitivityModifiers:"accept"}});t.cssSyntaxDefinitions={css1:p,css2:E,css3:f,"selectors-3":f,"selectors-4":m,latest:m,progressive:h}},8471:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.escapeString=t.escapeIdentifier=t.maxHexLength=t.digitsChars=t.quoteChars=t.whitespaceChars=t.stringRenderEscapeChars=t.identEscapeChars=t.isHex=t.isIdent=t.isIdentStart=void 0,t.isIdentStart=function(e){return e>="a"&&e<="z"||e>="A"&&e<="Z"||"-"===e||"_"===e||"\\"===e||e>="\xa0"},t.isIdent=function(e){return e>="a"&&e<="z"||e>="A"&&e<="Z"||e>="0"&&e<="9"||"-"===e||"_"===e||e>="\xa0"},t.isHex=function(e){return e>="a"&&e<="f"||e>="A"&&e<="F"||e>="0"&&e<="9"},t.identEscapeChars={"!":!0,'"':!0,"#":!0,$:!0,"%":!0,"&":!0,"'":!0,"(":!0,")":!0,"*":!0,"+":!0,",":!0,".":!0,"/":!0,";":!0,"<":!0,"=":!0,">":!0,"?":!0,"@":!0,"[":!0,"\\":!0,"]":!0,"^":!0,"`":!0,"{":!0,"|":!0,"}":!0,"~":!0},t.stringRenderEscapeChars={"\n":!0,"\r":!0,"	":!0,"\f":!0,"\v":!0},t.whitespaceChars={" ":!0,"	":!0,"\n":!0,"\r":!0,"\f":!0},t.quoteChars={'"':!0,"'":!0},t.digitsChars={0:!0,1:!0,2:!0,3:!0,4:!0,5:!0,6:!0,7:!0,8:!0,9:!0},t.maxHexLength=6,t.escapeIdentifier=function(e){for(var n=e.length,r="",a=0;a<n;){var o=e.charAt(a);if(t.identEscapeChars[o]||"-"===o&&1===a&&"-"===e.charAt(0))r+="\\"+o;else if("-"===o||"_"===o||o>="A"&&o<="Z"||o>="a"&&o<="z"||o>="0"&&o<="9"&&0!==a&&!(1===a&&"-"===e.charAt(0)))r+=o;else{var s=o.charCodeAt(0);if((63488&s)==55296){var l=e.charCodeAt(a++);if((64512&s)!=55296||(64512&l)!=56320)throw Error("UCS-2(decode): illegal sequence");s=((1023&s)<<10)+(1023&l)+65536}r+="\\"+s.toString(16)+" "}a++}return r.trim()},t.escapeString=function(e){for(var n=e.length,r="",a=0;a<n;){var o=e.charAt(a);'"'===o?o='\\"':"\\"===o?o="\\\\":t.stringRenderEscapeChars[o]&&(o="\\"+o.charCodeAt(0).toString(16)+(a===n-1?"":" ")),r+=o,a++}return'"'.concat(r,'"')}},8731:function(e,t,n){"use strict";n.r(t),n.d(t,{calculate:function(){return r},compare:function(){return o}});var r=function(e){var t,n,r,o,s=[];for(r=0,o=(t=e.split(",")).length;r<o;r+=1)(n=t[r]).length>0&&s.push(a(n));return s},a=function(e){var t,n,r=e,a={a:0,b:0,c:0},o=[];return n=function(t,n){var s,l,i,d,u,c;if(t.test(r))for(l=0,i=(s=r.match(t)).length;l<i;l+=1)a[n]+=1,d=s[l],u=r.indexOf(d),c=d.length,o.push({selector:e.substr(u,c),type:n,index:u,length:c}),r=r.replace(d,Array(c+1).join(" "))},(t=function(e){var t,n,a,o;if(e.test(r))for(n=0,a=(t=r.match(e)).length;n<a;n+=1)o=t[n],r=r.replace(o,Array(o.length+1).join("A"))})(/\\[0-9A-Fa-f]{6}\s?/g),t(/\\[0-9A-Fa-f]{1,5}\s/g),t(/\\./g),!function(){var e,t,n,a,o=/{[^]*/gm;if(o.test(r))for(t=0,n=(e=r.match(o)).length;t<n;t+=1)a=e[t],r=r.replace(a,Array(a.length+1).join(" "))}(),n(/(\[[^\]]+\])/g,"b"),n(/(#[^\#\s\+>~\.\[:\)]+)/g,"a"),n(/(\.[^\s\+>~\.\[:\)]+)/g,"b"),n(/(::[^\s\+>~\.\[:]+|:first-line|:first-letter|:before|:after)/gi,"c"),n(/(:(?!not|global|local)[\w-]+\([^\)]*\))/gi,"b"),n(/(:(?!not|global|local)[^\s\+>~\.\[:]+)/g,"b"),r=(r=(r=(r=(r=(r=r.replace(/[\*\s\+>~]/g," ")).replace(/[#\.]/g," ")).replace(/:not/g,"    ")).replace(/:local/g,"      ")).replace(/:global/g,"       ")).replace(/[\(\)]/g," "),n(/([^\s\+>~\.\[:]+)/g,"c"),o.sort(function(e,t){return e.index-t.index}),{selector:e,specificity:"0,"+a.a.toString()+","+a.b.toString()+","+a.c.toString(),specificityArray:[0,a.a,a.b,a.c],parts:o}},o=function(e,t){var n,r,o;if("string"==typeof e){if(-1!==e.indexOf(","))throw"Invalid CSS selector";n=a(e).specificityArray}else if(Array.isArray(e)){if(4!==e.filter(function(e){return"number"==typeof e}).length)throw"Invalid specificity array";n=e}else throw"Invalid CSS selector or specificity array";if("string"==typeof t){if(-1!==t.indexOf(","))throw"Invalid CSS selector";r=a(t).specificityArray}else if(Array.isArray(t)){if(4!==t.filter(function(e){return"number"==typeof e}).length)throw"Invalid specificity array";r=t}else throw"Invalid CSS selector or specificity array";for(o=0;o<4;o+=1){if(n[o]<r[o])return -1;if(n[o]>r[o])return 1}return 0}}}]);