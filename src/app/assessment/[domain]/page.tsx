"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import QuestionnaireForm from "@/components/assessment/QuestionnaireForm";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { getClient } from "@/lib/supabase/client";
import {
  formatDomainName,
  formatResponsesForDatabase,
  AssessmentResponse,
  calculateDomainScore
} from "@/lib/assessment";

export default function AssessmentPage() {
  const params = useParams();
  const router = useRouter();
  const domain = params.domain as string;
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use the enhanced formatDomainName function

  const handleSubmit = async (responses: Record<string, any>) => {
    try {
      setLoading(true);
      const supabase = getClient();

      // Get the current user
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("User not authenticated");
      }

      // Convert responses to AssessmentResponse format
      const assessmentResponses: AssessmentResponse[] = Object.entries(responses).map(([questionId, answer]) => ({
        questionId,
        answer,
        domain
      }));

      // Calculate domain score using enhanced logic
      const domainScoreResult = calculateDomainScore(domain, assessmentResponses);
      const domainScore = domainScoreResult.score;

      // Check if user already has individual results
      const { data: existingResults } = await supabase
        .from("individual_results")
        .select("*")
        .eq("user_id", user.id)
        .single();

      if (existingResults) {
        // Update existing results
        const updatedDomains = [...(existingResults.domains as any[])];
        const domainIndex = updatedDomains.findIndex(
          (d: any) => d.domain.toLowerCase() === domain.toLowerCase(),
        );

        if (domainIndex >= 0) {
          updatedDomains[domainIndex] = {
            ...updatedDomains[domainIndex],
            score: domainScore,
            responses: responses,
            subcategories: domainScoreResult.subcategories,
          };
        } else {
          updatedDomains.push({
            domain: formatDomainName(domain),
            score: domainScore,
            responses: responses,
            subcategories: domainScoreResult.subcategories,
          });
        }

        // Calculate overall score as average of domain scores
        const overallScore = Math.round(
          updatedDomains.reduce((sum, domainData: any) => sum + domainData.score, 0) /
            updatedDomains.length,
        );

        await supabase
          .from("individual_results")
          .update({
            domains: updatedDomains,
            overall_score: overallScore,
            updated_at: new Date().toISOString(),
          })
          .eq("id", existingResults.id as string);
      } else {
        // Create new results
        await supabase.from("individual_results").insert({
          user_id: user.id,
          domains: [
            {
              domain: formatDomainName(domain),
              score: domainScore,
              responses: responses,
              subcategories: domainScoreResult.subcategories,
            },
          ],
          overall_score: domainScore,
        });
      }

      // Redirect to couple dashboard
      router.push("/couple/dashboard");
    } catch (err) {
      console.error("Error saving assessment:", err);
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred while saving your assessment",
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (responses: Record<string, any>) => {
    // Similar to submit but without marking as completed
    // Implementation would be similar to handleSubmit but without changing status
    alert("Progress saved!");
  };

  return (
    <div className="container py-8">
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push("/dashboard")}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Dashboard
        </Button>
        <h1 className="text-3xl font-bold">
          {formatDomainName(domain)} Assessment
        </h1>
        <p className="text-muted-foreground">
          Complete the following questions to assess your {domain.toLowerCase()}{" "}
          compatibility.
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <QuestionnaireForm
        domain={domain}
        onSubmit={handleSubmit}
        onSave={handleSave}
      />
    </div>
  );
}
