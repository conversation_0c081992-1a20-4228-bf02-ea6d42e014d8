"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  fetchAllQuestions,
  addQuestion,
  updateQuestion,
  deleteQuestion,
} from "@/lib/assessment/supabaseQuestions";
import { Question } from "@/lib/assessment/questions";
import { createClient } from "@/lib/supabase/client";

export default function AdminQuestionsPage() {
  const router = useRouter();
  const [questions, setQuestions] = useState<Record<string, Question[]>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDomain, setSelectedDomain] = useState("finances");
  const [isAdmin, setIsAdmin] = useState(false);

  // Form state for adding/editing questions
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  const [formData, setFormData] = useState<{
    id: string;
    domain: string;
    type: "multiple-choice" | "scenario" | "ranking" | "open-ended" | "scale";
    text: string;
    options: string[];
    required: boolean;
  }>({
    id: "",
    domain: "finances",
    type: "multiple-choice",
    text: "",
    options: ["", "", "", ""],
    required: true,
  });

  // Check if user is admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        const supabase = createClient();

        // Get current user
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (!user) {
          router.push("/");
          return;
        }

        // Check if user is in admin_users table
        const { data, error } = await supabase
          .from("admin_users")
          .select("*")
          .eq("user_id", user.id)
          .single();

        if (error || !data) {
          setIsAdmin(false);
          router.push("/dashboard");
        } else {
          setIsAdmin(true);
          loadQuestions();
        }
      } catch (err) {
        console.error("Error checking admin status:", err);
        router.push("/dashboard");
      }
    };

    checkAdminStatus();
  }, [router]);

  // Load all questions
  const loadQuestions = async () => {
    try {
      setLoading(true);
      setError(null);
      const allQuestions = await fetchAllQuestions();
      setQuestions(allQuestions);
    } catch (err) {
      console.error("Error loading questions:", err);
      setError("Failed to load questions");
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  // Handle options change for multiple choice, scenario, and ranking questions
  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...formData.options];
    newOptions[index] = value;
    setFormData({
      ...formData,
      options: newOptions,
    });
  };

  // Add option field
  const addOption = () => {
    setFormData({
      ...formData,
      options: [...formData.options, ""],
    });
  };

  // Remove option field
  const removeOption = (index: number) => {
    const newOptions = formData.options.filter((_, i) => i !== index);
    setFormData({
      ...formData,
      options: newOptions,
    });
  };

  // Edit question
  const handleEditQuestion = (question: Question) => {
    setEditingQuestion(question);
    setFormData({
      id: question.id,
      domain: question.domain,
      type: question.type,
      text: question.text,
      options: question.options || ["", "", "", ""],
      required: question.required || false,
    });
  };

  // Save question (add or update)
  const handleSaveQuestion = async () => {
    try {
      // Validate form
      if (!formData.id || !formData.text || !formData.domain) {
        setError("Please fill in all required fields");
        return;
      }

      if (
        ["multiple-choice", "scenario", "ranking"].includes(formData.type) &&
        (!formData.options ||
          formData.options.filter((o) => o.trim()).length < 2)
      ) {
        setError("Please provide at least 2 options");
        return;
      }

      // Prepare question object
      const questionData: Question = {
        id: formData.id,
        domain: formData.domain,
        type: formData.type,
        text: formData.text,
        options: ["multiple-choice", "scenario", "ranking"].includes(
          formData.type,
        )
          ? formData.options.filter((o) => o.trim())
          : undefined,
        required: formData.required,
      };

      if (editingQuestion) {
        // Update existing question
        await updateQuestion(questionData);
      } else {
        // Add new question
        await addQuestion(questionData);
      }

      // Reset form and reload questions
      resetForm();
      await loadQuestions();
    } catch (err) {
      console.error("Error saving question:", err);
      setError("Failed to save question");
    }
  };

  // Delete question
  const handleDeleteQuestion = async (questionId: string) => {
    if (confirm("Are you sure you want to delete this question?")) {
      try {
        await deleteQuestion(questionId);
        await loadQuestions();
      } catch (err) {
        console.error("Error deleting question:", err);
        setError("Failed to delete question");
      }
    }
  };

  // Reset form
  const resetForm = () => {
    setEditingQuestion(null);
    setFormData({
      id: "",
      domain: selectedDomain,
      type: "multiple-choice",
      text: "",
      options: ["", "", "", ""],
      required: true,
    });
    setError(null);
  };

  if (!isAdmin) {
    return <div className="container py-8">Checking permissions...</div>;
  }

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">
        Assessment Questions Management
      </h1>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <Tabs defaultValue="view" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="view">View Questions</TabsTrigger>
          <TabsTrigger value="add">Add/Edit Question</TabsTrigger>
        </TabsList>

        <TabsContent value="view">
          <Card>
            <CardHeader>
              <CardTitle>Assessment Questions</CardTitle>
              <CardDescription>
                Select a domain to view its questions
              </CardDescription>
              <Select value={selectedDomain} onValueChange={setSelectedDomain}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select domain" />
                </SelectTrigger>
                <SelectContent>
                  {Object.keys(questions).map((domain) => (
                    <SelectItem key={domain} value={domain}>
                      {domain.charAt(0).toUpperCase() + domain.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardHeader>

            <CardContent>
              {loading ? (
                <p>Loading questions...</p>
              ) : questions[selectedDomain] &&
                questions[selectedDomain].length > 0 ? (
                <div className="space-y-4">
                  {questions[selectedDomain].map((question) => (
                    <Card key={question.id} className="border border-gray-200">
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-lg">
                            {question.text}
                          </CardTitle>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditQuestion(question)}
                            >
                              Edit
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleDeleteQuestion(question.id)}
                            >
                              Delete
                            </Button>
                          </div>
                        </div>
                        <CardDescription>
                          ID: {question.id} | Type: {question.type} | Required:{" "}
                          {question.required ? "Yes" : "No"}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        {question.options && question.options.length > 0 && (
                          <div>
                            <p className="font-medium mb-1">Options:</p>
                            <ul className="list-disc pl-5">
                              {question.options.map((option, index) => (
                                <li key={index}>{option}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <p>No questions found for this domain.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="add">
          <Card>
            <CardHeader>
              <CardTitle>
                {editingQuestion ? "Edit Question" : "Add New Question"}
              </CardTitle>
              <CardDescription>
                {editingQuestion
                  ? `Editing question ID: ${editingQuestion.id}`
                  : "Create a new assessment question"}
              </CardDescription>
            </CardHeader>

            <CardContent>
              <form className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="id">Question ID</Label>
                    <Input
                      id="id"
                      placeholder="e.g., finances_q5"
                      value={formData.id}
                      onChange={(e) => handleInputChange("id", e.target.value)}
                      disabled={!!editingQuestion}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="domain">Domain</Label>
                    <Select
                      value={formData.domain}
                      onValueChange={(value) =>
                        handleInputChange("domain", value)
                      }
                    >
                      <SelectTrigger id="domain">
                        <SelectValue placeholder="Select domain" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="finances">Finances</SelectItem>
                        <SelectItem value="vision">Vision</SelectItem>
                        <SelectItem value="parenting">Parenting</SelectItem>
                        <SelectItem value="communication">
                          Communication
                        </SelectItem>
                        <SelectItem value="roles">Roles</SelectItem>
                        <SelectItem value="sexuality">Sexuality</SelectItem>
                        <SelectItem value="spirituality">
                          Spirituality
                        </SelectItem>
                        <SelectItem value="darkside">Dark Side</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">Question Type</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value: any) =>
                      handleInputChange("type", value)
                    }
                  >
                    <SelectTrigger id="type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="multiple-choice">
                        Multiple Choice
                      </SelectItem>
                      <SelectItem value="scenario">Scenario</SelectItem>
                      <SelectItem value="ranking">Ranking</SelectItem>
                      <SelectItem value="open-ended">Open Ended</SelectItem>
                      <SelectItem value="scale">Scale</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="text">Question Text</Label>
                  <Textarea
                    id="text"
                    placeholder="Enter the question text"
                    value={formData.text}
                    onChange={(e) => handleInputChange("text", e.target.value)}
                    rows={3}
                  />
                </div>

                {["multiple-choice", "scenario", "ranking"].includes(
                  formData.type,
                ) && (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label>Options</Label>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addOption}
                      >
                        Add Option
                      </Button>
                    </div>

                    {formData.options.map((option, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Input
                          placeholder={`Option ${index + 1}`}
                          value={option}
                          onChange={(e) =>
                            handleOptionChange(index, e.target.value)
                          }
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeOption(index)}
                          disabled={formData.options.length <= 2}
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="required"
                    checked={formData.required}
                    onCheckedChange={(checked) =>
                      handleInputChange("required", checked === true)
                    }
                  />
                  <Label htmlFor="required">Required question</Label>
                </div>
              </form>
            </CardContent>

            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={resetForm}>
                {editingQuestion ? "Cancel" : "Clear"}
              </Button>
              <Button onClick={handleSaveQuestion}>
                {editingQuestion ? "Update Question" : "Add Question"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
