"use client";

import React, { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { getClient } from "@/lib/supabase/client";
import { Heart, Loader2, AlertCircle, UserCog as UserCogIcon, Settings } from "lucide-react";
import Link from "next/link";

function LoginPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const roleParam = searchParams.get("role");

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(
    searchParams.get("tab") === "register" ? "register" : "login",
  );
  const [userType, setUserType] = useState<"user" | "counselor" | "admin">(
    "user",
  );

  useEffect(() => {
    // Set user type based on URL parameter
    if (roleParam === "counselor") {
      setUserType("counselor");
    } else if (roleParam === "admin") {
      setUserType("admin");
    } else {
      setUserType("user");
    }

    // Check if user is already logged in
    const checkUserSessionAndRedirect = async () => {
      const supabase = getClient();
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError) {
        console.error("Error getting session:", sessionError);
        // Don't block login if session check fails, user can still try to log in
        return;
      }

      if (session) {
        setLoading(true); // Indicate activity
        try {
          console.log("Login Page: Active session found, fetching user role from /api/auth");
          const response = await fetch("/api/auth", {
            headers: {
              Authorization: `Bearer ${session.access_token}`,
            },
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: "Failed to parse error from /api/auth" }));
            console.error("Login Page: /api/auth call failed for existing session:", response.status, errorData.error);
            // If token is invalid or other auth issue, might need to sign out
            // await supabase.auth.signOut(); 
            // setError(`Session invalid: ${errorData.error || response.statusText}. Please log in again.`);
            setLoading(false);
            return; // Allow user to login manually
          }

          const userData = await response.json();
          console.log("Login Page: User data from /api/auth (existing session):", userData);

          if (userData.role === "admin") {
            window.location.href = "/admin/dashboard";
          } else if (userData.role === "counselor") {
            window.location.href = "/counselor/dashboard";
          } else {
            window.location.href = "/couple/dashboard";
          }
        } catch (err) {
          console.error("Login Page: Error during session check and redirect:", err);
          // setError("Failed to verify your existing session. Please try logging in.");
          setLoading(false);
        }
      }
    };

    checkUserSessionAndRedirect();
  }, [roleParam, router]); // Added router to dependency array, though not directly used in this hook's logic, common practice.

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const supabase = getClient();
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      // User signed in, now get session to include token for /api/auth
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !sessionData.session) {
        console.error("Login error: Could not get session after sign-in", sessionError);
        setError("Failed to retrieve session after login. Please try again.");
        setLoading(false);
        return;
      }

      console.log("Login Page: Successfully signed in, fetching user role from /api/auth with token.");
      // Check user role and redirect accordingly
      const response = await fetch("/api/auth", {
        headers: {
          Authorization: `Bearer ${sessionData.session.access_token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: "Failed to parse error from /api/auth" }));
        console.error("Login Page: /api/auth call failed after login:", response.status, errorData.error);
        setError(`Role check failed: ${errorData.error || response.statusText}. Please try again.`);
        setLoading(false);
        return;
      }

      const userData = await response.json();
      console.log("Login Page: User data from /api/auth (after login):", userData);

      // Use window.location.href for more reliable redirection
      if (userData.role === "admin") {
        window.location.href = "/admin/dashboard";
      } else if (userData.role === "counselor") {
        window.location.href = "/counselor/dashboard";
      } else {
        window.location.href = "/couple/dashboard";
      }
    } catch (err) {
      console.error("Login error:", err);
      setError(
        err instanceof Error ? err.message : "An error occurred during login",
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Register as a regular user
      const response = await fetch("/api/auth", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
          role: "user",
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Registration failed");
      }

      // Auto-login after successful registration
      const supabase = getClient();
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (signInError) {
        throw signInError;
      }

      // Redirect to the appropriate dashboard based on user role
      window.location.href = "/couple/dashboard";
    } catch (err) {
      console.error("Registration error:", err);
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred during registration",
      );
    } finally {
      setLoading(false);
    }
  };

  // Get icon and title based on user type
  const getUserTypeInfo = () => {
    switch (userType) {
      case "counselor":
        return {
          icon: <UserCogIcon className="h-8 w-8 text-primary" />,
          title: "Counselor Portal",
        };
      case "admin":
        return {
          icon: <Settings className="h-8 w-8 text-primary" />,
          title: "Admin Portal",
        };
      default:
        return {
          icon: <Heart className="h-8 w-8 text-primary" />,
          title: "Marriage Assessment",
        };
    }
  };

  const { icon, title } = getUserTypeInfo();

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md">
        <div className="flex justify-center mb-8">
          <div className="flex items-center gap-2">
            {icon}
            <span className="text-2xl font-bold">{title}</span>
          </div>
        </div>

        <Tabs
          defaultValue="login"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="login">Login</TabsTrigger>
            {userType === "user" && (
              <TabsTrigger value="register">Register</TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="login">
            <Card>
              <CardHeader>
                <CardTitle>
                  {userType === "user"
                    ? "Login"
                    : userType === "counselor"
                      ? "Counselor Login"
                      : "Admin Login"}
                </CardTitle>
                <CardDescription>
                  Enter your credentials to access your account
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleLogin}>
                <CardContent className="space-y-4">
                  {error && (
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                      <div className="flex items-center">
                        <AlertCircle className="h-4 w-4 mr-2" />
                        {error}
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="password">Password</Label>
                      <Link
                        href="/forgot-password"
                        className="text-sm text-primary hover:underline"
                      >
                        Forgot password?
                      </Link>
                    </div>
                    <Input
                      id="password"
                      type="password"
                      placeholder="••••••••"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                    />
                  </div>
                </CardContent>

                <CardFooter className="flex-col space-y-4">
                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Logging in...
                      </>
                    ) : (
                      "Login"
                    )}
                  </Button>

                  {userType !== "user" && (
                    <div className="text-center w-full">
                      <Link
                        href="/login"
                        className="text-sm text-primary hover:underline"
                      >
                        Login as regular user
                      </Link>
                    </div>
                  )}

                  {userType === "user" && (
                    <div className="text-center w-full">
                      <Link
                        href="/login?tab=register"
                        className="text-sm text-primary hover:underline"
                      >
                        Don't have an account? Register
                      </Link>
                    </div>
                  )}
                </CardFooter>
              </form>
            </Card>
          </TabsContent>

          {userType === "user" && (
            <TabsContent value="register">
              <Card>
                <CardHeader>
                  <CardTitle>Create an Account</CardTitle>
                  <CardDescription>
                    Register to start your marriage assessment journey
                  </CardDescription>
                </CardHeader>
                <form onSubmit={handleSignUp}>
                  <CardContent className="space-y-4">
                    {error && (
                      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                        <div className="flex items-center">
                          <AlertCircle className="h-4 w-4 mr-2" />
                          {error}
                        </div>
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor="register-email">Email</Label>
                      <Input
                        id="register-email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="register-password">Password</Label>
                      <Input
                        id="register-password"
                        type="password"
                        placeholder="••••••••"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                      />
                    </div>
                  </CardContent>

                  <CardFooter className="flex-col space-y-3">
                    <Button type="submit" className="w-full" disabled={loading}>
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Creating account...
                        </>
                      ) : (
                        "Create Account"
                      )}
                    </Button>
                    <div className="flex justify-between w-full text-xs text-muted-foreground pt-2">
                      <Link
                        href="/counselor/register"
                        className="hover:text-primary"
                      >
                        Register as counselor
                      </Link>
                      <Link
                        href="/admin/register"
                        className="hover:text-primary"
                      >
                        Register as admin
                      </Link>
                    </div>
                  </CardFooter>
                </form>
              </Card>
            </TabsContent>
          )}
        </Tabs>

        <div className="mt-6 text-center">
          <Link
            href="/"
            className="text-sm text-muted-foreground hover:text-primary"
          >
            ← Back to Home
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <div className="w-full max-w-md">
          <div className="flex justify-center mb-8">
            <div className="flex items-center gap-2">
              <Heart className="h-8 w-8 text-primary" />
              <span className="text-2xl font-bold">Loading...</span>
            </div>
          </div>
        </div>
      </div>
    }>
      <LoginPageContent />
    </Suspense>
  );
}
