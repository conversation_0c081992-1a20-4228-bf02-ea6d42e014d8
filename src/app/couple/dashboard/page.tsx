"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { getClient } from "@/lib/supabase/client";
import { handleLogout } from "@/lib/auth-utils";
import {
  Calendar,
  MessageCircle,
  FileText,
  Heart,
  Loader2,
  AlertCircle,
  CheckCircle,
  Clock,
  UserCog as UserCogIcon,
  LogOut,
} from "lucide-react";
import Link from "next/link";

export default function CoupleDashboardPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState({
    userName: "",
    coupleCode: "",
    partnerName: "",
    isConnected: false,
    completedDomains: [] as string[],
    counselorName: "",
    nextSessionDate: null as Date | null,
  });

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        const supabase = getClient();

        // Get current user
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (!user) {
          // Redirect to login if not authenticated
          window.location.href = "/login";
          return;
        }

        // Get user profile
        const { data: profile } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", user.id)
          .single();

        // Get individual results to determine completed domains
        const { data: results } = await supabase
          .from("individual_results")
          .select("domains")
          .eq("user_id", user.id)
          .single();

        // Get couple information using API
        const { data: { session } } = await supabase.auth.getSession();
        const token = session?.access_token;

        let coupleStatus = null;
        let coupleData = null;

        if (token) {
          try {
            const statusResponse = await fetch("/api/couples/status", {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            if (statusResponse.ok) {
              coupleStatus = await statusResponse.json();


              if (coupleStatus.isConnected && coupleStatus.couple) {
                // Create coupleData object compatible with existing code
                coupleData = {
                  couple_id: coupleStatus.couple.friendlyCode,
                  profiles: {
                    full_name: coupleStatus.partner.displayName
                  }
                };
              }
            }
          } catch (error) {
            // Silently handle error, user will see not connected status
          }
        }

        // Get counselor information if assigned
        let counselorName = "";
        let nextSessionDate = null;

        if (coupleData?.couple_id) {
          // Get counselor assignment
          const { data: counselorAssignment } = await supabase
            .from("counselor_couple_assignments")
            .select("*, counselor_profiles(full_name)")
            .eq("couple_id", coupleData.couple_id)
            .single();

          if (counselorAssignment) {
            counselorName =
              (counselorAssignment as any).counselor_profiles?.full_name || "";

            // Get next session
            const { data: nextSession } = await supabase
              .from("counseling_sessions")
              .select("*")
              .eq("couple_id", coupleData.couple_id)
              .eq("status", "scheduled")
              .order("session_date", { ascending: true })
              .limit(1)
              .single();

            if (nextSession) {
              nextSessionDate = new Date(nextSession.session_date as string);
            }
          }
        }

        // Extract completed domains from results
        const completedDomains =
          (results as any)?.domains?.map((domain: any) => domain.domain.toLowerCase()) ||
          [];

        setUserData({
          userName: (profile as any)?.full_name || user.email?.split("@")[0] || "User",
          coupleCode: (coupleData as any)?.couple_id || "",
          partnerName: (coupleData as any)?.profiles?.full_name || "",
          isConnected: (coupleStatus as any)?.isConnected || false,
          completedDomains,
          counselorName,
          nextSessionDate,
        });
      } catch (err) {
        console.error("Error fetching user data:", err);
        setError(
          err instanceof Error
            ? err.message
            : "An error occurred while loading your dashboard",
        );
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // Assessment domains
  const domains = [
    {
      id: "vision",
      title: "Vision",
      description: "Life goals and future plans",
      icon: "🔭",
      completed: userData.completedDomains.includes("vision"),
      status: userData.completedDomains.includes("vision")
        ? "completed"
        : "not-started",
    },
    {
      id: "finances",
      title: "Finances",
      description: "Money management and financial goals",
      icon: "💰",
      completed: userData.completedDomains.includes("finances"),
      status: userData.completedDomains.includes("finances")
        ? "completed"
        : "not-started",
    },
    {
      id: "parenting",
      title: "Parenting",
      description: "Child-rearing philosophies and approaches",
      icon: "👶",
      completed: userData.completedDomains.includes("parenting"),
      status: userData.completedDomains.includes("parenting")
        ? "completed"
        : "not-started",
    },
    {
      id: "communication",
      title: "Communication",
      description: "Styles and patterns of interaction",
      icon: "💬",
      completed: userData.completedDomains.includes("communication"),
      status: userData.completedDomains.includes("communication")
        ? "completed"
        : "not-started",
    },
    {
      id: "roles",
      title: "Roles",
      description: "Functions and responsibilities in marriage",
      icon: "🔄",
      completed: userData.completedDomains.includes("roles"),
      status: userData.completedDomains.includes("roles")
        ? "completed"
        : "not-started",
    },
    {
      id: "sexuality",
      title: "Sexuality",
      description: "Intimacy and physical relationship",
      icon: "❤️",
      completed: userData.completedDomains.includes("sexuality"),
      status: userData.completedDomains.includes("sexuality")
        ? "completed"
        : "not-started",
    },
    {
      id: "spirituality",
      title: "Spirituality",
      description: "Faith practices and spiritual growth",
      icon: "✝️",
      completed: userData.completedDomains.includes("spirituality"),
      status: userData.completedDomains.includes("spirituality")
        ? "completed"
        : "not-started",
    },
    {
      id: "darkside",
      title: "Dark Side",
      description: "Potential challenges and negative patterns",
      icon: "🌑",
      completed: userData.completedDomains.includes("darkside"),
      status: userData.completedDomains.includes("darkside")
        ? "completed"
        : "not-started",
    },
  ];

  // Calculate progress percentage
  const progressPercentage = Math.round(
    (userData.completedDomains.length / domains.length) * 100,
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading your dashboard...</span>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="flex">
        {/* Sidebar */}
        <div className="hidden md:flex w-64 flex-col fixed inset-y-0 z-50 border-r bg-background">
          <div className="flex flex-col space-y-6 p-4">
            <div className="flex items-center gap-2 px-2 py-4">
              <Heart className="h-6 w-6 text-primary" />
              <span className="text-xl font-bold">Couple Portal</span>
            </div>

            <nav className="flex flex-col space-y-1">
              <Link
                href="/couple/dashboard"
                className="flex items-center gap-3 rounded-lg bg-primary/10 px-3 py-2 text-primary transition-all hover:text-primary"
              >
                <Heart className="h-5 w-5" />
                <span>Dashboard</span>
              </Link>
              <Link
                href="/couple/assessments"
                className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary"
              >
                <FileText className="h-5 w-5" />
                <span>Assessments</span>
              </Link>
              <Link
                href="/couple/results"
                className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary"
              >
                <CheckCircle className="h-5 w-5" />
                <span>Results</span>
              </Link>
              <Link
                href="/couple/sessions"
                className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary"
              >
                <Calendar className="h-5 w-5" />
                <span>Sessions</span>
              </Link>
              <Link
                href="/couple/messages"
                className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary"
              >
                <MessageCircle className="h-5 w-5" />
                <span>Messages</span>
              </Link>
              <button
                onClick={handleLogout}
                className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:bg-red-50 hover:text-red-600 w-full mt-4"
              >
                <LogOut className="h-5 w-5" />
                <span>Logout</span>
              </button>
            </nav>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 md:pl-64">
          <div className="container py-8">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">
                  Welcome, {userData.userName}
                </h1>
                <p className="text-muted-foreground">
                  {userData.isConnected
                    ? `Connected with ${userData.partnerName}`
                    : "Complete your assessment and connect with your partner"}
                </p>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
                <div className="flex items-center">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  {error}
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Left column */}
              <div className="md:col-span-2 space-y-6">
                {/* Assessment Progress */}
                <Card>
                  <CardHeader>
                    <CardTitle>Assessment Progress</CardTitle>
                    <CardDescription>
                      Complete all 8 domains to get comprehensive insights
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>
                          {userData.completedDomains.length} of {domains.length}{" "}
                          completed
                        </span>
                        <span>{progressPercentage}%</span>
                      </div>
                      <Progress value={progressPercentage} className="h-2" />
                    </div>

                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mt-6">
                      {domains.map((domain) => (
                        <div
                          key={domain.id}
                          className="flex flex-col items-center p-3 border rounded-lg"
                        >
                          <div className="text-2xl mb-1">{domain.icon}</div>
                          <div className="text-sm font-medium">
                            {domain.title}
                          </div>
                          <div className="mt-1">
                            {domain.completed ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <Clock className="h-4 w-4 text-amber-500" />
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button
                      className="w-full"
                      onClick={() => router.push("/dashboard")}
                    >
                      Continue Assessment
                    </Button>
                  </CardFooter>
                </Card>

                {/* Compatibility Results */}
                {userData.isConnected &&
                  userData.completedDomains.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Compatibility Results</CardTitle>
                        <CardDescription>
                          See how your responses align with your partner's
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center py-8">
                          <p className="text-muted-foreground mb-4">
                            {userData.completedDomains.length === domains.length
                              ? "All assessments completed! View your detailed results below."
                              : "Complete all assessment domains to view detailed compatibility results."}
                          </p>
                        </div>
                      </CardContent>
                      <CardFooter>
                        <Button
                          className="w-full"
                          disabled={
                            userData.completedDomains.length !== domains.length
                          }
                          onClick={() => router.push("/couple/results")}
                        >
                          View Detailed Results
                        </Button>
                      </CardFooter>
                    </Card>
                  )}
              </div>

              {/* Right column */}
              <div className="space-y-6">
                {/* Connection Status */}
                <Card>
                  <CardHeader>
                    <CardTitle>Connection Status</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {userData.isConnected ? (
                      <div className="flex flex-col items-center text-center">
                        <div className="bg-green-50 rounded-full p-3 mb-3">
                          <CheckCircle className="h-6 w-6 text-green-500" />
                        </div>
                        <p className="font-medium">Connected with Partner</p>
                        <p className="text-sm text-muted-foreground mt-1">
                          {userData.partnerName}
                        </p>
                        <div className="mt-4 p-2 bg-muted rounded-md w-full">
                          <p className="text-xs font-medium">Couple Code</p>
                          <p className="font-mono text-sm">
                            {userData.coupleCode}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center text-center">
                        <div className="bg-amber-50 rounded-full p-3 mb-3">
                          <AlertCircle className="h-6 w-6 text-amber-500" />
                        </div>
                        <p className="font-medium">Not Connected</p>
                        <p className="text-sm text-muted-foreground mt-1">
                          Connect with your partner to compare results
                        </p>
                        <Button
                          className="mt-4 w-full"
                          onClick={() => router.push("/dashboard?tab=connect")}
                        >
                          Connect Now
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Counselor Information */}
                <Card>
                  <CardHeader>
                    <CardTitle>Counselor</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {userData.counselorName ? (
                      <div className="flex flex-col items-center text-center">
                        <div className="bg-primary/10 rounded-full p-3 mb-3">
                          <UserCogIcon className="h-6 w-6 text-primary" />
                        </div>
                        <p className="font-medium">{userData.counselorName}</p>
                        <p className="text-sm text-muted-foreground mt-1">
                          Your assigned counselor
                        </p>
                        <Button
                          className="mt-4 w-full"
                          variant="outline"
                          onClick={() => router.push("/couple/messages")}
                        >
                          Message Counselor
                        </Button>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center text-center">
                        <div className="bg-muted rounded-full p-3 mb-3">
                          <UserCogIcon className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <p className="font-medium">No Counselor Assigned</p>
                        <p className="text-sm text-muted-foreground mt-1">
                          Complete your assessments to get matched with a
                          counselor
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Next Session */}
                {userData.nextSessionDate && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Next Session</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-col items-center text-center">
                        <div className="bg-primary/10 rounded-full p-3 mb-3">
                          <Calendar className="h-6 w-6 text-primary" />
                        </div>
                        <p className="font-medium">
                          {userData.nextSessionDate.toLocaleDateString(
                            "en-US",
                            {
                              weekday: "long",
                              month: "long",
                              day: "numeric",
                            },
                          )}
                        </p>
                        <p className="text-sm text-muted-foreground mt-1">
                          {userData.nextSessionDate.toLocaleTimeString(
                            "en-US",
                            {
                              hour: "numeric",
                              minute: "2-digit",
                            },
                          )}
                        </p>
                        <Button
                          className="mt-4 w-full"
                          onClick={() => router.push("/couple/sessions")}
                        >
                          View Details
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Quick Actions */}
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => router.push("/dashboard")}
                    >
                      <FileText className="mr-2 h-4 w-4" />
                      Continue Assessment
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => router.push("/couple/results")}
                      disabled={userData.completedDomains.length === 0}
                    >
                      <CheckCircle className="mr-2 h-4 w-4" />
                      View Results
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => router.push("/couple/sessions/schedule")}
                      disabled={!userData.counselorName}
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      Schedule Session
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
