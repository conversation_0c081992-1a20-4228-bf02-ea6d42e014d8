import { createAdminClient } from "@/lib/supabase/client";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
    try {
        const supabase = createAdminClient(); // Uses SERVICE_ROLE_KEY

        // 1. Verify the user token from the Authorization header
        const authHeader = request.headers.get("Authorization");
        const token = authHeader?.replace("Bearer ", "");

        if (!token) {
            console.warn("/api/admin/stats: No token provided");
            return NextResponse.json(
                { error: "Authentication required" },
                { status: 401 },
            );
        }

        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser(token);

        if (userError || !user) {
            console.warn(
                "/api/admin/stats: Invalid token or user not found.",
                userError?.message,
            );
            return NextResponse.json(
                { error: userError?.message || "Invalid token" },
                { status: 401 },
            );
        }

        // Optional: Re-verify admin role here if desired for extra security,
        // though the frontend /api/auth should have already done this.
        // For simplicity, we'll assume if they have a valid token and hit this endpoint,
        // the frontend has done its due diligence. A more robust solution might re-check admin_users table.
        console.log(
            `/api/admin/stats: Request from authenticated user ${user.id}. Fetching stats.`,
        );

        // 2. Fetch statistics
        const { count: usersCount, error: usersError } = await supabase
            .from("profiles")
            .select("*", { count: "exact", head: true });

        const { count: couplesCount, error: couplesError } = await supabase
            .from("couples")
            .select("*", { count: "exact", head: true });

        const { count: counselorsCount, error: counselorsError } =
            await supabase
                .from("counselor_profiles")
                .select("*", { count: "exact", head: true });

        const { count: completedAssessmentsCount, error: assessmentsError } =
            await supabase
                .from("individual_results") // Assuming this table stores completed assessments
                .select("*", { count: "exact", head: true });

        if (usersError || couplesError || counselorsError || assessmentsError) {
            console.error(
                "/api/admin/stats: Error fetching one or more stats:",
                {
                    usersError: usersError?.message,
                    couplesError: couplesError?.message,
                    counselorsError: counselorsError?.message,
                    assessmentsError: assessmentsError?.message,
                },
            );
            return NextResponse.json(
                { error: "Failed to fetch some statistics" },
                { status: 500 },
            );
        }

        const stats = {
            totalUsers: usersCount || 0,
            totalCouples: couplesCount || 0,
            totalCounselors: counselorsCount || 0,
            completedAssessments: completedAssessmentsCount || 0,
        };

        console.log("/api/admin/stats: Successfully fetched stats:", stats);
        return NextResponse.json(stats);
    } catch (error: any) {
        console.error(
            "/api/admin/stats: Unhandled exception:",
            error.message,
            error.stack,
        );
        return NextResponse.json(
            { error: "Internal server error fetching stats" },
            { status: 500 },
        );
    }
}
