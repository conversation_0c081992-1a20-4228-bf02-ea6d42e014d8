import { NextResponse } from "next/server";
import { createAdminClient } from "@/lib/supabase/client";

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
    try {
        const supabase = createAdminClient();

        // Get token from Authorization header
        const authHeader = request.headers.get("Authorization");
        const token = authHeader?.replace("Bearer ", "");

        if (!token) {
            return NextResponse.json(
                { error: "No token provided" },
                { status: 401 },
            );
        }

        // Get current user using token
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser(token);

        if (userError || !user) {
            return NextResponse.json(
                { error: "Unauthorized" },
                { status: 401 },
            );
        }

        // Check if user is in a couple
        const { data: couple, error: coupleError } = await supabase
            .from("couples")
            .select(
                `
        couple_id,
        user_id_1,
        user_id_2,
        created_at
      `,
            )
            .or(`user_id_1.eq.${user.id},user_id_2.eq.${user.id}`)
            .single();

        if (coupleError && coupleError.code !== "PGRST116") {
            console.error("Error fetching couple:", coupleError);
            return NextResponse.json(
                { error: "Failed to fetch couple status" },
                { status: 500 },
            );
        }

        if (!couple) {
            // User is not connected, check for active invitation codes
            const { data: activeCodes } = await supabase
                .from("couple_invitation_codes")
                .select("code, expires_at")
                .eq("creator_user_id", user.id)
                .eq("is_active", true)
                .order("created_at", { ascending: false })
                .limit(1);

            return NextResponse.json({
                isConnected: false,
                couple: null,
                partner: null,
                activeInvitationCode: activeCodes?.[0] || null,
            });
        }

        // Get partner ID
        const partnerId =
            couple.user_id_1 === user.id ? couple.user_id_2 : couple.user_id_1;

        // Get partner profile (without avatar_url for now due to column not existing)
        const { data: partnerProfile, error: partnerError } = await supabase
            .from("profiles")
            .select("full_name, email")
            .eq("id", partnerId as string)
            .single();

        // Generate a user-friendly couple code (first 8 characters of UUID)
        const friendlyCode = (couple.couple_id as string)
            .substring(0, 8)
            .toUpperCase();

        // Determine display name with priority: email > full_name > "Partner"
        let displayName = "Partner";
        if (partnerProfile?.email) {
            displayName = partnerProfile.email as string;
        } else if (partnerProfile?.full_name) {
            displayName = partnerProfile.full_name as string;
        }

        return NextResponse.json({
            isConnected: true,
            couple: {
                id: couple.couple_id as string,
                friendlyCode: friendlyCode,
                created_at: couple.created_at,
            },
            partner: {
                id: partnerId,
                name: (partnerProfile?.full_name as string) || "Partner",
                email: (partnerProfile?.email as string) || "Unknown",
                displayName: displayName,
            },
            activeInvitationCode: null,
        });
    } catch (error) {
        console.error("Error in couples status API:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 },
        );
    }
}
