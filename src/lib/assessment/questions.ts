export interface Question {
    id: string;
    type: "multiple-choice" | "scenario" | "ranking" | "open-ended" | "scale";
    text: string;
    options?: string[];
    required?: boolean;
    domain: string;
    weight?: number; // For scoring calculations
    category?: string; // For categorization (e.g., parenting style, communication type)
}

// Legacy questions - DEPRECATED
// Use enhancedAssessmentQuestions from enhancedQuestions.ts instead
export const assessmentQuestions: Record<string, Question[]> = {
    // Kept for backward compatibility only
    // All new implementations should use enhancedAssessmentQuestions
};
