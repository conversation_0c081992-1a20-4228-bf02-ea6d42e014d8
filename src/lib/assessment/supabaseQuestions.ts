import { createClient } from "@/lib/supabase/client";
import { Question } from "./questions";

// Function to fetch questions from Supabase
export async function fetchQuestionsByDomain(
    domain: string,
): Promise<Question[]> {
    const supabase = createClient();

    const { data, error } = await supabase
        .from("assessment_questions")
        .select("*")
        .eq("domain", domain.toLowerCase());

    if (error) {
        console.error("Error fetching questions:", error);
        throw new Error(`Failed to fetch ${domain} questions`);
    }

    // Transform the data to match the Question interface
    return data.map((item: any) => ({
        id: item.id as string,
        type: item.type as
            | "multiple-choice"
            | "scenario"
            | "ranking"
            | "open-ended"
            | "scale",
        text: item.text as string,
        options: item.options as string[] | undefined,
        required: item.required as boolean | undefined,
        domain: item.domain as string,
        weight: item.weight as number | undefined,
        category: item.category as string | undefined,
    }));
}

// Function to fetch all questions (for admin purposes)
export async function fetchAllQuestions(): Promise<Record<string, Question[]>> {
    const supabase = createClient();

    const { data, error } = await supabase
        .from("assessment_questions")
        .select("*");

    if (error) {
        console.error("Error fetching all questions:", error);
        throw new Error("Failed to fetch questions");
    }

    // Group questions by domain
    const questionsByDomain: Record<string, Question[]> = {};

    data.forEach((item: any) => {
        const question: Question = {
            id: item.id as string,
            type: item.type as
                | "multiple-choice"
                | "scenario"
                | "ranking"
                | "open-ended"
                | "scale",
            text: item.text as string,
            options: item.options as string[] | undefined,
            required: item.required as boolean | undefined,
            domain: item.domain as string,
            weight: item.weight as number | undefined,
            category: item.category as string | undefined,
        };

        if (!questionsByDomain[item.domain as string]) {
            questionsByDomain[item.domain as string] = [];
        }

        questionsByDomain[item.domain as string].push(question);
    });

    return questionsByDomain;
}

// Function to add a new question (admin only)
export async function addQuestion(question: Question): Promise<void> {
    const supabase = createClient();

    const { error } = await supabase.from("assessment_questions").insert([
        {
            id: question.id,
            domain: question.domain,
            type: question.type,
            text: question.text,
            options: question.options,
            required: question.required,
            weight: question.weight,
            category: question.category,
        },
    ]);

    if (error) {
        console.error("Error adding question:", error);
        throw new Error("Failed to add question");
    }
}

// Function to update an existing question (admin only)
export async function updateQuestion(question: Question): Promise<void> {
    const supabase = createClient();

    const { error } = await supabase
        .from("assessment_questions")
        .update({
            domain: question.domain,
            type: question.type,
            text: question.text,
            options: question.options,
            required: question.required,
            weight: question.weight,
            category: question.category,
        })
        .eq("id", question.id);

    if (error) {
        console.error("Error updating question:", error);
        throw new Error("Failed to update question");
    }
}

// Function to delete a question (admin only)
export async function deleteQuestion(questionId: string): Promise<void> {
    const supabase = createClient();

    const { error } = await supabase
        .from("assessment_questions")
        .delete()
        .eq("id", questionId);

    if (error) {
        console.error("Error deleting question:", error);
        throw new Error("Failed to delete question");
    }
}
