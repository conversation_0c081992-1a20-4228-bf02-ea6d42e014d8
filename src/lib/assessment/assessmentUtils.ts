import { enhancedAssessmentQuestions } from "./enhancedQuestions";
import {
    AssessmentResponse,
    IndividualResult,
    CompatibilityResult,
    calculateIndividualResult,
    calculateCompatibility,
    ASSESSMENT_DOMAINS,
} from "./calculationLogic";
import {
    generateCoupleAnalysisReport,
    CoupleAnalysisReport,
} from "./resultAnalysis";

// Utility functions for the assessment system

// Get all questions for a specific domain
export function getQuestionsForDomain(domain: string) {
    return enhancedAssessmentQuestions[domain] || [];
}

// Get all domains
export function getAllDomains() {
    return ASSESSMENT_DOMAINS;
}

// Format domain name for display
export function formatDomainName(domain: string): string {
    const domainNames: Record<string, string> = {
        "visi-hidup": "Visi Hidup",
        keuangan: "Keuangan",
        pengasuhan: "Pengasuhan Anak",
        komunikasi: "Komunikasi",
        "fungsi-dan-peran": "Fungsi dan Peran",
        seks: "<PERSON>intiman Seksual",
        spiritualitas: "Spiritualitas",
        "sisi-gelap": "Sisi Gelap",
    };

    return domainNames[domain] || domain;
}

// Convert between Indonesian and English domain names
export function convertDomainName(
    domain: string,
    toLanguage: "id" | "en",
): string {
    const idToEn: Record<string, string> = {
        "visi-hidup": "vision",
        keuangan: "finances",
        pengasuhan: "parenting",
        komunikasi: "communication",
        "fungsi-dan-peran": "roles",
        seks: "sexuality",
        spiritualitas: "spirituality",
        "sisi-gelap": "darkside",
    };

    const enToId: Record<string, string> = {
        vision: "visi-hidup",
        finances: "keuangan",
        parenting: "pengasuhan",
        communication: "komunikasi",
        roles: "fungsi-dan-peran",
        sexuality: "seks",
        spirituality: "spiritualitas",
        darkside: "sisi-gelap",
    };

    if (toLanguage === "en") {
        return idToEn[domain] || domain;
    } else {
        return enToId[domain] || domain;
    }
}

// Check if domain is completed based on formatted names from database
export function isDomainCompleted(
    domainName: string,
    completedDomains: string[],
): boolean {
    // Try exact match first
    if (completedDomains.includes(domainName)) {
        return true;
    }

    // Try formatted name match
    const formattedName = formatDomainName(domainName);
    if (completedDomains.includes(formattedName)) {
        return true;
    }

    // Try converted name match
    const convertedToEn = convertDomainName(domainName, "en");
    if (completedDomains.includes(convertedToEn)) {
        return true;
    }

    const convertedToId = convertDomainName(domainName, "id");
    if (completedDomains.includes(convertedToId)) {
        return true;
    }

    // Try case-insensitive match
    const lowerDomainName = domainName.toLowerCase();
    return completedDomains.some(
        (completed) =>
            completed.toLowerCase() === lowerDomainName ||
            completed.toLowerCase() === formattedName.toLowerCase() ||
            completed.toLowerCase() === convertedToEn.toLowerCase() ||
            completed.toLowerCase() === convertedToId.toLowerCase(),
    );
}

// Validate assessment responses
export function validateResponses(responses: AssessmentResponse[]): {
    isValid: boolean;
    missingDomains: string[];
    missingQuestions: string[];
} {
    const missingDomains: string[] = [];
    const missingQuestions: string[] = [];

    // Check if all domains are covered
    ASSESSMENT_DOMAINS.forEach((domain) => {
        const domainResponses = responses.filter((r) => r.domain === domain);
        const domainQuestions = enhancedAssessmentQuestions[domain];

        if (domainResponses.length === 0) {
            missingDomains.push(domain);
        } else {
            // Check if all required questions are answered
            domainQuestions.forEach((question) => {
                if (question.required) {
                    const hasResponse = domainResponses.some(
                        (r) => r.questionId === question.id,
                    );
                    if (!hasResponse) {
                        missingQuestions.push(question.id);
                    }
                }
            });
        }
    });

    return {
        isValid: missingDomains.length === 0 && missingQuestions.length === 0,
        missingDomains,
        missingQuestions,
    };
}

// Process complete assessment for an individual
export function processIndividualAssessment(
    userId: string,
    responses: AssessmentResponse[],
): IndividualResult {
    const validation = validateResponses(responses);

    if (!validation.isValid) {
        throw new Error(
            `Assessment incomplete. Missing domains: ${validation.missingDomains.join(", ")}. ` +
                `Missing questions: ${validation.missingQuestions.join(", ")}`,
        );
    }

    return calculateIndividualResult(userId, responses);
}

// Process couple compatibility assessment
export function processCoupleAssessment(
    partner1Result: IndividualResult,
    partner2Result: IndividualResult,
): {
    compatibility: CompatibilityResult;
    analysisReport: CoupleAnalysisReport;
} {
    const compatibility = calculateCompatibility(
        partner1Result,
        partner2Result,
    );
    const analysisReport = generateCoupleAnalysisReport(compatibility);

    return {
        compatibility,
        analysisReport,
    };
}

// Get progress for an individual's assessment
export function getAssessmentProgress(responses: AssessmentResponse[]): {
    completedDomains: string[];
    totalDomains: number;
    progressPercentage: number;
    nextDomain?: string;
} {
    const completedDomains: string[] = [];

    ASSESSMENT_DOMAINS.forEach((domain) => {
        const domainQuestions = enhancedAssessmentQuestions[domain];
        const requiredQuestions = domainQuestions.filter((q) => q.required);
        const domainResponses = responses.filter((r) => r.domain === domain);

        // Check if all required questions are answered
        const allRequiredAnswered = requiredQuestions.every((question) =>
            domainResponses.some(
                (response) => response.questionId === question.id,
            ),
        );

        if (allRequiredAnswered) {
            completedDomains.push(domain);
        }
    });

    const progressPercentage = Math.round(
        (completedDomains.length / ASSESSMENT_DOMAINS.length) * 100,
    );

    // Find next incomplete domain
    const nextDomain = ASSESSMENT_DOMAINS.find(
        (domain) => !completedDomains.includes(domain),
    );

    return {
        completedDomains,
        totalDomains: ASSESSMENT_DOMAINS.length,
        progressPercentage,
        nextDomain,
    };
}

// Generate summary for counselor dashboard
export function generateCounselorSummary(
    analysisReport: CoupleAnalysisReport,
): {
    riskLevel: "Low" | "Medium" | "High" | "Critical";
    keyInsights: string[];
    actionItems: string[];
    sessionRecommendations: string[];
} {
    const { overallCompatibility, challengeAreas, domainAnalyses } =
        analysisReport;

    // Determine risk level
    let riskLevel: "Low" | "Medium" | "High" | "Critical";
    if (overallCompatibility >= 80) riskLevel = "Low";
    else if (overallCompatibility >= 60) riskLevel = "Medium";
    else if (overallCompatibility >= 40) riskLevel = "High";
    else riskLevel = "Critical";

    // Generate key insights
    const keyInsights: string[] = [];

    if (challengeAreas.length === 0) {
        keyInsights.push(
            "Pasangan menunjukkan keselarasan yang baik di semua area",
        );
    } else {
        keyInsights.push(
            `${challengeAreas.length} area memerlukan perhatian khusus`,
        );
    }

    // Check for critical patterns
    const communicationIssues = domainAnalyses.find(
        (d) => d.domain === "komunikasi" && d.status === "conflict",
    );
    if (communicationIssues) {
        keyInsights.push(
            "Masalah komunikasi terdeteksi - prioritas utama untuk ditangani",
        );
    }

    const roleConflicts = domainAnalyses.find(
        (d) => d.domain === "fungsi-dan-peran" && d.status === "conflict",
    );
    if (roleConflicts) {
        keyInsights.push(
            "Perbedaan pandangan tentang peran dalam pernikahan perlu didiskusikan",
        );
    }

    // Generate action items
    const actionItems: string[] = [];

    challengeAreas.forEach((area) => {
        actionItems.push(`Sesi khusus untuk membahas ${area}`);
    });

    if (riskLevel === "Critical" || riskLevel === "High") {
        actionItems.push("Pertimbangkan sesi konseling intensif");
        actionItems.push("Evaluasi kesiapan untuk menikah");
    }

    // Generate session recommendations
    const sessionRecommendations: string[] = [];

    if (challengeAreas.length > 0) {
        sessionRecommendations.push(
            `Mulai dengan area prioritas: ${challengeAreas[0]}`,
        );
    }

    sessionRecommendations.push(
        "Gunakan hasil assessment sebagai panduan diskusi",
    );
    sessionRecommendations.push(
        "Fokus pada solusi praktis dan rencana tindakan",
    );

    if (riskLevel === "Low") {
        sessionRecommendations.push("Sesi follow-up dalam 3-6 bulan");
    } else {
        sessionRecommendations.push("Sesi follow-up dalam 1-2 bulan");
    }

    return {
        riskLevel,
        keyInsights,
        actionItems,
        sessionRecommendations,
    };
}

// Helper function to convert responses to database format
export function formatResponsesForDatabase(responses: AssessmentResponse[]) {
    return responses.map((response) => ({
        question_id: response.questionId,
        answer:
            typeof response.answer === "object"
                ? JSON.stringify(response.answer)
                : response.answer.toString(),
        domain: response.domain,
    }));
}

// Helper function to parse responses from database format
export function parseResponsesFromDatabase(
    dbResponses: any[],
): AssessmentResponse[] {
    return dbResponses.map((dbResponse) => ({
        questionId: dbResponse.question_id,
        answer: dbResponse.answer,
        domain: dbResponse.domain,
    }));
}

// Get domain completion status
export function getDomainCompletionStatus(
    domain: string,
    responses: AssessmentResponse[],
): {
    isComplete: boolean;
    answeredQuestions: number;
    totalQuestions: number;
    requiredQuestions: number;
    answeredRequired: number;
} {
    const domainQuestions = enhancedAssessmentQuestions[domain] || [];
    const domainResponses = responses.filter((r) => r.domain === domain);
    const requiredQuestions = domainQuestions.filter((q) => q.required);

    const answeredRequired = requiredQuestions.filter((question) =>
        domainResponses.some((response) => response.questionId === question.id),
    ).length;

    const isComplete = answeredRequired === requiredQuestions.length;

    return {
        isComplete,
        answeredQuestions: domainResponses.length,
        totalQuestions: domainQuestions.length,
        requiredQuestions: requiredQuestions.length,
        answeredRequired,
    };
}

// Export commonly used types and constants
export type {
    AssessmentResponse,
    IndividualResult,
    CompatibilityResult,
    CoupleAnalysisReport,
};
export { ASSESSMENT_DOMAINS };
