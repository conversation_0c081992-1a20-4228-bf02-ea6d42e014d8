"use client";

import React from "react";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { CheckCircle, Clock, ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface DomainCardLinkProps {
  title: string;
  description: string;
  icon: string;
  status: "completed" | "not-started" | "in-progress";
  domainId: string;
  className?: string;
}

const DomainCardLink: React.FC<DomainCardLinkProps> = ({
  title,
  description,
  icon,
  status,
  domainId,
  className,
}) => {
  const getStatusIcon = () => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "in-progress":
        return <Clock className="h-4 w-4 text-amber-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case "completed":
        return (
          <Badge variant="secondary" className="bg-green-100 text-green-700 border-green-200">
            Completed
          </Badge>
        );
      case "in-progress":
        return (
          <Badge variant="secondary" className="bg-amber-100 text-amber-700 border-amber-200">
            In Progress
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="text-gray-600">
            Not Started
          </Badge>
        );
    }
  };

  return (
    <Link href={`/assessment/${domainId}`} className="block">
      <Card 
        className={cn(
          "h-full transition-all duration-200 hover:shadow-md hover:scale-[1.02] cursor-pointer group",
          status === "completed" && "border-green-200 bg-green-50/50",
          className
        )}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className="text-2xl">{icon}</div>
              <div className="flex-1">
                <CardTitle className="text-base font-semibold group-hover:text-primary transition-colors">
                  {title}
                </CardTitle>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon()}
              <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors" />
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <CardDescription className="text-sm text-gray-600 mb-3">
            {description}
          </CardDescription>
          <div className="flex justify-between items-center">
            {getStatusBadge()}
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export default DomainCardLink;
